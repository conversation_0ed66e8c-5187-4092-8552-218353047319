import React, { useState, useEffect, useContext } from 'react';
import { ThemeContext } from '@/components/Context/ThemeContext';
import AdvancedProjectBlueprint from './AdvancedProjectBlueprint';

const ProjectSelectionModal = ({
  isOpen,
  onClose,
  onStartImplementation,
  initialBlueprint,
  frameworkOptions = [],
  isImplementing = false
}) => {
  const { isDarkMode } = useContext(ThemeContext) || { isDarkMode: false };

  const [state, setState] = useState({
    currentStep: 'blueprint',
    selectedTemplate: null,
    projectBlueprint: null,
    customRequirements: ''
  });


  useEffect(() => {
    if (isOpen && initialBlueprint) {
      setState(prev => ({
        ...prev,
        currentStep: 'blueprint',
        projectBlueprint: initialBlueprint
      }));
    }
  }, [isOpen, initialBlueprint]);

  const handleBlueprintUpdate = (blueprint) => {
    // Prevent unnecessary updates that can cause loops
    if (JSON.stringify(blueprint) === JSON.stringify(state.projectBlueprint)) {
      return; // Skip update if nothing has changed
    }
    
    setState(prev => ({
      ...prev,
      projectBlueprint: blueprint
    }));
  };

  const handleStartImplementation = () => {
    
    onStartImplementation(state);
  };

  // Reset state when modal is closed
  const initialRender = React.useRef(true);
  React.useEffect(() => {
    if (initialRender.current) {
      initialRender.current = false;
      return;
    }
    
    if (!isOpen) {
      setState({
        currentStep: 'blueprint',
        selectedTemplate: null,
        projectBlueprint: null,
        customRequirements: ''
      });
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[1000]">
      <div className="bg-white text-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-800">
                Get Ready to Build Your App
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                Review and customize your project settings below to match your vision. Once you&apos;re satisfied, start implementation and watch your app come to life.
              </p>
            </div>
            <button
              onClick={onClose}
              className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/50 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isImplementing}
              aria-label="Close modal"
              title="Close"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* This is where we'll add the extra field later */}
          <div className="mb-6">
            {/* Placeholder for extra field */}
          </div>

          <AdvancedProjectBlueprint
            onBlueprintUpdate={handleBlueprintUpdate}
            initialBlueprint={state.projectBlueprint || undefined}
            frameworkOptions={frameworkOptions}
            isDarkMode={isDarkMode}
          />
          
          <div className="flex justify-end mt-6">
            <button
              onClick={handleStartImplementation}
              disabled={isImplementing}
              className={`px-6 py-2 bg-primary text-white rounded-md ${isImplementing ? 'opacity-70 cursor-not-allowed' : 'hover:bg-primary-600'} flex items-center`}
            >
              {isImplementing ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Starting session...
                </>
              ) : (
                'Start Session'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectSelectionModal; 