import { useState, useCallback, useRef, useEffect } from 'react';

const useAppetize = () => {
  const [appetizeState, setAppetizeState] = useState({
    isAppetizeUrl: false,
    sdkLoaded: false,
    client: null,
    session: null,
    debugLogs: [],
    isRecordingLogs: false,
    logError: null,
    connectionStatus: 'disconnected', // disconnected, connecting, connected, error
    isInitializing: false
  });

  const clientRef = useRef(null);
  const sessionRef = useRef(null);
  const initializationTimeoutRef = useRef(null);
  const isInitializingRef = useRef(false);

  // Debug log helper
  const addDebugLog = useCallback((message, level = 'info', raw = null) => {
    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      message,
      level,
      raw
    };


    setAppetizeState(prev => ({
      ...prev,
      debugLogs: [...prev.debugLogs.slice(-99), logEntry]
    }));
  }, []);

  // Check if URL is from Appetize
  const isAppetizeUrl = useCallback((url) => {
    if (!url || typeof url !== 'string') return false;
    return url.includes('appetize.io') || url.includes('app.appetize.io');
  }, []);

  // Load Appetize SDK dynamically
  const loadAppetizeSDK = useCallback(() => {
    return new Promise((resolve, reject) => {
      if (window.appetize) {
        resolve(window.appetize);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.appetize.io/embed.js';
      script.async = true;
      script.onload = () => {
        if (window.appetize) {
          resolve(window.appetize);
        } else {
          reject(new Error('Appetize SDK failed to load'));
        }
      };
      script.onerror = () => reject(new Error('Failed to load Appetize SDK'));
      document.head.appendChild(script);
    });
  }, []);

  // Enhance URL with debug parameters
  const ensureDebugUrl = useCallback((url) => {
    if (!url || !isAppetizeUrl(url)) return url;

    try {
      const urlObj = new URL(url);

      if (!urlObj.searchParams.has('debug')) {
        urlObj.searchParams.set('debug', 'true');
      }

      if (!urlObj.searchParams.has('deviceScale')) {
        urlObj.searchParams.set('deviceScale', '75');
      }

      return urlObj.toString();
    } catch (error) {
      console.error('Error parsing Appetize URL:', error);
      return url;
    }
  }, [isAppetizeUrl]);

  // Setup session event listeners
  const setupSessionListeners = useCallback((session) => {
    addDebugLog(`Session started - Token: ${session.token}`, 'info');
    addDebugLog(`Device: ${session.device?.name || 'Unknown'}`, 'info');

    session.on('log', (logData) => {
      const message = logData.message || String(logData);
      const level = logData.level || 'info';
      addDebugLog(message, level, logData);
    });

    session.on('interaction', (interaction) => {
      const message = `User interaction: ${interaction.type || 'touch'} ${
        interaction.coordinates ? `at (${interaction.coordinates.x}, ${interaction.coordinates.y})` : ''
      }`;
      addDebugLog(message, 'interaction', interaction);
    });

    session.on('launch', (appInfo) => {
      addDebugLog(`App launched: ${appInfo?.bundleId || 'Unknown app'}`, 'info', appInfo);
    });

    session.on('install', (installInfo) => {
      addDebugLog(`App installed: ${installInfo?.bundleId || 'Unknown app'}`, 'info', installInfo);
    });

    session.on('network', (networkData) => {
      const message = `Network: ${networkData.method || 'GET'} ${networkData.url || 'unknown URL'}`;
      addDebugLog(message, 'network', networkData);
    });

    session.on('error', (error) => {
      const message = `Session Error: ${error.message || error}`;
      addDebugLog(message, 'error', error);
    });

    session.on('end', () => {
      addDebugLog('Session ended', 'info');
      sessionRef.current = null;
      setAppetizeState(prev => ({ 
        ...prev, 
        session: null,
        isRecordingLogs: false
      }));
    });

  }, [addDebugLog]);

  // Initialize Appetize client
  const initializeAppetizeClient = useCallback(async (iframeElement) => {
    if (isInitializingRef.current || !appetizeState.isAppetizeUrl || !iframeElement) {
      return;
    }

    if (initializationTimeoutRef.current) {
      clearTimeout(initializationTimeoutRef.current);
    }

    isInitializingRef.current = true;

    try {
      addDebugLog('Starting Appetize client initialization...', 'info');
      
      setAppetizeState(prev => ({ 
        ...prev, 
        connectionStatus: 'connecting',
        logError: null,
        isInitializing: true
      }));

      // Load SDK if not already loaded
      if (!appetizeState.sdkLoaded) {
        addDebugLog('Loading Appetize SDK...', 'info');
        await loadAppetizeSDK();
        setAppetizeState(prev => ({ ...prev, sdkLoaded: true }));
        addDebugLog('Appetize SDK loaded successfully', 'info');
      }

      addDebugLog('Waiting for iframe to load...', 'info');
      await new Promise(resolve => setTimeout(resolve, 4000));

      const iframeId = 'appetize-preview-iframe';
      iframeElement.id = iframeId;

      addDebugLog(`Getting Appetize client for iframe: ${iframeId}`, 'info');
      
      const client = await Promise.race([
        window.appetize.getClient(`#${iframeId}`),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Client initialization timeout')), 20000)
        )
      ]);

      clientRef.current = client;
      addDebugLog('Appetize client successfully initialized', 'info');

      setAppetizeState(prev => ({ 
        ...prev, 
        client,
        connectionStatus: 'connected',
        logError: null,
        isInitializing: false
      }));

      client.on('session', (session) => {
        sessionRef.current = session;
        setAppetizeState(prev => ({ 
          ...prev,
          session,
          isRecordingLogs: true
        }));

        // Setup all session event listeners
        setupSessionListeners(session);
      });

      // Listen for client errors
      client.on('error', (error) => {
        const message = `Client Error: ${error.message || error}`;
        addDebugLog(message, 'error', error);
        setAppetizeState(prev => ({ 
          ...prev, 
          connectionStatus: 'error',
          logError: message
        }));
      });

      // Check if there's already an active session
      if (client.session) {
        addDebugLog('Found existing session', 'info');
        sessionRef.current = client.session;
        setAppetizeState(prev => ({ 
          ...prev,
          session: client.session,
          isRecordingLogs: true
        }));
        setupSessionListeners(client.session);
      } else {
        addDebugLog('No active session found. Waiting for user to start a session...', 'info');
        addDebugLog('Tip: Click on the device screen to start interacting', 'info');
      }

      // Try to automatically start a session if none exists
      setTimeout(async () => {
        if (!sessionRef.current && clientRef.current) {
          try {
            addDebugLog('Attempting to start a new session...', 'info');
            const newSession = await clientRef.current.startSession();
            if (newSession) {
              addDebugLog('New session started successfully', 'info');
            }
          } catch (error) {
            addDebugLog(`Failed to auto-start session: ${error.message}`, 'warn');
            addDebugLog('Session will start when you interact with the device', 'info');
          }
        }
      }, 2000);

    } catch (error) {
      console.error('Failed to initialize Appetize client:', error);
      
      let errorMessage = error.message || 'Unknown error';

      if (errorMessage.includes('timeout') || errorMessage.includes('Timed out')) {
        errorMessage = 'Connection timeout. The Appetize session may take longer to load.';
      } else if (errorMessage.includes('not found') || errorMessage.includes('selector')) {
        errorMessage = 'Cannot connect to Appetize. Please refresh and try again.';
      }

      addDebugLog(errorMessage, 'error');
      setAppetizeState(prev => ({ 
        ...prev, 
        connectionStatus: 'error',
        logError: errorMessage,
        isInitializing: false
      }));
    } finally {
      isInitializingRef.current = false;
    }
  }, [appetizeState.isAppetizeUrl, appetizeState.sdkLoaded, loadAppetizeSDK, setupSessionListeners, addDebugLog]);

  // Clear logs
  const clearLogs = useCallback(() => {
    setAppetizeState(prev => ({ 
      ...prev, 
      debugLogs: []
    }));
  }, []);

  // Reset Appetize state
  const resetAppetizeState = useCallback(() => {
    // Clear timeout
    if (initializationTimeoutRef.current) {
      clearTimeout(initializationTimeoutRef.current);
    }
    
    // End session
    if (sessionRef.current) {
      try {
        sessionRef.current.end();
      } catch (error) {
        console.warn('Error ending Appetize session:', error);
      }
    }
    
    // Reset refs
    clientRef.current = null;
    sessionRef.current = null;
    isInitializingRef.current = false;
    
    setAppetizeState(prev => ({
      isAppetizeUrl: false,
      sdkLoaded: prev.sdkLoaded, // Keep SDK loaded
      client: null,
      session: null,
      debugLogs: [],
      isRecordingLogs: false,
      logError: null,
      connectionStatus: 'disconnected',
      isInitializing: false
    }));
  }, []);

  // Update Appetize URL detection
  const updateUrl = useCallback((url) => {
    const isAppetize = isAppetizeUrl(url);
    
    setAppetizeState(prev => ({ 
      ...prev, 
      isAppetizeUrl: isAppetize
    }));

    if (!isAppetize) {
      resetAppetizeState();
    }
  }, [isAppetizeUrl, resetAppetizeState]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      resetAppetizeState();
    };
  }, [resetAppetizeState]);

  return {
    appetizeState,
    isAppetizeUrl,
    ensureDebugUrl,
    initializeAppetizeClient,
    clearLogs,
    resetAppetizeState,
    updateUrl
  };
};

export default useAppetize;