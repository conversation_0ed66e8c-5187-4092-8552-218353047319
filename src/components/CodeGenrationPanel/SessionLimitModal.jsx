import React, { useState, useEffect, useCallback, useRef } from 'react';
import { AlertTriangle, Loader2 } from 'lucide-react';

const SessionLimitModal = ({ 
  isVisible, 
  onMergeAndClose, 
  countdownSeconds = 3,
  sessionInputValue,
  messageType = 'pause' // 'pause' or 'error'
}) => {
  const [countdown, setCountdown] = useState(countdownSeconds);
  const [isProcessing, setIsProcessing] = useState(false);
  const countdownIntervalRef = useRef(null);
  const isComponentMountedRef = useRef(true);
  const hasTriggeredRef = useRef(false);

  // Auto-start countdown and merge process when modal becomes visible
  useEffect(() => {
    if (isVisible && !hasTriggeredRef.current) {
      hasTriggeredRef.current = true;
      setCountdown(countdownSeconds);
      
      countdownIntervalRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            // Auto-trigger merge when countdown reaches 0
            if (isComponentMountedRef.current) {
              handleMergeAndClose();
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, [isVisible, countdownSeconds]);

  // Reset when modal closes
  useEffect(() => {
    if (!isVisible) {
      hasTriggeredRef.current = false;
      setIsProcessing(false);
      setCountdown(countdownSeconds);
    }
  }, [isVisible, countdownSeconds]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isComponentMountedRef.current = false;
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, []);

  const handleMergeAndClose = useCallback(async () => {
    if (isProcessing) return;
    
    setIsProcessing(true);
    
    // Clear countdown interval
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
    }

    try {
      await onMergeAndClose();
    } catch (error) {
      console.error('Error during merge and close:', error);
    }
  }, [isProcessing, onMergeAndClose]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-[100]">
      {/* Backdrop overlay */}
      <div className="fixed inset-0 bg-gray-800 bg-opacity-75 backdrop-blur-sm" />

      {/* Modal container */}
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 z-[70] relative">
        {/* Modal content */}
        <div className="space-y-5">
          {/* Warning Icon */}
          <div className="flex justify-center mb-4">
            <div className="bg-orange-100 rounded-full p-3">
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </div>

          {/* Title and description */}
          <div className="text-center">
            {!isProcessing ? (
              <>
                <h3 className="text-lg font-semibold text-gray-900">Session Limit Reached</h3>
                <p className="mt-2 text-sm text-gray-500">
                  Your session has reached its usage limit. Saving your work and closing the session.
                </p>
                <div className="mt-4">
                  <div className="text-4xl font-bold text-orange-600">
                    {countdown}
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    {countdown === 1 ? 'second remaining' : 'seconds remaining'}
                  </div>
                </div>
              </>
            ) : (
              <>
                <h3 className="text-lg font-semibold text-gray-900">Saving Session</h3>
                <p className="mt-2 text-sm text-gray-500">
                  Please wait while we save your work and close the session...
                </p>
                <div className="flex justify-center mt-4">
                  <Loader2 className="h-8 w-8 animate-spin text-orange-600" />
                </div>
              </>
            )}
          </div>

          {/* Session name display - only show if not processing and has value */}
          {!isProcessing && sessionInputValue && (
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm text-gray-600 mb-1">Session will be saved as:</div>
              <div className="font-medium text-gray-900">{sessionInputValue}</div>
            </div>
          )}

          {/* Progress indicator for processing */}
          {isProcessing && (
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-orange-600 h-2 rounded-full animate-pulse" style={{ width: '100%' }}></div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SessionLimitModal;