"use client";
import React from "react";
import RequirementCard from "@/components/UIComponents/DetailCards/DetailCard";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { X } from "lucide-react";

const TaskDetailModal = ({ isOpen, onClose, taskData, projectId }) => {

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg w-3/4 max-w-4xl p-6 relative"
        onClick={(e) => e.stopPropagation()}
      >
        <DynamicButton
          variant="square"
          size="sqDefault"
          icon={X}
          onClick={onClose}
          className="absolute top-2 right-2"
        />
          <div>
            <div className="space-y-4 mt-8">
              <RequirementCard
                nodeDetails={taskData}
                onAssignUser={() => {}}
                updatePriority={() => {}}
                listAllUsers={[]}
                refresh={false}
                setRefresh={() => {}}
              />
            </div>
          </div>
      </div>
    </div>
  );
};

export default TaskDetailModal;