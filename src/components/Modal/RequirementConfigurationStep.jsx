"use client"

import { useState, useContext, useEffect , useRef} from 'react';
import { Upload, Info} from 'lucide-react';
import Logo from "../../../public/logo/kavia_logo.svg";
import Image from "next/image";
import ConfigureModal from "../Modal/ConfigureModel";
import { StateContext } from '../Context/StateContext';
import { ProjectSetupContext } from '../Context/ProjectSetupContext';

import { getTopLevelRequirements,deleteNodeById,fetchChildRequirements} from '@/utils/api';
import WorkItemTable from '../SimpleTable/WorkItemTable';

import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { AlertContext } from '../NotificationAlertService/AlertList';
import DeleteProjectModal from './DeleteProjectModal';
import en from "@/en.json"
import StatusPanel from "@/components/StatusPanel/StatusPanel";
import { ExecutionContext } from '../Context/ExecutionContext';

export default function RequirementConfigurationStep({ setIsRequirementConfig, setIsAutoConfiguration, autoConfigItems }) {
  const [configMethod, setConfigMethod] = useState('discussion'); // 'discussion' or 'auto'
  const { projectId, openFlowModel ,setOpenFlowModel,showConfigModel,
    setShowConfigModel } = useContext(ProjectSetupContext);
  const [configureModel, setConfigureModel] = useState(false);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const [requirementRootId,setRequirementRootId] = useState(null)
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [checkAll, setCheckAll] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [expandedRows, setExpandedRows] = useState([]);
  const [isOpenDropdownOn, setIsOpenDropdownOn] = useState(null);
  const [requirementDetails,setRequirementDetails] = useState([])
  const {showAlert} = useContext(AlertContext)
  const [loading,setLoading] = useState(true)
  const [isVisible, setIsVisible] = useState(true);
   const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
     const [deleteParams, setDeleteParams] = useState({
        nodeId: null,
        nodeType: null,
      });


  // Filtered data from requirements
  const [filteredData, setFilteredData] = useState([]);
    const [sortConfig, setSortConfig] = useState({
      key: "id",
      direction: "ascending",
    });
    const {configStatus,currentTaskId,setAutoNavigateEnabled} = useContext(ExecutionContext)
    const [taskStatus, setTaskStatus] = useState("Idle");
  const [childLoading, setChildLoading] = useState(null);
  const dropdownRef = useRef(null);

    const typeBadgeColors = {
        Epic: "bg-semantic-purple-100 text-semantic-purple-700 max-w-14",
        UserStory: "bg-semantic-green-100 text-semantic-green-700 max-w-24",
        Task: "bg-primary-100 text-primary-700 max-w-14",
        default: "bg-custom-bg-primary max-w-20",
      };

    const flag = sessionStorage.getItem(`openRequirementContent-${projectId}`);



  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(()=>{
    const getRequirements = async ()=>{
        try{
            const topLevelRequirements = await getTopLevelRequirements(projectId)
            setRequirementRootId(topLevelRequirements[0].requirement_root_id)
            setRequirementDetails(topLevelRequirements)

        }catch(error){
            }finally {
          setLoading(false);
        }
    }

    getRequirements()
  },[projectId,searchParams])

  const handleCloseModal = () => {
    setConfigureModel(false);
  };

const handleConfigureClick = () => {
  setConfigMethod("auto");
  setConfigureModel(true);
};
   // Handle clicks outside the dropdown

   useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpenDropdownOn(null);
      }

    }
    // Add event listener when dropdown is open

    if (isOpenDropdownOn !== null) {

      document.addEventListener('mousedown', handleClickOutside);

    }
    // Clean up event listener

    return () => {

      document.removeEventListener('mousedown', handleClickOutside);

    };

  }, [isOpenDropdownOn]);
  useEffect(() => {
  const hasRealData = requirementDetails.some(
    item => item.title || item.type || item.status || item.assignee_name
  );

  if (hasRealData) {
    setIsRequirementConfig(true);
  } else {
    setIsRequirementConfig(false);
  }
}, [requirementDetails]);

  useEffect(() => {
  const fetchRequirementsOnComplete = async () => {
    if (taskStatus.toLowerCase() === "complete" && projectId) {
      try {
        // Fetch the latest requirements data when task is complete
        const topLevelRequirements = await getTopLevelRequirements(projectId);
        setRequirementRootId(topLevelRequirements[0].requirement_root_id);
        setRequirementDetails(topLevelRequirements);
        

      } catch (error) {
      } finally {
        setLoading(false);
      }
    }
  };

  fetchRequirementsOnComplete();
}, [taskStatus, projectId, showAlert]);

  const LoadingSpinner = () => (
    <div className="w-full animate-pulse rounded-lg overflow-hidden border border-gray-200">
    {/* Table header */}
    <div className="flex items-center p-4 border-b bg-gray-50 rounded-t-lg">
      <div className="w-5 h-5 bg-gray-200 rounded mr-3"></div>
      <div className="flex-1 grid grid-cols-5 gap-4">
        <div className="h-6 bg-gray-200 rounded col-span-2"></div>
        <div className="h-6 bg-gray-200 rounded"></div>
        <div className="h-6 bg-gray-200 rounded"></div>
        <div className="h-6 bg-gray-200 rounded"></div>
      </div>
    </div>

    {/* Table rows */}
    {[...Array(3)].map((_, i) => (
      <div key={i} className="flex items-center p-4 border-b hover:bg-gray-50">
        <div className="w-5 h-5 bg-gray-200 rounded mr-3"></div>
        <div className="flex-1 grid grid-cols-5 gap-4">
          <div className="col-span-2">
            <div className="h-5 bg-gray-200 rounded mb-2 w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div className="flex items-center">
            <div className="h-6 w-16 bg-gray-200 rounded"></div>
          </div>
          <div className="h-5 bg-gray-200 rounded w-1/2"></div>
          <div className="h-5 bg-gray-200 rounded w-1/4"></div>
        </div>
      </div>
    ))}

    {/* Table footer / pagination */}
    <div className="flex justify-between items-center p-4 bg-gray-50 rounded-b-lg">
      <div className="h-8 w-32 bg-gray-200 rounded"></div>
      <div className="flex space-x-2">
        <div className="h-8 w-8 bg-gray-200 rounded"></div>
        <div className="h-8 w-8 bg-gray-200 rounded"></div>
        <div className="h-8 w-8 bg-gray-200 rounded"></div>
      </div>
    </div>
  </div>
  );

  const InformationBadge = ()=>{
    return (
      <div className="w-full mx-auto my-4">
        <div className="bg-gradient-to-r from-primary-100 to-indigo-100 border-l-4 border-primary p-5 rounded-lg shadow-lg flex items-start gap-4">
          <div className="flex-shrink-0 bg-primary-100 p-2 rounded-full">
            <Info className="h-5 w-5 text-primary" />
          </div>
          <div className="flex-1">
            <h4 className="font-weight-medium text-primary-800 mb-1">Mandatory Configuration</h4>
            <p className="typography-body-sm text-primary-700">
            Defining your project’s requirements is a mandatory step and must be completed before you can proceed.
              <br />
            </p>
          </div>
          <button
            onClick={() => setIsVisible(false)}
            className="mt-1 text-primary-400 hover:text-primary transition-colors focus:outline-none"
            aria-label="Dismiss"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    );

  }



  useEffect(()=>{
    if (configStatus[currentTaskId]) {

      setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
      setAutoNavigateEnabled(false)

    }
  },[currentTaskId, configStatus[currentTaskId],projectId])


  useEffect(() => {
    if (requirementDetails && requirementDetails.length > 0) {
      // Process requirements data
      const processedData = requirementDetails.map(item => ({
        ...item,
        key: item.id,
        checked: false,
      }));

      // Apply sorting if needed
      const sortedData = sortData(processedData, sortConfig);
      setFilteredData(sortedData);
    }
  }, [requirementDetails, sortConfig,projectId]);

  const requestSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }

    setSortConfig({ key, direction });
  };

  const sortData = (data, config) => {
    if (!config.key) return data;

    return [...data].sort((a, b) => {
      if (a[config.key] < b[config.key]) {
        return config.direction === "ascending" ? -1 : 1;
      }
      if (a[config.key] > b[config.key]) {
        return config.direction === "ascending" ? 1 : -1;
      }
      return 0;
    });
  };



  const handleSelectAll = (e) => {
    const isChecked = e.target.checked;
    setCheckAll(isChecked);

    const updatedData = filteredData.map(item => ({
      ...item,
      checked: isChecked
    }));

    setFilteredData(updatedData);
    setSelectedItems(isChecked ? updatedData.map(item => item.id) : []);
  };
const handleExpandRow = async (itemId, level = 0) => {
    const isExpanded = expandedRows.some(
      (row) => row.id === itemId && row.level === level
    );

    if (isExpanded) {
      // Collapse: Remove this row and all its children from expanded rows
      setExpandedRows(prev => prev.filter(
        (row) => !(row.id === itemId && row.level === level)
      ));
    } else {
      // Expand: Add to expanded rows and fetch child data
      setExpandedRows(prev => [...prev, { id: itemId, level }]);
      setChildLoading(itemId);
      
      try {
        const childData = await fetchChildRequirements(itemId, "Requirement");
        
        const addChildData = (items) => {
          return items.map((item) => {
            if (item.id === itemId) {
              return {
                ...item,
                subItems: childData.length ? childData.map(child => ({
                  ...child,
                  key: child.id,
                  checked: false,
                  subItems: [],
                  level: level + 1,
                })) : [
                  {
                    id: `${itemId}-empty`,
                    title: "No child work items available",
                    status: "",
                    assignee_name: "",
                    type: "",
                    subItems: [],
                    level: level + 1,
                    isEmpty: true,
                  },
                ],
              };
            }
            if (item.subItems && item.subItems.length > 0) {
              return {
                ...item,
                subItems: addChildData(item.subItems)
              };
            }
            return item;
          });
        };

      setFilteredData(prevData => addChildData(prevData));
      } catch (error) {
        showAlert("Failed to load child requirements", "danger");
        
        // Remove the expansion state on error
        setExpandedRows(prev => prev.filter(
          (row) => !(row.id === itemId && row.level === level)
        ));
      } finally {
        setChildLoading(null);
      }
    }
  };
  const handleCheckboxChange = (id) => {
    setSelectedItems(prev => {
      if (prev.includes(id)) {
        return prev.filter(itemId => itemId !== id);
      }
      return [...prev, id];
    });

    setFilteredData(prev => {
      return prev.map(item => {
        if (item.id === id) {
          return { ...item, checked: !item.checked };
        }
        return item;
      });
    });
  };

  const handleItemClick = (type,id) => {
    // Navigate to Epic Configuration step with the selected requirement
  const newSearchParams = new URLSearchParams(searchParams);
  newSearchParams.set("stepName", "User Stories");
  newSearchParams.set("selectedEpicId", id);
  router.push(`${pathname}?${newSearchParams.toString()}`);


  };

  const handleViewPastDiscussion = () => {
    };

  const confirmAndDelete = (id,type) => {
    setDeleteParams({ id, type });
    setIsDeleteModalOpen(true);

  };
  const cancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  const handleDelete = async () => {
      const { id, type } = deleteParams;
      setIsDeleting(true);
      try {
        const nodeId = id;
        const node_type = type;

        const response = await deleteNodeById(nodeId, node_type);
        await getTopLevelRequirements(projectId).then((topLevelRequirements) => {
          setRequirementDetails(topLevelRequirements)
          setRequirementRootId(topLevelRequirements[0].requirement_root_id);
        });

        if (response.status >= 200 && response.status < 300) {
          showAlert("Requirement deleted successfully", "success");
        } else {
          throw new Error(response.statusText || "Unknown error");
        }
      } catch (error) {

        showAlert("Failed to delete the Requirement!", "danger");
      } finally {
        setIsDeleting(false);
        setIsDeleteModalOpen(false);
      }
    };


  const handleBtn = () => {
    showAlert("Functionality was not implemented", "info");

  };

   const { setIsVertCollapse } = useContext(StateContext);


 const updateProject = () => {
  setConfigMethod("discussion");

  const newSearchParams = new URLSearchParams(searchParams);
  newSearchParams.set("discussion", "new");
  newSearchParams.set("node_id", requirementRootId);
  newSearchParams.set("node_type", "RequirementRoot");
  newSearchParams.set("is_creating_Requirement", "true");

  router.push(`${pathname}?${newSearchParams.toString()}`);
};

  let Data = filteredData.filter(
    (item) =>
      (item.id != null) ||
      (item.title != null) ||
      (item.type != null) ||
      (item.status != null) ||
      (item.assignee_name != null)
  );

   const shouldShowStatusPanel = () => {
    if (!showConfigModel) return false;
    const hasOngoingTask = currentTaskId && configStatus[currentTaskId];
    
    // Only show status panel for auto configuration tasks
    if (hasOngoingTask) {
      const taskInProgress = taskStatus.toLowerCase() === 'in_progress' || taskStatus.toLowerCase() === 'idle';
      const isConfigured =requirementDetails.some(
        item => item.title || item.type || item.status || item.assignee_name
      );
      const taskNotComplete = taskStatus.toLowerCase() !== 'complete';
      
      return taskInProgress || (isConfigured && taskNotComplete);
    }
    
    // For interactive configuration, never show status panel
    return false;
  };

  return (
    <div className='h-full overflow-y-auto px-4"'>
       {shouldShowStatusPanel() ? (
      <StatusPanel />
    ) : (
      <>

        {configureModel && (
                <ConfigureModal
                id={projectId}
                type={"RequirementRoot"}
                isNodeType={"RequirementRoot"}
                isCreateProject={true}
                closeModal={handleCloseModal}
                setLoadingAutoConfigure={setLoadingAutoConfigure}
                setShowConfigModel={setShowConfigModel}
                onSubmitSuccess={() => {
                  showAlert(
                    `RequirementRoot configuration initiated successfully`,
                    "success"
                  );
                  setIsVertCollapse(false);
                }}
                />
              )}

              {isDeleteModalOpen && (
                          <DeleteProjectModal
                            isOpen={isDeleteModalOpen}
                            onClose={cancelDelete}
                            onDelete={handleDelete}
                            isDeleting={isDeleting}
                            type="requirements"
                          />
                        )}

       {Data.length > 0 ? (
        <div ref={dropdownRef}>
          {loading ? (
            <LoadingSpinner />
          ):(autoConfigItems?.includes("project") &&
  !autoConfigItems?.includes("requirement") &&
  requirementDetails.every(
  item => !item.title && !item.type && !item.status && !item.assignee_name &&
  (!currentTaskId || !configStatus[currentTaskId] || taskStatus.toLowerCase() === 'complete')
) ) ? (
    <div className="flex-1 p-4 flex flex-col">
  {/* Configuration Method */}
  <div className="mb-6">
    <h3 className="typography-body-lg font-weight-medium mb-4">Configuration Method</h3>

    <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
      {/* Interactive configuration card */}
      <div
        className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md border-gray-200 hover:border-primary-200`}
        onClick={updateProject}
      >
        <div className="flex mb-4 items-center space-x-3">
          <div className="py-1 px-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
            <Image
              src={Logo}
              alt="Logo"
              width="16"
              height="16"
              className="text-primary"
            />
          </div>
          <h4 className="typography-body-lg font-weight-medium">Interactive configuration</h4>
        </div>

        <p className="text-gray-600">
          {en.RequirementUpdate}
        </p>
      </div>

      {/* Auto configuration card */}
      <div
        className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md border-gray-200 hover:border-primary-200`}
        onClick={handleConfigureClick}
      >
        <div className="flex mb-4 items-center space-x-3">
          <div className="p-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
            <Upload className="w-4 h-4 text-primary text-bold" />
          </div>
          <h4 className="typography-body-lg font-weight-medium">Auto Configuration</h4>
        </div>

        <p className="text-gray-600">
          {en.RequirementAutoConfig}
        </p>
      </div>
    </div>
  </div>
</div>
  ) : (

      <WorkItemTable
                data={filteredData}
                totalItems={filteredData.length}
                currentPage={currentPage}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
                onPageSizeChange={(newPageSize) => {
                  setPageSize(newPageSize);
                  setCurrentPage(1);
                }}
                checkAll={checkAll}
                handleSelectAll={handleSelectAll}
                requestSort={requestSort}
                sortConfig={sortConfig}
                handleExpandRow={handleExpandRow}
                expandedRows={expandedRows}
                handleItemClick={handleItemClick}
                handleCheckboxChange={handleCheckboxChange}
                selectedItems={selectedItems}
                typeBadgeColors={typeBadgeColors}
                isOpenDropdownOn={isOpenDropdownOn}
                setIsOpenDropdownOn={setIsOpenDropdownOn}
                confirmAndDelete={confirmAndDelete}
                handleBtn={handleBtn}
                handleViewPastDiscussion={handleViewPastDiscussion}
              /> )}

     </div>

      ):(
      <>
        {/* {isVisible && (
           <InformationBadge />
        )} */}
         {/* Content */}
         <div className="flex-1 p-4 flex flex-col">

         {/* Configuration Method */}
         <div className="mb-6">
           <h3 className="typography-body-lg font-weight-medium mb-4">Configuration Method</h3>

           <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
             {/* Update via discussion card */}
             <div
               className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${
                 configMethod === 'discussion' ? 'border-primary bg-primary-50' : 'border-gray-200'
               }`}
               onClick={updateProject}
             >
               <div className="flex mb-4 items-center space-x-2">
                 <div className="py-1 px-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                                 <Image
                                     src={Logo}
                                     alt="Logo"
                                     width={16}
                                     height={16}
                                     className="text-primary"
                                 />

                 </div>
                 <h4 className="typography-body-lg font-weight-medium ">Interactive configuration</h4>
               </div>

               <p className="text-gray-600">
              {en.RequirementUpdate}

               </p>
             </div>

             {/* Auto Configuration card */}
             <div
               className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${
                 configMethod === 'auto' ? 'border-primary bg-primary-50' : 'border-gray-200'
               }`}
               onClick={handleConfigureClick}
             >
               <div className="flex mb-4 items-center space-x-2">
                 <div className="p-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                   <Upload className="w-4 h-4  text-primary text-bold" />
                 </div>
                 <h4 className="typography-body-lg font-weight-medium ">Auto Configuration</h4>
               </div>

               <p className="text-gray-600">
               {en.RequirementAutoConfig}

               </p>
             </div>
           </div>
         </div>
       </div>
       </>
      )}
    </>)}
    </div>
  );
}