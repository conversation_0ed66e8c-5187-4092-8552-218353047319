import { useState, useEffect, useContext } from "react";
import { Upload, Info, CheckCircle, ArrowRight, ArrowLeft } from "lucide-react";
import {
  getTopLevelRequirements,
  fetchNodeById,
  createProjectGuidanceFlow,
  fetchChildRequirements,

} from "@/utils/api";
import { FaForward } from "react-icons/fa";
import ConfigureModal from "../Modal/ConfigureModel";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { AlertContext } from "../NotificationAlertService/AlertList";
import Logo from "../../../public/logo/kavia_logo.svg";
import Image from "next/image";
import { ProjectSetupContext } from "../Context/ProjectSetupContext";
import EmptyStateView from "./EmptyStateModal";
import TableComponent from "../SimpleTable/table";
import { getRelatedNodes } from "@/utils/nodeRoute";
import en from "@/en.json"
import StatusPanel from "@/components/StatusPanel/StatusPanel";
import { ExecutionContext } from "../Context/ExecutionContext";

export default function EpicConfigurationStep({ handleSkipAllEpics }) {
  const [epics, setEpics] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { projectId, showConfigModel, setShowConfigModel } = useContext(ProjectSetupContext);
  const [configMethod, setConfigMethod] = useState("discussion");
  const [configureModel, setConfigureModel] = useState(false);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const [skippedEpics, setSkippedEpics] = useState([]);
  const [showSkipNotification, setShowSkipNotification] = useState(false);
  const [showAllSkippedNotification, setShowAllSkippedNotification] = useState(false);
  const [epicCount, setEpicCount] = useState(null);
  const [epicId, setEpicId] = useState(null);
  const [epicType, setEpicType] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userStories, setUserStories] = useState([]);
  const [viewingUserStories, setViewingUserStories] = useState(false);
  const [userStoryIndex, setUserStoryIndex] = useState(0);
  const [loadingUserStories, setLoadingUserStories] = useState(false);
  const [testCase, setTestCase] = useState([])
  const [showUserStorySkipNotification, setShowUserStorySkipNotification] = useState(false);
  const [skippedUserStories, setSkippedUserStories] = useState([]);
  const [metadata, setMetaData] = useState([])
  const [UserStoryDetailVal, setUserStoryDetail] = useState([])
  const { configStatus, currentTaskId, setAutoNavigateEnabled } = useContext(ExecutionContext)
  const [taskStatus, setTaskStatus] = useState("Idle");
  const [rowClick, setRowClick] = useState(false)
  const [isOpen, setIsOpen] = useState(false);
  const [loadingTest,setLoadTest] = useState(false)

  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);


  const searchParams = useSearchParams();
  const router = useRouter();
  const { showAlert } = useContext(AlertContext);
  const pathname = usePathname();
  const selectedEpicId = searchParams.get("selectedEpicId");

  // Track URL parameters
  useEffect(() => {
    const hasDiscussionParam = searchParams.has("discussion");
    const isCreatingEpic = searchParams.has("is_creating_Epic");
    const isCreatingUserStory = searchParams.has("is_creating_UserStory");
    const nodeId = searchParams.get("node_id");
    const nodeType = searchParams.get("node_type");

    if (nodeId && nodeType === "Epic") {
      setEpicId(nodeId);
      setEpicType(nodeType);
    }

    // If the discussion parameter was previously present but now removed
    if (!hasDiscussionParam && !isCreatingEpic && epicId) {
      // Fetch the updated node data
      const fetchUpdatedEpic = async () => {
        try {
          const updatedEpic = await fetchNodeById(epicId, epicType);


          if (updatedEpic) {
            // Store flag to indicate epic content has been opened
            sessionStorage.setItem(
              `openEpicContent-${projectId}-${epicId}`,
              "true"
            );

            // If configuration_state is "configured", update MongoDB
            if (
              updatedEpic.properties &&
              updatedEpic.properties.configuration_state === "configured"
            ) {
              try {
                // Send just the minimal data needed
                const minimalData = {
                  id: epicId,
                  type: "Epic",
                  status: "configured",
                };

                const result = await createProjectGuidanceFlow(
                  parseInt(projectId),
                  {
                    project_id: parseInt(projectId),
                    step_name: "epic_configuration",
                    status: "completed",
                    data: {
                      epic_id: parseInt(epicId),
                      type: "Epic",
                      status: "configured",
                    },
                  }
                );


                // Update epics list
                fetchTestCase()
                fetchEpics();
              } catch (error) {

              }
            }
          }
        } catch (error) {

        }
      };

      fetchUpdatedEpic();
      fetchTestCase()
      fetchEpics()
    }

    if (!hasDiscussionParam && !isCreatingUserStory && nodeType === "UserStory" && viewingUserStories) {
      fetchTestCase()
    }
  }, [searchParams, epicId, epicType, projectId,viewingUserStories]);

  const fetchEpics = async () => {
    setIsLoading(true);
    try {
      const data = await getTopLevelRequirements(projectId);
      const storedSkips = JSON.parse(
        sessionStorage.getItem("skippedEpics") || "[]"
      );
      const filteredEpics = (data || []).filter(
        (epic) => !storedSkips.includes(epic.id)
      );
      setSkippedEpics(storedSkips);
      setEpics(filteredEpics);
      setEpicCount(data.length);

      const allSkipped = sessionStorage.getItem(`allEpicsSkipped-${projectId}`);

      if (allSkipped === "true" && filteredEpics.length === 0) {
        setShowAllSkippedNotification(true);
      }
    } catch (error) {

    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user stories for the current epic
  const fetchUserStories = async (epicId) => {
    if (!epicId) return;

    setLoadingUserStories(true);
    try {
      const response = await fetchChildRequirements(epicId, "Requirement");




      if (response && Array.isArray(response)) {
        // Load skipped user stories from session storage
        const skippedStoriesKey = `skippedUserStories-${projectId}-${epicId}`;
        const storedSkippedStories = JSON.parse(
          sessionStorage.getItem(skippedStoriesKey) || "[]"
        );

        setSkippedUserStories(storedSkippedStories);

        // Filter out skipped user stories
        const filteredUserStories = response.filter(
          story => !storedSkippedStories.includes(story.id)
        );

        setUserStories(filteredUserStories);
        if (!rowClick) {

          setUserStoryIndex(0)

        }

      } else {
        setUserStories([]);
      }
    } catch (error) {

      showAlert("Failed to load user stories", "error");
      setUserStories([]);
    } finally {
      setLoadingUserStories(false);
    }
  };

  useEffect(() => {
    fetchEpics();
  }, [projectId]);

  useEffect(() => {
    if (configStatus[currentTaskId]) {

      setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
      setAutoNavigateEnabled(false)

      const progress = configStatus[currentTaskId].progress;
      const status = configStatus[currentTaskId].task_status;

      if (progress >= 99 || status === "Complete") {
        fetchEpics();
      }

    }
  }, [currentTaskId, configStatus[currentTaskId], projectId])



  const handleSkipAll = () => {
    if (epics.length === 0) return;

    const allIdsToSkip = epics.map((epic) => epic.id);
    const updatedSkipped = [...skippedEpics, ...allIdsToSkip];
    sessionStorage.setItem("skippedEpics", JSON.stringify(updatedSkipped));
    sessionStorage.setItem(`allEpicsSkipped-${projectId}`, "true");
    setSkippedEpics(updatedSkipped);
    setShowAllSkippedNotification(true);

    setCurrentIndex(epics.length - 1);
    setTimeout(() => {
      setEpics([]);
      setCurrentIndex(0);
    }, 50);

    // Log skipped epics to MongoDB
    try {
      createProjectGuidanceFlow(parseInt(projectId), {
        project_id: parseInt(projectId),
        step_name: "epics_skipped",
        status: "completed",
        data: {
          skipped_epics: allIdsToSkip.map((id) => parseInt(id)),
          type: "Epic",
          status: "skipped",
        },
      })
        .then((result) => {

        })
        .catch((error) => {

        });
    } catch (error) {

    }
  };

  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  const handleConfigureClick = () => {
    setConfigMethod("auto");
    setConfigureModel(true);
  };

  const handleUpdateEpics = (id) => {
    setConfigMethod("discussion");
    if (!id) {
      return null;
    }

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", id);
    newSearchParams.set("node_type", "Epic");
    newSearchParams.set("is_creating_Epic", "true");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleUpdateUserStory = (id) => {
    setConfigMethod("discussion");
    if (!id) {
      return null;
    }

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", id);
    newSearchParams.set("node_type", "UserStory");
    newSearchParams.set("discussion_type", "testcase_generation");
    newSearchParams.set("is_creating_UserStory", "true");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };


  const currentEpic = epics[currentIndex];
  const currentUserStory = userStories[userStoryIndex];

  const flag = currentEpic
    ? sessionStorage.getItem(`openEpicContent-${projectId}-${currentEpic.id}`)
    : null;
  const isEpicContentOpen = flag === "true";

  const fetchTestCase = async () => {
    setLoadTest(true)
    try {
      // const nodeDetails = await fetchNodeById(currentUserStory.id, "UserStory")
      const testCase = await getRelatedNodes(currentUserStory.id, "UserStory", "VERIFIES");

      setTestCase(testCase)
      setMetaData(nodeDetails?.ui_metadata)
      setUserStoryDetail(nodeDetails?.properties)
    } catch (error) {
    }finally{
      setLoadTest(false)
    }
  }

  // Fetch user stories when epic changes or when viewing user stories is toggled
  useEffect(() => {

    if (currentEpic) {
      fetchUserStories(currentEpic.id);
    }
  }, [currentIndex, epics, viewingUserStories, projectId, currentEpic,]);


  useEffect(() => {
  
    fetchTestCase()
    
  }, [projectId, currentUserStory?.id,currentIndex, epics,  userStories, viewingUserStories, userStoryIndex,searchParams])

  useEffect(() => {
    if (selectedEpicId && epics.length > 0) {
      // Find the index of the selected epic
      const index = epics.findIndex(epic => epic.id.toString() === selectedEpicId);
      if (index !== -1) {
        setCurrentIndex(index);

        // If we have user stories for this epic, go ahead and fetch them
        if (epics[index]) {
          fetchUserStories(epics[index].id);
        }
      }
    }
  }, [selectedEpicId, epics]);

  const handleSkip = () => {
    if (!epics[currentIndex]) return;

    // Reset user story view when skipping
    setViewingUserStories(false);

    if (isEpicContentOpen) {
      // Just move to the next epic when flag is true
      setCurrentIndex((prev) => Math.min(prev + 1, epics.length - 1));
    } else {
      const epicId = epics[currentIndex].id;
      const updatedSkipped = [...skippedEpics, epicId];
      sessionStorage.setItem("skippedEpics", JSON.stringify(updatedSkipped));
      setSkippedEpics(updatedSkipped);
      setShowSkipNotification(true);

      // Log the skipped epic to MongoDB
      try {
        createProjectGuidanceFlow(parseInt(projectId), {
          project_id: parseInt(projectId),
          step_name: "epic_skipped",
          status: "completed",
          data: {
            epic_id: parseInt(epicId),
            type: "Epic",
            status: "skipped",
          },
        })
          .then((result) => {

          })
          .catch((error) => {

          });
      } catch (error) {

      }

      setTimeout(() => {
        const newEpics = epics.filter((epic) => epic.id !== epicId);
        setEpics(newEpics);
        setCurrentIndex((prev) => Math.min(prev, newEpics.length - 1));
        setShowSkipNotification(false);
      }, 5000);
    }
  };

  const toggleUserStoryView = () => {
    if (!viewingUserStories) {
      fetchUserStories(currentEpic.id);
    }
    setViewingUserStories(!viewingUserStories);
  };

  const handleNextUserStory = () => {
    if (userStoryIndex < userStories.length - 1) {
      setUserStoryIndex(userStoryIndex + 1);
    }
  };

  // Navigate to previous user story
  const handlePreviousUserStory = () => {
    if (userStoryIndex > 0) {
      setUserStoryIndex(userStoryIndex - 1);
    }
  };

  function EpicSkippedNotification() {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <div className="border border-cyan-200 bg-cyan-50 rounded-lg p-4 flex items-start gap-3">
          <div className="text-gray-500">
            <Info size={24} />
          </div>
          <div>
            <p className="text-gray-800 font-weight-medium">
              Epic skipped: You can create user stories for "
              {currentEpic?.title || "this epic"}" later in the workflow
              manager.
            </p>
          </div>
        </div>
      </div>
    );
  }

  function AllEpicsSkippedNotification() {
    return (
      <div className="border-l-4 border-green-500 bg-green-50 p-4 mb-8 flex items-start gap-3">
        <div className="text-gray-500">
          <Info size={24} />
        </div>
        <div>
          <p className="text-gray-800">
            All epics skipped: You can create user stories for all epics later
            in the workflow manager.
          </p>
        </div>
      </div>
    );
  }

  function UserStorySkippedNotification() {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <div className="border border-cyan-200 bg-cyan-50 rounded-lg p-4 flex items-start gap-3">
          <div className="text-gray-500">
            <Info size={24} />
          </div>
          <div>
            <p className="text-gray-800 font-weight-medium">
              User Story skipped: You can configure "
              {currentUserStory?.title || currentUserStory?.properties?.Title || "this user story"}"
              later in the workflow manager.
            </p>
          </div>
        </div>
      </div>
    );
  }



  function UserStoriesTable() {
    // Headers for user stories table
    const tableHeaders = [
      { key: 'id', label: 'ID' },
      { key: 'title', label: 'Title' },
      { key: 'type', label: 'Type' },
      { key: 'Priority', label: 'Priority' },

    ];

    // Format user stories data for the table
    const tableData = userStories.map(story => ({
      id: story.id,
      title: story.title || story.properties?.Title || 'Untitled Story',
      type: story.type || 'User Story',
      Priority: story.priority || story.properties?.Priority || 'Medium',

    }));

    return (
      <div className="mt-6">
        <h3 className="typography-body-lg font-weight-medium text-gray-800 mb-4">
          User Stories
        </h3>

        {userStories.length > 0 ? (
          <TableComponent
            data={tableData}
            headers={tableHeaders}
            onRowClick={(id) => {
              // Set the userStoryIndex to display the selected user story
              const index = userStories.findIndex(story => story.id === id);

              if (index !== -1) {
                setUserStoryIndex(index);
                setViewingUserStories(true);
                setRowClick(true)
                fetchTestCase();
              }
            }}
            sortableColumns={{ id: true, title: true, priority: true, status: true }}
            itemsPerPage={5}
            title="User Stories"
          />
        ) : (
          <div className="text-center py-10 bg-gray-50 rounded-lg border border-gray-200">
            <p className="text-gray-600">No user stories found for this epic.</p>
          </div>
        )}
      </div>
    );
  }

  const tableTestCaseHeaders = [
    { key: 'id', label: 'ID' },
    { key: 'title', label: 'Title' },
    { key: 'type', label: 'Type' },
    { key: 'description', label: 'Description' },
    { key: 'category', label: 'CATEGORY' },

  ];

  function UserStoryDetail() {
    if (!currentUserStory) {
      return (
        <div className="text-center py-10 bg-gray-50 rounded-lg border border-gray-200">
          <p className="text-gray-600">No user story selected or available.</p>
        </div>
      );
    }



    // Format user story data for display
    const storyData = {
      id: currentUserStory.id,
      title: currentUserStory.title || currentUserStory.properties?.Title || 'Untitled Story',
      description: currentUserStory.description || currentUserStory.properties?.Description || 'No description available',
      type: currentUserStory.type || 'User Story',
      priority: currentUserStory.priority || currentUserStory.properties?.Priority || 'Medium',
      status: currentUserStory.status || currentUserStory.properties?.Status || 'New',
      createdAt: currentUserStory.createdAt || 'Unknown',
      modifiedAt: currentUserStory.modifiedAt || 'Unknown'
    };
    const tableData = testCase.length > 0
      ? testCase.map(story => ({
        id: story.id,
        title: story.title || story.properties?.Title || 'Untitled Story',
        type: story.type || story.properties?.Type || 'User Story',
        description: story.properties?.Description || '',
        category: story.properties?.Category || '',

      }))
      : [];

    return (
      <div className="mt-4">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h3 className="typography-body-lg font-weight-semibold text-gray-800">
              {storyData.title}
            </h3>
          </div>

          <div className="flex space-x-2">
            <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full typography-body-sm">
              {storyData.type}
            </span>
            <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full typography-body-sm">
              {storyData.priority}
            </span>
          </div>
        </div>
        {showUserStorySkipNotification ? (
          <UserStorySkippedNotification />
        ) : showConfigModel && (taskStatus.toLowerCase() === 'in_progress' || taskStatus.toLowerCase() === 'idle') ? (
          <StatusPanel />) :
          loadingTest ?(
            <LoadingSkeleton />
          ):
          testCase.length > 0 ? (
            <div>
              {/* <PropertiesRenderer
                  properties={UserStoryDetailVal}
                  metadata={metadata}
                  to_skip={["Type", "Title", "configuration_state",]}
                /> */}
              <TableComponent
                data={tableData}
                headers={tableTestCaseHeaders}
                onRowClick={() => { }}
                sortableColumns={{ id: true, title: true, priority: true, status: true }}
                itemsPerPage={5}
                title="Test Cases"
              />
            </div>
          ) : (
            <>
              <div className="mb-6">
                <h3 className="typography-body-lg font-weight-medium text-gray-700 mb-4">
                  Configure this User Story
                </h3>
              </div>

              {/* Buttons */}
              <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                <div
                  className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === "discussion"
                    ? "border-primary bg-primary-50"
                    : "border-gray-200"
                    }`}
                  onClick={() => handleUpdateUserStory(storyData.id)}
                >
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="py-1 px-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                      <Image
                        src={Logo}
                        alt="Logo"
                        width={16}
                        height={16}
                        className="text-primary"
                      />
                    </div>
                    <h4 className="typography-body-lg font-weight-medium">
                      Interactive configuration
                    </h4>
                  </div>

                  <p className="text-gray-600">
                    {en.UserStoryUpdate}
                  </p>
                </div>

                <div
                  className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === "auto"
                    ? "border-primary bg-primary-50"
                    : "border-gray-200"
                    }`}
                  onClick={handleConfigureClick}
                >
                  <div className="flex mb-4 items-center space-x-2">
                    <div className="p-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                      <Upload className="w-4 h-4  text-primary text-bold" />
                    </div>
                    <h4 className="typography-body-lg font-weight-medium">
                      Auto Configuration
                    </h4>
                  </div>

                  <p className="text-gray-600">
                    {en.UserStoryAutoConfig}
                  </p>
                </div>
              </div>

            </>

          )}



      </div>
    );
  }

  const handleSkipUserStory = () => {
    if (!userStories[userStoryIndex]) return;

    const storyId = userStories[userStoryIndex].id;
    const skippedStoriesKey = `skippedUserStories-${projectId}-${currentEpic.id}`;
    const skippedStories = JSON.parse(sessionStorage.getItem(skippedStoriesKey) || "[]");

    if (!skippedStories.includes(storyId)) {
      const updatedSkipped = [...skippedStories, storyId];
      sessionStorage.setItem(skippedStoriesKey, JSON.stringify(updatedSkipped));
      setSkippedUserStories(updatedSkipped);

      // Show notification instead of alert
      setShowUserStorySkipNotification(true);

      // Remove the user story after a delay
      setTimeout(() => {
        const newUserStories = userStories.filter(story => story.id !== storyId);
        setUserStories(newUserStories);

        // Adjust current index if needed
        if (userStoryIndex >= newUserStories.length) {
          setUserStoryIndex(Math.max(0, newUserStories.length - 1));
        }

        setShowUserStorySkipNotification(false);

        // If no user stories left, go back to epic view
        if (newUserStories.length === 0) {
          setViewingUserStories(false);
        }
      }, 5000);
    }
  };

  function LoadingSkeleton() {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 mb-8 animate-pulse">
        <div className="flex justify-between items-center mb-4">
          <div className="h-6 w-24 bg-gray-200 rounded"></div>
          <div className="h-8 w-20 bg-gray-200 rounded"></div>
        </div>
        <div className="h-8 w-3/4 bg-gray-200 rounded mb-4"></div>
        <div className="flex space-x-2 mb-6">
          <div className="h-6 w-16 bg-gray-200 rounded-full"></div>
          <div className="h-6 w-20 bg-gray-200 rounded-full"></div>
        </div>
        <div className="mb-6">
          <div className="h-6 w-1/2 bg-gray-200 rounded mb-4"></div>
          <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>
          <div className="h-4 w-5/6 bg-gray-200 rounded"></div>
        </div>
        <div className="flex flex-col md:flex-row gap-6 justify-center md:justify-start">
          <div className="w-[300px] h-[200px] bg-gray-100 rounded-lg p-6">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
              <div className="h-6 w-32 bg-gray-200 rounded"></div>
            </div>
            <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-4/6 bg-gray-200 rounded"></div>
          </div>
          <div className="w-[300px] h-[200px] bg-gray-100 rounded-lg p-6">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
              <div className="h-6 w-32 bg-gray-200 rounded"></div>
            </div>
            <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-4/6 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  function SkipAllModel() {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4" onClick={closeModal}>
        <div className="bg-white rounded-lg shadow-lg w-full max-w-md">

          {/* Header */}
          <div className="flex justify-between items-center p-4 border-b">
            <h2 className="typography-body-lg font-weight-semibold">Skip All Configuration</h2>
          </div>

          {/* Body */}
          <div className="p-4">
            <p className="text-gray-600 typography-body-sm">
              Are you sure you want to skip the current Epic and User Story configuration?

            </p>
          </div>
          <div className="flex items-start gap-2 bg-primary-50 border border-primary-200 text-primary-800 typography-body-sm rounded-md p-3">
            <svg className="w-5 h-5 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M12 20c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8z" />
            </svg>
            <p>
              You will proceed directly to the <strong>Architecture Requirements</strong> section. You can always create Epics and User Stories later under the Requirements section.
            </p>
          </div>


          {/* Footer */}
          <div className="flex justify-end gap-2 p-4 border-t">
            <button
              onClick={closeModal}
              className="px-4 py-2 typography-body text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={handleSkipAllEpics}
              className="px-4 py-2 typography-body bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              Skip All
            </button>
          </div>
        </div>
      </div>

    )
  }

  function UserStoryLoadingSkeleton() {
    return (
      <div className="animate-pulse">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="h-6 w-32 bg-gray-200 rounded-full"></div>
            <div className="h-8 w-48 bg-gray-200 rounded"></div>
          </div>
          <div className="flex space-x-2">
            <div className="h-6 w-20 bg-gray-200 rounded-full"></div>
            <div className="h-6 w-20 bg-gray-200 rounded-full"></div>
          </div>
        </div>
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="h-6 w-32 bg-gray-200 rounded mb-4"></div>
          <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>
          <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 w-3/4 bg-gray-200 rounded"></div>
        </div>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="h-5 w-16 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-24 bg-gray-200 rounded"></div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="h-5 w-24 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  const handlePrevious = () => {
    // If viewing user stories, first go back to epic view
    if (viewingUserStories) {
      setViewingUserStories(false);
    } else {
      setCurrentIndex((prev) => Math.max(0, prev - 1));
    }
  };

  const handleNext = () => {
    // If viewing user stories, ensure we get back to epic view before moving to next epic
    if (viewingUserStories) {
      setViewingUserStories(false);
    }
    setCurrentIndex((prev) => Math.min(prev + 1, epicCount - 1));
  };

  const handleBackEpic = () => {
    setViewingUserStories(false)
    setRowClick(false)
  }

  const isFirstEpic = currentIndex === 0;
  const isLastEpic = currentIndex === epicCount - 1 || epicCount === 0;

  const isFirstUserStory = userStoryIndex === 0;
  const isLastUserStory = userStoryIndex === userStories.length - 1 || userStories.length === 0;

  return (
    <div className="p-4 h-full  overflow-y-auto px-4 bg-white ">
      <div className="mx-auto w-full">
        {/* Navigation Buttons */}
        <div className="flex justify-end mb-2 -mt-4">
          <div className="flex space-x-2 ">
            {viewingUserStories ? (
              <>
                <button
                  className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200"
                  onClick={handleBackEpic}
                  title="Navigate to Epic overview"
                >
                  <ArrowLeft size={16} className="mr-1" />
                  Back to Epic
                </button>
                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200 ${isFirstUserStory ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handlePreviousUserStory}
                  disabled={isFirstUserStory}
                  title="View previous UserStory"
                >
                  <ArrowLeft size={16} className="mr-1" />
                  Previous UserStory
                </button>
                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200 ${isLastUserStory ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handleNextUserStory}
                  disabled={isLastUserStory}
                  title="View next UserStory"
                >
                  Next UserStory
                  <ArrowRight size={16} className="ml-1" />
                </button>
              </>
            ) : (
              <>
                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200`}
                  onClick={handleSkipAllEpics}
                  title="Skip this section and go to Architectural Requirements."
                >
                  <FaForward size={16} className="mr-1" />
                  Skip All
                </button>
                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200 ${isFirstEpic ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handlePrevious}
                  disabled={isFirstEpic}
                  title="View previous Epic"
                >
                  <ArrowLeft size={16} className="mr-1" />
                  Previous Epic
                </button>
                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200 ${isLastEpic ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handleNext}
                  disabled={isLastEpic}
                  title="View Next Epic"
                >
                  Next Epic
                  <ArrowRight size={16} className="ml-1" />
                </button>
              </>
            )}
          </div>

          {/* {!viewingUserStories && (
            <button
              className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-primary-100 hover:text-primary-700 transition border border-gray-200"
              onClick={handleSkipAll}
              disabled={epics.length === 0}
            >
              <FaForward size={16} className="mr-1" />
              Skip All
            </button>
          )} */}
        </div>

        {showAllSkippedNotification && <Skipped_Epics />}

        {/* Progress Indicators */}
        {!showAllSkippedNotification && !viewingUserStories ? (
          <div className="flex justify-between items-center mb-6 px-4 py-2 bg-gray-100 rounded">
            <div className="text-gray-700 font-weight-medium">Epic Progress:</div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-700">
                {currentIndex + 1} / {epicCount}
              </span>
              <div className="w-24 h-2 bg-gray-200 rounded-full">
                <div
                  className="h-2 bg-primary rounded-full"
                  style={{
                    width: `${((currentIndex + 1) / epicCount) * 100}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>
        ) : viewingUserStories && userStories.length > 0 ? (
          <div className="flex justify-between items-center mb-6 px-4 py-2 bg-primary-50 rounded">
            <div className="text-primary-700 font-weight-medium">User Story Progress:</div>
            <div className="flex items-center space-x-2">
              <span className="text-primary-700">
                {userStoryIndex + 1} / {userStories.length}
              </span>
              <div className="w-24 h-2 bg-primary-100 rounded-full">
                <div
                  className="h-2 bg-primary rounded-full"
                  style={{
                    width: `${((userStoryIndex + 1) / userStories.length) * 100}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>
        ) : (
          ""
        )}

        {/* Content Section */}
        {isLoading ? (
          <LoadingSkeleton />
        ) : viewingUserStories ? (
          // User Story View
          <div className="bg-white rounded-lg shadow-md p-6 -mt-4">
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center space-x-4">
                <div className="typography-body-sm bg-gray-100 text-gray-500 px-3 py-1 rounded">
                  {`STORY-${currentUserStory?.id || 'ID'}`}
                </div>
                <div className="typography-body-sm bg-gray-100 text-gray-500 px-3 py-1 rounded">
                  Parent: {`EPIC-${currentEpic?.id || 'ID'}`}
                </div>
              </div>
              {/* <button
                className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-primary-100 hover:text-primary-700 transition border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={handleSkipUserStory}
                disabled={(testCase?.length ?? 0) > 0}
              >
                <FaForward size={16} className="mr-1" />
                Skip User Story
              </button> */}
            </div>

            {loadingUserStories ? (
              <LoadingSkeleton />
            ) : userStories.length > 0 ? (
              <UserStoryDetail />
            ) : (
              <div className="text-center py-10">
                <p className="text-gray-500">No user stories found for this epic.</p>
              </div>
            )}
          </div>
        ) : showConfigModel && (taskStatus.toLowerCase() === 'in_progress' || taskStatus.toLowerCase() === 'idle') ? (
          <StatusPanel />
        ) :
          currentEpic ? (
            // Epic View
            <div className="bg-white rounded-lg shadow-md p-6 -mt-4">
              <div className="flex items-center mb-4 space-x-4 justify-between">
                <div className="flex items-center space-x-4 flex-wrap">
                  <div className="typography-body-sm bg-gray-100 text-gray-500 px-3 py-1 rounded">
                    {`EPIC-${currentEpic.id}` || "EPIC-ID"}
                  </div>
                  <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                    {currentEpic.title || "No title available"}
                  </h2>
                  <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full typography-body-sm">
                    Epic
                  </span>
                  <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full typography-body-sm">
                    {currentEpic.priority || "Medium"}
                  </span>
                </div>
                <div className="flex space-x-2">
                  {userStories.length > 0 && (
                    <button
                      className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-primary-100 hover:text-primary-700 transition border border-gray-200"
                      onClick={toggleUserStoryView}
                      title="Click to see the Userstory details"
                    >
                      Configure User Stories
                      <ArrowRight size={16} className="ml-1" />
                    </button>
                  )}
                  {/* <button
                  className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-primary-100 hover:text-primary-700 transition border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={handleSkip}
                  disabled={(userStories?.length ?? 0) > 0}
                >
                  <FaForward size={16} className="mr-1" />
                  {"Skip Epic"}
                </button> */}
                </div>
              </div>

              {showSkipNotification ? (
                <EpicSkippedNotification />
              ) : userStories.length > 0 ? (
                // Show user stories table instead of success badge
                <UserStoriesTable />
              ) : (
                <>
                  <div className="mb-6">
                    <h3 className="typography-body-lg font-weight-medium text-gray-700 mb-4">
                      Generate User Stories For This Epic
                    </h3>
                  </div>

                  {/* Buttons */}
                  <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                    <div
                      className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === "discussion"
                          ? "border-primary bg-primary-50"
                          : "border-gray-200"
                        }`}
                      onClick={() => handleUpdateEpics(currentEpic.id)}
                    >
                      <div className="flex items-center space-x-2 mb-4">
                        <div className="py-1 px-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                          <Image
                            src={Logo}
                            alt="Logo"
                            width={16}
                            height={16}
                            className="text-primary"
                          />
                        </div>
                        <h4 className="typography-body-lg font-weight-medium">
                          Interactive configuration
                        </h4>
                      </div>

                      <p className="text-gray-600">
                        {en.EpicUpdate}
                      </p>
                    </div>

                    <div
                      className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === "auto"
                          ? "border-primary bg-primary-50"
                          : "border-gray-200"
                        }`}
                      onClick={handleConfigureClick}
                    >
                      <div className="flex mb-4 items-center space-x-2">
                        <div className="p-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                          <Upload className="w-4 h-4  text-primary text-bold" />
                        </div>
                        <h4 className="typography-body-lg font-weight-medium">
                          Auto Configuration
                        </h4>
                      </div>

                      <p className="text-gray-600">
                        {en.EpicAutoConfig}
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>
          ) : !showAllSkippedNotification && !isLoading ? (
            <EmptyStateView type="epic" />
          ) : null}
      </div>

      {isOpen ? (
        <SkipAllModel />
      ) : null}

      {configureModel && (
        <ConfigureModal
          id={currentEpic.id}
          type={"Epic"}
          isNodeType={"Epic"}
          requirementId={currentEpic.id}
          isCreateProject={true}
          setShowConfigModel={setShowConfigModel}
          closeModal={handleCloseModal}
          setLoadingAutoConfigure={setLoadingAutoConfigure}
          onSubmitSuccess={() => {
            const successMessage = "Epic Configured Successfully";
            showAlert(successMessage, "success");

            // Log the successful configuration to MongoDB
            try {
              createProjectGuidanceFlow(parseInt(projectId), {
                project_id: parseInt(projectId),
                step_name: "epic_configuration",
                status: "completed",
                data: {
                  epic_id: parseInt(currentEpic.id),
                  type: "Epic",
                  status: "configured",
                },
              })
                .then((result) => {

                  // Set the flag to true to indicate this epic has been configured
                  sessionStorage.setItem(
                    `openEpicContent-${projectId}-${currentEpic.id}`,
                    "true"
                  );
                  // Refresh the epics list and fetch user stories
                  fetchEpics();
                  fetchUserStories(currentEpic.id);
                })
                .catch((error) => {

                });
            } catch (error) {

            }
          }}
        />
      )}
    </div>
  );
}

function Skipped_Epics() {
  return (
    <div className="p-4 max-h-[40vh] overflow-y-auto bg-white">
      <div className="mx-auto w-full max-w-2xl">
        <div className="bg-gradient-to-r from-primary-50 to-cyan-50 rounded-lg shadow p-4 mb-4 border-l-4 border-cyan-500">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-green-500 text-white p-2 rounded-full">
              <CheckCircle size={24} />
            </div>
          </div>

          <h2 className="typography-heading-4 font-weight-semibold text-center text-gray-800 mb-2">
            All Epics Skipped Successfully
          </h2>

          <p className="text-gray-600 text-center typography-body-sm mb-4">
            You've chosen to skip all epics for now. You can create user
            stories later in the workflow manager.
          </p>

          <div className="border-t border-gray-200 pt-4">
            <h3 className="typography-body-sm font-weight-medium text-gray-700 mb-2">
              What's next?
            </h3>
            <ul className="space-y-2 typography-body-sm">
              <li className="flex items-start gap-2">
                <div className="bg-cyan-100 text-cyan-600 p-1 rounded-full">
                  <ArrowRight size={14} />
                </div>
                <span>Your project setup will continue to the next step</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="bg-cyan-100 text-cyan-600 p-1 rounded-full">
                  <ArrowRight size={14} />
                </div>
                <span>
                  You can find your epics in the Requirements tab after
                  project creation
                </span>
              </li>
              <li className="flex items-start gap-2">
                <div className="bg-cyan-100 text-cyan-600 p-1 rounded-full">
                  <ArrowRight size={14} />
                </div>
                <span>
                  The workflow manager will guide you through creating user
                  stories
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}