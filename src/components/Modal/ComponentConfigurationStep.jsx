"use client"

import { useState, useContext, useEffect, useCallback } from 'react';
import { ArrowLeft, ArrowRight, Info, AlertCircle, CheckCircle, Upload } from 'lucide-react';
import Logo from "../../../public/logo/kavia_logo.svg";
import Image from "next/image";
import ConfigureModal from "../Modal/ConfigureModel";
import { StateContext } from '../Context/StateContext';
import PropertiesRenderer from '../UiMetadata/PropertiesRenderer';
import {
    fetchComponentsWithChildren,
    fetchComponentInterfaces,
    fetchNodeById,
    getAllComponentsFromProject,
    updateNodeByPriority
} from "@/utils/api";
import { ProjectSetupContext } from '../Context/ProjectSetupContext';
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { AlertContext } from '../NotificationAlertService/AlertList';
import EmptyStateView from './EmptyStateModal';
import TableComponent from '../SimpleTable/table';
import PlaceholderMessage from './PlaceHolderMessage';
import APIDocumentation from '../API/API';
import en from "@/en.json";
import { ExecutionContext } from '../Context/ExecutionContext';
import StatusPanel from "@/components/StatusPanel/StatusPanel";

const DISCUSSION_TYPES = [
    "architecture_details",
    "component_details"
];

// Mapping of discussion types to their configuration state properties
const CONFIG_STATE_MAPPING = {
    "architecture_details": "design_detail_state",
    "component_details": "configuration_state"
};

export default function ComponentConfigurationStep({ type = "components overview" }) {
    const [currentIndex, setCurrentIndex] = useState(0);
    const { projectId, ComponentData, setComponentData, testCasesLoading, setTestCasesLoading, componentId, setComponentId, showConfigModel, setShowConfigModel } = useContext(ProjectSetupContext);
    const [configMethod, setConfigMethod] = useState('discussion');
    const [configureModel, setConfigureModel] = useState(false);
    const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
    const [skippedComponents, setSkippedComponents] = useState([]);
    const [showSkipNotification, setShowSkipNotification] = useState(false);
    const [showAllSkippedNotification, setShowAllSkippedNotification] = useState(false);
    const [componentCount, setComponentCount] = useState(0);
    const [functionalReqs, setFunctionalReqs] = useState(null);
    const [components, setComponents] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [sourceComponentDetails, setSourceComponentDetails] = useState({});
    const [testCases, setTestCases] = useState([]);
    const [testCasePage, setTestCasePage] = useState(1);
    const [testCasePageSize, setTestCasePageSize] = useState(10);
    const searchParams = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();
    const { showAlert } = useContext(AlertContext);
    const { setIsVertCollapse } = useContext(StateContext);
    const [metaData, setMetaData] = useState([])
    const selectedComponentId = searchParams.get("selectedComponentId");
    const [showComponentDetails, setShowComponentDetails] = useState(false);
    const [activeDiscussion, setActiveDiscussion] = useState(1);
    const [architectureDiscussionCompleted, setArchitectureDiscussionCompleted] = useState({});;
    const [componentDiscussionCompleted, setComponentDiscussionCompleted] = useState({});
    const [completedDiscussions, setCompletedDiscussions] = useState({});
    const [allDiscussionsComplete, setAllDiscussionsComplete] = useState(false);
    const [currentDiscussionIndex, setCurrentDiscussionIndex] = useState(0);
    const { configStatus, currentTaskId, setAutoNavigateEnabled } = useContext(ExecutionContext)
    const [taskStatus, setTaskStatus] = useState("Idle");

    // Get current discussion type based on index
    const currentDiscussionType = DISCUSSION_TYPES[currentDiscussionIndex];


    const headers = [
        { key: "id", label: "Id" },
        { key: "title", label: "Title" },
        // { key: "type", label: "Type" },
        { key: "description", label: "Description" },
        { key: "container", label: "Container" }
    ];

    const interfaceTableHeaders = [
        { key: "id", label: "ID" },
        { key: "title", label: "Title" },
        { key: "definition_state", label: "Definition State" },
    ];

    const interfaceHeaders = [
        { key: "sourceComponentId", label: "ID" },
        { key: "interfaceName", label: "Interface Name" },
        { key: "sourceComponentTitle", label: "Source Component Title" },
        {
            key: "sourceComponentDescription",
            label: "Source Component Description",
        },
    ];

    const [interfaces, setInterfaces] = useState(null);

    // Parse interfaces helper functions
    const parseIncomingInterfaces = (interfaceData) => {
        if (!interfaceData?.data?.interfaces) return [];

        const interfaces = [];

        interfaceData.data.interfaces.forEach((intf) => {
            const incomingInterfaces = JSON.parse(
                intf.properties.incoming_interfaces || "[]"
            );

            incomingInterfaces.forEach((incoming) => {
                interfaces.push({
                    id: incoming.source_component_id,
                    sourceComponentId: incoming.source_component_id,
                    sourceComponentTitle:
                        sourceComponentDetails[incoming.source_component_id]?.title || "",
                    interfaceName: incoming.name || "", // Using the name property from incoming interface
                    sourceComponentDescription:
                        sourceComponentDetails[incoming.source_component_id]?.description ||
                        "",
                });
            });
        });

        return interfaces;
    };

    const parseInterfaces = (interfaceData) => {
        if (!interfaceData?.data?.interfaces) return [];

        return interfaceData.data.interfaces.map((intf) => ({
            id: intf.id,
            title: intf.properties.Title,
            definition_state: (
                <DefinitionStateBadge state={intf.properties.definition_state} />
            ),
        }));
    };

    const DefinitionStateBadge = ({ state }) => {
        return (
            <span className="bg-gray-200 rounded-xl px-2 py-0.5 breadcrumb-title">
                {state || "Unknown"}
            </span>
        );
    };

    const cleanDescription = (description) => {
        if (!description) return '';

        return description
            .replace(/#+\s/g, '')        // Remove markdown headers (# Header)
            .replace(/\*\*/g, '')        // Remove bold (**text**)
            .replace(/\*/g, '')          // Remove italics (*text*)
            .replace(/`/g, '')           // Remove code ticks (`code`)
            .replace(/\n\n/g, ' ')       // Replace double line breaks with space
            .replace(/\n-\s/g, ', ')     // Replace bullet points with commas
            .replace(/\n\d+\.\s/g, ', ') // Replace numbered lists with commas
            .replace(/\n/g, ' ')         // Replace remaining line breaks with spaces
            .replace(/\s{2,}/g, ' ')     // Replace multiple spaces with single space
            .trim();                     // Trim extra whitespace
    };

    const getDiscussionTitle = (type) => {
        switch (type) {
            case "architecture_details": return "System Architecture";
            case "component_details": return "Component Details";
            default: return "Discussion";
        }
    };

    // Fetch components for the project
    useEffect(() => {
        const fetchComponents = async () => {
            if (!projectId) return;

            setLoading(true);
            try {
                const response = await getAllComponentsFromProject(projectId);
                if (response && response.containers && Array.isArray(response.containers)) {
                    const allComponents = response.containers.flatMap(container => {
                        // Map container components to include container information
                        return (container.components || []).map(component => ({
                            ...component,
                            containerName: container.container_name,
                            containerTitle: container.container_name,
                            containerID: container.container_id
                        }));
                    });

                    setComponents(allComponents);
                    setComponentData(allComponents); // Update the context as well
                    setComponentCount(allComponents.length);



                    // If we have components and no componentId is set, set the first one
                    if (allComponents.length > 0 && !componentId) {
                        setComponentId(allComponents[0].id);
                    }
                    const data = await fetchComponentsWithChildren(projectId, componentId);
                    setMetaData(data.model?.Component?.ui_metadata)

                } else {

                    setError("Failed to fetch components: Invalid data format");
                }
            } catch (error) {

                setError("Failed to fetch components");
            } finally {
                setLoading(false);
            }
        };

        fetchComponents();
    }, [projectId, setComponentData, setComponentId]);

    // Fetch component details when componentId changes
    useEffect(() => {
        const fetchComponentDetails = async () => {
            if (!projectId || !componentId) return;

            setLoading(true);
            try {
                // Find the current component from our components list
                const selectedComponent = components.find(comp => comp.id === componentId);
                if (selectedComponent) {


                    // Fetch interface data
                    try {
                        const interfaceData = await fetchComponentInterfaces(projectId, componentId);
                        setInterfaces(interfaceData);

                        // Fetch source component details for interfaces
                        if (interfaceData?.data?.interfaces) {
                            const sourceComponents = {};
                            const incomingInterfaces = interfaceData.data.interfaces
                                .map((intf) => JSON.parse(intf.properties.incoming_interfaces || "[]"))
                                .flat();

                            for (const intf of incomingInterfaces) {
                                if (intf.source_component_id && !sourceComponents[intf.source_component_id]) {
                                    try {
                                        const sourceData = await fetchNodeById(intf.source_component_id, "Component");

                                        if (sourceData) {
                                            sourceComponents[intf.source_component_id] = {
                                                title: sourceData.properties?.Title || "",
                                                description: sourceData.properties?.Description || "",
                                            };
                                        }
                                    } catch (error) {

                                    }
                                }
                            }
                            setSourceComponentDetails(sourceComponents);
                        }
                    } catch (interfaceError) {

                    }

                    // Fetch functional requirements if implemented requirement IDs exist
                    if (selectedComponent?.properties?.ImplementedRequirementIDs &&
                        Array.isArray(selectedComponent.properties.ImplementedRequirementIDs) &&
                        selectedComponent.properties.ImplementedRequirementIDs.length > 0) {
                        try {
                            // This function wasn't imported in the original code
                            // const reqData = await getComponentFunctionalRequirements(projectId, componentId);
                            // setFunctionalReqs(reqData);

                        } catch (error) {

                        }
                    }
                } else {

                }

            } catch (err) {

                setError("Failed to fetch component details");
            } finally {
                setLoading(false);
            }
        };

        if (components.length > 0) {
            fetchComponentDetails();
        }
    }, [projectId, componentId, components]);

    useEffect(() => {
        // Check if both discussions are completed
        if (architectureDiscussionCompleted || componentDiscussionCompleted) {
            // Both discussions are completed, fetch components data
            const fetchUpdatedComponents = async () => {
                if (!projectId) return;

                setLoading(true);
                try {
                    const response = await getAllComponentsFromProject(projectId);
                    if (response && response.containers && Array.isArray(response.containers)) {
                        const allComponents = response.containers.flatMap(container => {
                            // Map container components to include container information
                            return (container.components || []).map(component => ({
                                ...component,
                                containerName: container.container_name,
                                containerTitle: container.container_name,
                                containerID: container.container_id
                            }));
                        });

                        setComponents(allComponents);
                        setComponentData(allComponents); // Update the context as well
                        setComponentCount(allComponents.length);

                        // If we have components and no componentId is set, set the first one
                        if (allComponents.length > 0 && !componentId) {
                            setComponentId(allComponents[0].id);
                        }

                        // Fetch metadata for the current component
                        if (componentId) {
                            const data = await fetchComponentsWithChildren(projectId, componentId);
                            setMetaData(data.model?.Component?.ui_metadata);
                        }




                    } else {
                        console.error("Invalid data format:", response);
                        setError("Failed to fetch components: Invalid data format");
                    }
                } catch (error) {
                    console.error("Error fetching components:", error);
                    setError("Failed to fetch components");
                } finally {
                    setLoading(false);
                }
            };

            fetchUpdatedComponents();

            // Reset the discussion completion flags after fetching
            setArchitectureDiscussionCompleted(false);
            setComponentDiscussionCompleted(false);
        }
    }, [architectureDiscussionCompleted, componentDiscussionCompleted, projectId, componentId]);

    useEffect(() => {
        if (configStatus[currentTaskId]) {

            setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
            setAutoNavigateEnabled(false)

        }
    }, [currentTaskId, configStatus[currentTaskId], projectId])

    // Get current component
    const currentComponent = components.length > 0 ? components[currentIndex] : null;


    // Prepare components table data
    const componentsTableData = components.map(component => ({
        id: component.id,
        title: component.properties?.Title || "Untitled",
        // type: component.properties?.Type || component.type || "Component",
        description: component.properties?.Description ?
            (component.properties.Description.length > 100 ?
                cleanDescription(component.properties.Description.substring(0, 100)) + "..." :
                cleanDescription(component.properties.Description)) :
            "No description",
        container: component.containerName || "Unknown Container"
    }));

    // Parse child nodes data
    const tableData = ComponentData?.data?.childNodes?.map((data) => {
        return {
            id: data.id,
            title: data.properties.Title,
            type: data.properties.Type,
            description: data.properties.Description,
        };
    }) || [];

    const handleCloseModal = () => {
        setConfigureModel(false);
    };

    const handleConfigureClick = () => {
        setConfigMethod('auto');
        setConfigureModel(true);
    };

    useEffect(() => {
        if (selectedComponentId && components.length > 0) {
            const componentIndex = components.findIndex(comp => comp.id.toString() === selectedComponentId);
            if (componentIndex !== -1) {
                setCurrentIndex(componentIndex);
                setComponentId(components[componentIndex].id);
                setShowComponentDetails(true);
            }
        }
    }, [selectedComponentId, components]);
    

    useEffect(() => {
        if (currentComponent?.properties?.design_detail_state === "configured" &&
            !currentComponent?.properties?.configuration_state === "configured") {

            setActiveDiscussion(2);
        }
    }, [currentComponent?.properties?.design_detail_state, currentComponent?.properties?.configuration_state, componentId]);

    // Add this effect to check for completed discussions
    useEffect(() => {
        // Check if the URL has parameters indicating completed discussions
        const discussionStatus = sessionStorage.getItem("discussion_status");
        const discussionType = sessionStorage.getItem("discussion_type");

        if (discussionStatus === "completed") {
            if (discussionType === "architecture") {
                setArchitectureDiscussionCompleted(true);

            } else if (discussionType === "component") {
                setComponentDiscussionCompleted(true);
            }

            // Clear the URL parameters

        }
    }, [searchParams, pathname, router, projectId, componentId]);
const handlePropertyUpdate = async (key, value) => {
  try {
    const response = await updateNodeByPriority(currentComponent.id, key, value);

    if (response === "success" || response?.status === "success") {
      // Update ComponentData context
      setComponentData((prev) =>
        prev.map((component) =>
          component.id === currentComponent.id
            ? {
                ...component,
                properties: {
                  ...component.properties,
                  [key]: value,
                },
              }
            : component
        )
      );

      // Also update local components list to sync state
      setComponents((prev) =>
        prev.map((component) =>
          component.id === currentComponent.id
            ? {
                ...component,
                properties: {
                  ...component.properties,
                  [key]: value,
                },
              }
            : component
        )
      );

      showAlert("Content updated successfully", "success");
    } else {
      showAlert("Failed to update content", "error");
    }
  } catch (error) {
    
    showAlert("Failed to update content", "error");
  }
};

    const isDiscussionCompleted = useCallback((componentId, discussionType) => {
        if (!components || components.length === 0) return false;

        const component = components.find(comp => comp.id === componentId);
        if (!component || !component.properties) return false;

        const configKey = CONFIG_STATE_MAPPING[discussionType];
        return component.properties[configKey] === "configured";
    }, [components]);

    // Handle a discussion being completed
    const handleDiscussionCompletion = useCallback((componentId, discussionType) => {
        setCompletedDiscussions(prev => {
            const componentDiscussions = prev[componentId] || {};
            const updatedComponentDiscussions = {
                ...componentDiscussions,
                [discussionType]: true
            };

            return {
                ...prev,
                [componentId]: updatedComponentDiscussions
            };
        });

        // Move to next discussion if this isn't the last one
        const currentTypeIndex = DISCUSSION_TYPES.indexOf(discussionType);
        if (currentTypeIndex < DISCUSSION_TYPES.length - 1) {
            // Add a slight delay to allow state updates to complete
            setTimeout(() => {
                setCurrentDiscussionIndex(currentTypeIndex + 1);
            }, 300);
        }
    }, []);

    const canSelectDiscussion = useCallback((componentId, discussionType) => {
        const index = DISCUSSION_TYPES.indexOf(discussionType);

        // First discussion is always selectable
        if (index === 0) return true;

        // For other discussions, check if all previous discussions are completed
        for (let i = 0; i < index; i++) {
            if (!isDiscussionCompleted(componentId, DISCUSSION_TYPES[i])) {
                return false;
            }
        }

        return true;
    }, [isDiscussionCompleted]);

    // Handle discussion selection
    const handleDiscussionSelect = (index) => {
        // Only allow selecting if prerequisites are met
        if (canSelectDiscussion(currentComponent.id, DISCUSSION_TYPES[index])) {
            setCurrentDiscussionIndex(index);
        }
    };

    useEffect(() => {
        if (!currentComponent) return;

        // Check if the first discussion is completed
        const isFirstDiscussionCompleted =
            currentComponent.properties?.design_detail_state === "configured";

        // Check if the second discussion is completed
        const isSecondDiscussionCompleted =
            currentComponent.properties?.configuration_state === "configured";

        if (isFirstDiscussionCompleted && !isSecondDiscussionCompleted) {
            // First discussion completed but second is not - show second discussion
            setCurrentDiscussionIndex(1);
        } else if (!isFirstDiscussionCompleted) {
            // First discussion not completed - show first discussion
            setCurrentDiscussionIndex(0);
        } else if (isFirstDiscussionCompleted && isSecondDiscussionCompleted) {
            // Both discussions completed - show completed state
            setAllDiscussionsComplete(true);
        }
    }, [currentComponent]);



    const handleUpdateComponent = (id) => {
        setConfigMethod('discussion');
        if (!id) return;
        setComponentId(id);
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("discussion", "new");
        newSearchParams.set("node_id", id);
        newSearchParams.set("node_type", "Architecture");
        newSearchParams.set("is_creating_Component", "true");

        router.push(`${pathname}?${newSearchParams.toString()}`);
    };


    const handleArchitectureDiscussion = (id) => {
        if (!id) return;
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("discussion", "new");
        newSearchParams.set("node_id", id);
        newSearchParams.set("node_type", "Architecture");
        newSearchParams.set("discussionType", "design_details")
        newSearchParams.set("is_creating_architecture", "true");
        router.push(`${pathname}?${newSearchParams.toString()}`);
    };





    const handleSkipAll = () => {
        if (components.length === 0) return;

        const allIdsToSkip = components.map((component) => component.id);
        const updatedSkipped = [...skippedComponents, ...allIdsToSkip];
        sessionStorage.setItem('skippedComponents', JSON.stringify(updatedSkipped));
        setSkippedComponents(updatedSkipped);
        setShowAllSkippedNotification(true);

        setCurrentIndex(components.length - 1);
        setTimeout(() => {
            setCurrentIndex(0);
        }, 50);
    };

    const handleSkip = () => {
        if (!components[currentIndex]) return;

        const isComponentConfigured = sessionStorage.getItem(
            `openComponentContent-${projectId}-${components[currentIndex].id}`
        ) === 'true';

        if (isComponentConfigured) {
            // Just move to the next component when already configured
            setCurrentIndex((prev) => Math.min(prev + 1, components.length - 1));
        } else {
            const componentId = components[currentIndex].id;
            const updatedSkipped = [...skippedComponents, componentId];
            sessionStorage.setItem('skippedComponents', JSON.stringify(updatedSkipped));
            setSkippedComponents(updatedSkipped);
            setShowSkipNotification(true);

            setTimeout(() => {
                setShowSkipNotification(false);
                handleNext(); // Simply move to next component
            }, 5000);
        }
    };

    const handlePrevious = () => {
        setCurrentIndex((prev) => Math.max(0, prev - 1));
    };

    const handleNext = () => {
        setCurrentIndex((prev) => Math.min(prev + 1, components.length - 1));
    };

    const handleTestCaseClick = (id) => {
        // Implement test case click handling

    };

    const handleBackToComponentsList = () => {

        setShowComponentDetails(false);
        setComponentId(null)
        sessionStorage.removeItem("discussionStatus")
        sessionStorage.removeItem("discussionType")

        const newSearchParams = new URLSearchParams(searchParams);

        if (newSearchParams.has("selectedComponentId")) {

            newSearchParams.delete("selectedComponentId");
        }
        router.replace(`${pathname}?${newSearchParams.toString()}`, { scroll: false });

    };

    const handleComponentRowClick = (id) => {
        setShowComponentDetails(true)
        // Set the selected component ID and update current index
        if (type === "componentsList") {
            const newSearchParams = new URLSearchParams(searchParams);
            newSearchParams.set("stepName", "Components Details");
            newSearchParams.set("selectedComponentId", id);
            router.push(`${pathname}?${newSearchParams.toString()}`);
        } else {
            // Set the selected component ID and update current index for the detail view
            setComponentId(parseInt(id));
            const componentIndex = components.findIndex(comp => comp.id === parseInt(id));
            if (componentIndex !== -1) {
                setCurrentIndex(componentIndex);
            }
        }
    };

    // Helper flags
    const isFirstComponent = currentIndex === 0;
    const isLastComponent = currentIndex === components.length - 1 || components.length === 0;

    // Check if current component is configured
    const isComponentConfigured = currentComponent ?
        sessionStorage.getItem(`openComponentContent-${projectId}-${currentComponent.id}`) === 'true' :
        false;

    // Check if current component has minimal details (only title and description)
    const hasMinimalDetails = currentComponent ?
        (currentComponent.properties &&
            Object.keys(currentComponent.properties).length <= 3 &&
            (currentComponent.properties.Title || currentComponent.properties.Description)) :
        false;

    // Check if component has complete details for PropertiesRenderer
    const hasCompleteDetails = currentComponent &&
        currentComponent?.properties?.configuration_state === "configured" &&
        currentComponent?.properties?.design_detail_state === "configured"

    // Notification components
    function ComponentSkippedNotification() {
        return (
            <div className="max-w-3xl mx-auto p-4">
                <div className="border border-cyan-200 bg-cyan-50 rounded-lg p-4 flex items-start gap-3">
                    <div className="text-gray-500">
                        <Info size={24} />
                    </div>
                    <div>
                        <p className="text-gray-800 font-weight-medium">
                            Component skipped: You can configure "{currentComponent?.properties?.Title || currentComponent?.title}" later in the workflow manager.
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    function AllComponentsSkippedNotification() {
        return (
            <div className="border-l-4 border-green-500 bg-green-50 p-4 mb-8 flex items-start gap-3">
                <div className="text-gray-500">
                    <Info size={24} />
                </div>
                <div>
                    <p className="text-gray-800">
                        All components skipped: You can configure all components later in the workflow manager.
                    </p>
                </div>
            </div>
        );
    }

    function SuccessBadge() {
        return (
            <>
                {currentComponent ? (
                    <div>
                        <PropertiesRenderer
                            properties={currentComponent.properties}
                            metadata={metaData}
                            to_skip={["configuration_state", "Type", "Title"]}
                            onUpdate={handlePropertyUpdate}
                        />
                        {/* <div className="mt-6">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="typography-body-lg font-weight-medium text-gray-800">Test Cases</h3>
                                {testCasesLoading ? (
                                    <div className="px-4 py-2 typography-body-sm text-gray-600">Loading...</div>
                                ) : testCases.length > 0 ? (
                                    <div className="px-4 py-2 typography-body-sm rounded-md bg-primary-50 text-primary-700">
                                        {testCases.length} test cases found
                                    </div>
                                ) : null}
                            </div>

                            {testCasesLoading ? (
                                <div className="h-40 flex items-center justify-center">
                                    <CardLoadingSkeleton />
                                </div>
                            ) : testCases.length > 0 ? (
                                <div className="border rounded-lg p-6">
                                  
                                    <p className="text-center text-gray-600">Test cases available</p>
                                </div>
                            ) : (
                                <div className="border rounded-lg p-6 flex flex-col items-center justify-center bg-gray-50">
                                    <p className="text-gray-500 mb-4">No test cases are associated with this component</p>
                                    <DynamicButton
                                        variant="primary"
                                        icon={Plus}
                                        text="Create Test Case"
                                        onClick={() => {
                                            const newSearchParams = new URLSearchParams(searchParams);
                                            newSearchParams.set("discussion", "new");
                                            newSearchParams.set("node_id", componentId);
                                            newSearchParams.set("node_type", "Architecture");
                                            newSearchParams.set("discussion_type", "component_testcase_generation");
                                            router.push(`${pathname}?${newSearchParams.toString()}`);
                                        }}
                                    />
                                </div>
                            )}
                        </div> */}
                        <div id="functional-requirements">
                            {functionalReqs && (functionalReqs.length > 0) ? (
                                <TableComponent
                                    data={functionalReqs?.functional_requirements?.map((fr) => ({
                                        id: fr.id,
                                        title: fr.properties.Title,
                                        type: fr.properties.Type,
                                        description: fr.properties.Description,
                                    }))}
                                    headers={[
                                        { key: "id", label: "ID" },
                                        { key: "title", label: "Title" },
                                        { key: "type", label: "Type" },
                                        { key: "description", label: "Description" },
                                    ]}
                                    title="Functional Requirements"
                                    itemsPerPage={20}
                                    onRowClick={(id) => { }}
                                />
                            ) : (
                                <div className="mt-5 mb-3">
                                    <PlaceholderMessage type="subComponents" message="No functional requirements are currently available" subMessage="Add a new functional requirements to get started." />
                                </div>
                            )
                            }</div>
                        <div id="sub-components" className="relatedContentDiv ">
                            {tableData && tableData.length > 0 ? (
                                <>
                                    <TableComponent
                                        data={tableData}
                                        onRowClick={() => { }}
                                        headers={headers}
                                        sortableColumns={{ id: true, title: true, type: true }}
                                        itemsPerPage={20}
                                        title={en.ChildSubComponentsHeading}
                                    />
                                </>
                            ) : (
                                <div className="mt-5 mb-3">
                                    <PlaceholderMessage type="subComponents" message="No sub components are currently available" subMessage="Add a new sub components to get started." />
                                </div>
                            )}
                        </div>

                        <div id="public-interfaces" className="space-y-4">
                            {interfaces && interfaces.data?.interfaces?.length > 0 ? (
                                <>
                                    <TableComponent
                                        data={parseInterfaces(interfaces)}
                                        headers={interfaceTableHeaders}
                                        sortableColumns={{
                                            id: true,
                                            title: true,
                                            definition_state: true,
                                        }}
                                        itemsPerPage={20}
                                        onRowClick={(rowId) => {}}
                                        title="Public Interfaces"
                                    />

                                    <APIDocumentation
                                        apiDetails={
                                            interfaces.data.interfaces[0].properties?.PublicAPIDetails
                                        }
                                    />
                                </>
                            ) : (
                                <div className="mt-5 mb-3">
                                    <PlaceholderMessage type="interfaces" message="No interfaces are currently available" subMessage="Add a new interface to get started" />
                                </div>
                            )}
                        </div>

                        <div id="connected-components" className="space-y-4">
                            {interfaces && parseIncomingInterfaces(interfaces).length > 0 ? (
                                <TableComponent
                                    data={parseIncomingInterfaces(interfaces)}
                                    headers={interfaceHeaders}
                                    sortableColumns={{
                                        sourceComponentId: true,
                                        sourceComponentTitle: true,
                                    }}
                                    itemsPerPage={20}
                                    onRowClick={(rowId) => {
                                        if (rowId) {
                                            // Handle row click
                                        }
                                    }}
                                    title="Connected Components"
                                />
                            ) : (
                                <></>
                            )}
                        </div>
                    </div>
                ) : (
                    <p className="notFound">
                        <EmptyStateView type="componentsDetails" />
                    </p>
                )}
            </>
        );
    }



    const LoadingSpinner = () => (
        <div className="w-full animate-pulse rounded-lg overflow-hidden border border-gray-200">
            {/* Table header */}
            <div className="flex items-center p-4 border-b bg-gray-50 rounded-t-lg">
                <div className="w-5 h-5 bg-gray-200 rounded mr-3"></div>
                <div className="flex-1 grid grid-cols-5 gap-4">
                    <div className="h-6 bg-gray-200 rounded col-span-2"></div>
                    <div className="h-6 bg-gray-200 rounded"></div>
                    <div className="h-6 bg-gray-200 rounded"></div>
                    <div className="h-6 bg-gray-200 rounded"></div>
                </div>
            </div>

            {/* Table rows */}
            {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center p-4 border-b hover:bg-gray-50">
                    <div className="w-5 h-5 bg-gray-200 rounded mr-3"></div>
                    <div className="flex-1 grid grid-cols-5 gap-4">
                        <div className="col-span-2">
                            <div className="h-5 bg-gray-200 rounded mb-2 w-3/4"></div>
                            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        </div>
                        <div className="flex items-center">
                            <div className="h-6 w-16 bg-gray-200 rounded"></div>
                        </div>
                        <div className="h-5 bg-gray-200 rounded w-1/2"></div>
                        <div className="h-5 bg-gray-200 rounded w-1/4"></div>
                    </div>
                </div>
            ))}

            {/* Table footer / pagination */}
            <div className="flex justify-between items-center p-4 bg-gray-50 rounded-b-lg">
                <div className="h-8 w-32 bg-gray-200 rounded"></div>
                <div className="flex space-x-2">
                    <div className="h-8 w-8 bg-gray-200 rounded"></div>
                    <div className="h-8 w-8 bg-gray-200 rounded"></div>
                    <div className="h-8 w-8 bg-gray-200 rounded"></div>
                </div>
            </div>
        </div>
    );


    function LoadingSkeleton() {

        return (

            <div className="bg-white rounded-lg shadow-md p-6 mb-8 animate-pulse">

                <div className="flex justify-between items-center mb-4">

                    <div className="h-6 w-24 bg-gray-200 rounded"></div>

                    <div className="h-8 w-20 bg-gray-200 rounded"></div>

                </div>

                <div className="h-8 w-3/4 bg-gray-200 rounded mb-4"></div>

                <div className="flex space-x-2 mb-6">

                    <div className="h-6 w-16 bg-gray-200 rounded-full"></div>

                    <div className="h-6 w-20 bg-gray-200 rounded-full"></div>

                </div>

                <div className="mb-6">

                    <div className="h-6 w-1/2 bg-gray-200 rounded mb-4"></div>

                    <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>

                    <div className="h-4 w-5/6 bg-gray-200 rounded"></div>

                </div>

                <div className="flex flex-col md:flex-row gap-6 justify-center md:justify-start">

                    <div className="w-[300px] h-[200px] bg-gray-100 rounded-lg p-6">

                        <div className="flex items-center space-x-2 mb-4">

                            <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>

                            <div className="h-6 w-32 bg-gray-200 rounded"></div>

                        </div>

                        <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>

                        <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>

                        <div className="h-4 w-4/6 bg-gray-200 rounded"></div>

                    </div>

                    <div className="w-[300px] h-[200px] bg-gray-100 rounded-lg p-6">

                        <div className="flex items-center space-x-2 mb-4">

                            <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>

                            <div className="h-6 w-32 bg-gray-200 rounded"></div>

                        </div>

                        <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>

                        <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>

                        <div className="h-4 w-4/6 bg-gray-200 rounded"></div>

                    </div>

                </div>

            </div>

        );

    }


    if (error && !currentComponent) {
        return (
            <div className="p-6 h-full bg-white">
                <div className="max-w-3xl mx-auto p-6 border border-primary-200 bg-primary-50 rounded-lg">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            {/* Information icon */}
                            <svg className="h-6 w-6 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <h2 className="typography-body-lg font-weight-medium text-primary-800 mb-2">No Components Available</h2>
                            <p className="text-primary-700 mb-4">
                                No components are present for this project yet. You can continue creating your interface and set up components after the project is created.
                            </p>

                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Render components list view
    if ( !showComponentDetails) {
        return (
            <div className="p-6 h-full  overflow-y-auto">
                <div className="mx-auto w-full">
                    <h2 className="typography-body-lg font-weight-semibold text-gray-800 mb-4">
                        Components List
                    </h2>
                    {loading ? (
                        <div className=" flex items-center justify-center">
                            <LoadingSpinner />
                        </div>
                    )
                        : components.length > 0 ? (
                            <TableComponent
                                data={componentsTableData}
                                headers={headers}
                                title="Project Components"
                                itemsPerPage={20}
                                sortableColumns={{ id: true, title: true, type: true, container: true }}
                                onRowClick={handleComponentRowClick}
                            />
                        ) : (
                            <EmptyStateView type='components'/>
                        )}
                </div>
            </div>
        );
    }

    // Render components overview view (default)
    return (
        <div className="p-6 h-full  overflow-y-auto px-4 bg-white">
            {showConfigModel && (taskStatus.toLowerCase() === 'in_progress' || taskStatus.toLowerCase() === 'idle') ? (
                <StatusPanel />
            ) : (<>
                <div className="mx-auto w-full">

                    {/* Skip Button and Navigation Controls */}
                    <div className="flex justify-end mb-2 -mt-5">
                        <div className="flex space-x-2">
                            <button
                                className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200"
                                onClick={handleBackToComponentsList}

                            >
                                <ArrowLeft size={16} className="mr-2" />
                                Back to Components List
                            </button>
                            <button
                                className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200 ${isFirstComponent ? 'opacity-50 cursor-not-allowed' : ''}`}
                                onClick={handlePrevious}
                                disabled={isFirstComponent}
                            >
                                <ArrowLeft size={16} className="mr-1" />
                                Previous
                            </button>
                            <button
                                className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200 ${isLastComponent ? 'opacity-50 cursor-not-allowed' : ''}`}
                                onClick={handleNext}
                                disabled={isLastComponent}
                            >
                                Next
                                <ArrowRight size={16} className="ml-1" />
                            </button>
                        </div>
                        {/* <button
                        className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-primary-100 hover:text-primary-700 transition border border-gray-200"
                        onClick={handleSkipAll}
                        disabled={components.length === 0}
                    >
                        <FaForward size={16} className="mr-1" />
                        Skip All
                    </button> */}
                    </div>

                    {showAllSkippedNotification && <AllComponentsSkippedNotification />}

                    {/* Component Progress */}
                    {!showAllSkippedNotification && (
                        <div className="flex justify-between items-center mb-6 px-4 py-2 bg-gray-100 rounded">
                            <div className="text-gray-700 font-weight-medium">Component Progress:</div>
                            <div className="flex items-center space-x-2">
                                <span className="text-gray-700">{currentIndex + 1} / {componentCount}</span>
                                <div className="w-24 h-2 bg-gray-200 rounded-full">
                                    <div
                                        className="h-2 bg-primary rounded-full"
                                        style={{ width: `${componentCount > 0 ? ((currentIndex + 1) / componentCount) * 100 : 0}%` }}
                                    ></div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Component Card */}
                    {loading && showComponentDetails ? (
                        <LoadingSkeleton />
                    ):
                        currentComponent? (
                            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                                <div className="flex justify-between items-center mb-4 space-x-4">
                                    <div className="flex items-center space-x-4 flex-wrap">
                                        <div className="typography-body-sm bg-gray-100 text-gray-500 px-3 py-1 rounded">
                                            {`COMPONENT-${currentComponent.id || 'ID'}`}
                                        </div>
                                        <h2 className="typography-body-lg font-weight-semibold text-gray-800 ">
                                            {currentComponent.properties?.Title || currentComponent.title || "Untitled Component"}
                                        </h2>
                                        <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full typography-body-sm">
                                            {currentComponent.properties?.Type || currentComponent.componentType || 'Component'}
                                        </span>
                                        <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full typography-body-sm">
                                            {currentComponent.type || 'Architecture'}
                                        </span>

                                    </div>
                                    {/* <button
                                className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-primary-100 hover:text-primary-700 transition border border-gray-200 ${currentComponent?.properties?.configuration_state === "configured"? "disabled:opacity-50 disabled:cursor-not-allowed":""} `}
                                onClick={handleSkip}
                                disabled={currentComponent?.properties?.configuration_state === "configured"}
                            >
                                <FaForward size={16} className="mr-1" />
                                {'Skip'}
                            </button> */}
                                </div>



                                <div className="flex space-x-2 mb-2">

                                </div>

                                {/* <div className="mb-6">
                            <p className="typography-body-sm text-gray-500">
                                Container: {currentComponent.containerName || "Default Container"}
                            </p>
                        </div> */}

                                {showSkipNotification ? (
                                    <ComponentSkippedNotification />
                                ) :hasCompleteDetails ? (
                                    <SuccessBadge />
                                ) : (
                                    <>
                                        <div className="mb-6">
                                            <div className="flex justify-between items-center mb-2">
                                                <h3 className="typography-body-lg font-weight-medium text-gray-700 mb-4">
                                                    Configure  Component {currentDiscussionType === DISCUSSION_TYPES[0] ? "System Architecture" : "Content Creation"}
                                                </h3>

                                                {/* Progress Bar aligned right */}
                                                <div className="flex items-center space-x-2">
                                                    <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full typography-body-sm">
                                                        Discussion Progress
                                                    </span>
                                                    <span className="text-primary-700 typography-body-sm">
                                                        {currentDiscussionIndex + 1} / {DISCUSSION_TYPES.length}
                                                    </span>
                                                    <div className="w-24 h-2 bg-primary-100 rounded-full">
                                                        <div
                                                            className="h-2 bg-primary rounded-full transition-all duration-300"
                                                            style={{
                                                                width: `${((currentDiscussionIndex + 1) / DISCUSSION_TYPES.length) * 100}%`,
                                                            }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="mt-4 p-4 bg-amber-100 border-l-4 border-amber-400 rounded-md flex items-center">
                                                <AlertCircle className="text-amber-600 mr-3" size={24} />
                                                <p className="text-amber-800 typography-body-sm font-weight-medium">
                                                    To view the component details, make sure both the System Architecture and Component discussions are completed
                                                </p>
                                            </div>
                                            {/* <p className="text-gray-600">
                                        {currentComponent.properties?.Description || currentComponent.description || "No description available for this component."}
                                    </p> */}
                                        </div>

                                        {/* Discussion Status Pills */}
                                        <div className="flex flex-wrap gap-2 mb-6">
                                            {DISCUSSION_TYPES.map((type, index) => {
                                                const isCompleted = isDiscussionCompleted(currentComponent.id, type);
                                                // First discussion is always selectable, second only if first is completed
                                                const isSelectable = index === 0 || isDiscussionCompleted(currentComponent.id, DISCUSSION_TYPES[0]);
                                                const isDisabled = (index === 0 && isCompleted) ||
                                                    (index === 1 && (!isDiscussionCompleted(currentComponent.id, DISCUSSION_TYPES[0]) || isCompleted));

                                                return (
                                                    <button
                                                        key={type}
                                                        onClick={() => {
                                                            if (!isDisabled) {
                                                                setCurrentDiscussionIndex(index);
                                                            }
                                                        }}
                                                        className={`px-3 py-1 rounded-full typography-body-sm flex items-center gap-1 
          ${index === currentDiscussionIndex
                                                                ? 'bg-primary-100 text-primary-800 border border-primary-300'
                                                                : isCompleted
                                                                    ? 'bg-green-100 text-green-800 cursor-pointer'
                                                                    : isSelectable
                                                                        ? 'bg-gray-100 text-gray-600 cursor-pointer hover:bg-gray-200'
                                                                        : 'bg-gray-100 text-gray-400 opacity-60 cursor-not-allowed'
                                                            }`}
                                                        disabled={isDisabled}
                                                        title={
                                                            isCompleted
                                                                ? "This discussion is already completed"
                                                                : index === 1 && !isDiscussionCompleted(currentComponent.id, DISCUSSION_TYPES[0])
                                                                    ? "Complete System Architecture discussion first"
                                                                    : ""
                                                        }
                                                    >
                                                        {index + 1}. {getDiscussionTitle(type)}
                                                        {isCompleted && (
                                                            <CheckCircle size={14} className="text-green-600 ml-1" />
                                                        )}
                                                    </button>
                                                );
                                            })}
                                        </div>

                                        {/* Configuration Buttons */}
                                        <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                                            {currentDiscussionType === "architecture_details" ? (
                                                <div className="col-span-1 md:col-span-1 lg:col-span-2">
                                                    <div
                                                        className={`border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === 'discussion' ? 'border-primary bg-primary-50' : 'border-gray-200'
                                                            }`}
                                                        onClick={() => handleArchitectureDiscussion(currentComponent.id)}
                                                    >
                                                        <div className="flex items-center space-x-2 mb-4">
                                                            <div className="py-1 px-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                                                                <Image
                                                                    src={Logo}
                                                                    alt="Logo"
                                                                    width={16}
                                                                    height={16}
                                                                    className="text-primary"
                                                                />
                                                            </div>
                                                            <h4 className="typography-body-lg font-weight-medium">Interactive configuration</h4>
                                                        </div>

                                                        <p className="text-gray-600">
                                                            {en.ComponentUpdate}
                                                        </p>
                                                        {currentComponent?.properties?.design_detail_state === "configured" && (
                                                            <div className="mt-4 flex items-center text-green-700">
                                                                <CheckCircle size={16} className="mr-1" />
                                                                Completed
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="col-span-1 md:col-span-1 lg:col-span-2">
                                                    <div
                                                        className={`border rounded-lg p-6 transition-all hover:shadow-md cursor-pointer 
              ${configMethod === 'discussion' ? 'border-primary bg-primary-50' : 'border-gray-200'}
              ${currentComponent?.properties?.design_detail_state !== "configured" ? 'opacity-75 cursor-not-allowed pointer-events-none' : ''}
            `}
                                                        onClick={() => {
                                                            if (currentComponent?.properties?.design_detail_state === "configured") {
                                                                handleUpdateComponent(currentComponent.id);
                                                            }
                                                        }}
                                                        title={
                                                            currentComponent?.properties?.design_detail_state !== "configured"
                                                                ? "Please complete System Architecture configuration before creating a component."
                                                                : ""
                                                        }
                                                    >
                                                        <div className="flex items-center space-x-2 mb-4">
                                                            <div className="py-1 px-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                                                                <Image
                                                                    src={Logo}
                                                                    alt="Logo"
                                                                    width={16}
                                                                    height={16}
                                                                    className="text-primary"
                                                                />
                                                            </div>
                                                            <h4 className="typography-body-lg font-weight-medium">Interactive configuration</h4>
                                                        </div>

                                                        <p className="text-gray-600">
                                                            {en.ComponentUpdate}
                                                        </p>
                                                        {currentComponent?.properties?.configuration_state === "configured" && (
                                                            <div className="mt-4 flex items-center text-green-700">
                                                                <CheckCircle size={16} className="mr-1" />
                                                                Completed
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            )}

                                            <div
                                                className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === 'auto' ? 'border-primary bg-primary-50' : 'border-gray-200'
                                                    }`}
                                                onClick={handleConfigureClick}
                                            >
                                                <div className="flex mb-4 items-center space-x-2">
                                                    <div className="p-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                                                        <Upload className="w-4 h-4 text-primary" />
                                                    </div>
                                                    <h4 className="typography-body-lg font-weight-medium">Auto Configuration</h4>
                                                </div>

                                                <p className="text-gray-600">
                                                    Let our LLM automatically configure this component based on the available information
                                                </p>
                                            </div>
                                        </div>
                                    </>
                                )}
                            </div>
                        ) : !showAllSkippedNotification ? (
                            <EmptyStateView type='components'/>
                        ) : null}
                </div></>)}

            {configureModel && currentComponent && (
                <ConfigureModal
                    id={currentComponent.id}
                    type={"Architecture"}
                    isNodeType={"Architecture"}
                    isCreateProject={true}
                    setShowConfigModel={setShowConfigModel}
                    requirementId={currentComponent.id}
                    closeModal={handleCloseModal}
                    setLoadingAutoConfigure={setLoadingAutoConfigure}
                    onSubmitSuccess={() => {
                        const successMessage = "Component Configured Successfully";
                        showAlert(successMessage, "success");

                        // Mark this component as configured
                        sessionStorage.setItem(`openComponentContent-${projectId}-${currentComponent.id}`, 'true');

                        // Refresh component data by fetching all components again
                        const fetchNewData = async () => {
                            try {
                                const response = await getAllComponentsFromProject(projectId);
                                if (response && response.containers && Array.isArray(response.containers)) {
                                    const allComponents = response.containers.flatMap(container => {
                                        return (container.components || []).map(component => ({
                                            ...component,
                                            containerName: container.container_name,
                                            containerTitle: container.container_name,
                                            containerID: container.container_id
                                        }));
                                    });
                                    setComponents(allComponents);
                                    setComponentData(allComponents);
                                }
                            } catch (error) {

                            }
                        };

                        fetchNewData();
                    }}
                />
            )}
        </div>
    );
}