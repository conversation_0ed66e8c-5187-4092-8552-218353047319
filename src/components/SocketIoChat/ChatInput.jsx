import React, { useState, useRef, useEffect, useCallback, useContext } from "react";
import ReactDOM from "react-dom";
import { Plus, ArrowUp, X, FileText, Loader2, Paperclip, Figma, ChevronRight, Upload, ArrowLeft } from "lucide-react";
import { uploadMultipleAttachments } from "@/utils/fileAPI";
import { useParams, useSearchParams } from "next/navigation";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";
import { getFigmaFiles, getFigmaJsonFiles } from "@/utils/FigmaAPI";
import { getFigmaProcessingStatus, reloadFigmaDesign, getFramesList, WriteScreenList, getFigmaDesignList, getFigmaFilesScreens } from "@/api/figma";
import { AlertContext } from "../NotificationAlertService/AlertList";
import { useCodeGeneration } from "../Context/CodeGenerationContext";
import AttachmentSelector from "./AttachmentSelector";

// Image Preview Component
const ImagePreview = ({ imageUrl, isVisible, mousePosition }) => {
  if (!isVisible || !imageUrl) return null;

  return ReactDOM.createPortal(
    <div
      className="fixed pointer-events-none z-[10000]"
      style={{
        left: mousePosition.x + 10,
        top: mousePosition.y - 100,
        transform:
          mousePosition.x > window.innerWidth - 300
            ? "translateX(-100%)"
            : "none",
      }}
    >
      <div className="bg-white rounded-lg shadow-xl border border-gray-200 p-2 max-w-xs">
        <img
          src={imageUrl}
          alt="Figma frame preview"
          className="w-full h-auto max-h-48 rounded object-contain"
          onError={(e) => {
            e.target.style.display = "none";
            e.target.nextSibling.style.display = "block";
          }}
        />
        <div className="hidden text-xs text-gray-500 text-center py-2">
          Preview not available
        </div>
      </div>
    </div>,
    document.body
  );
};

// Progress Modal Component
const ProgressModal = ({
  isOpen,
  onClose,
  design,
  processingStatus,
  isLoadingStatus,
}) => {
  if (!isOpen) return null;

  return ReactDOM.createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[10000] flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 w-[425px] max-w-[90%] relative">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
        >
          <X className="h-5 w-5" />
        </button>

        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Processing Status</h2>
          <div className="space-y-4">
            {isLoadingStatus ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
              </div>
            ) : processingStatus ? (
              <>
                <div className="space-y-3">
                  {/* Design Name */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Design:</span>
                    <span className="text-sm text-gray-600">
                      {design?.name}
                    </span>
                  </div>

                  {/* Status Badge */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Status:</span>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${processingStatus.status === "completed"
                        ? "bg-green-100 text-green-800"
                        : processingStatus.status === "processing"
                          ? "bg-primary-100 text-primary-800"
                          : processingStatus.status === "failed"
                            ? "bg-red-100 text-red-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                    >
                      {processingStatus.status.charAt(0).toUpperCase() +
                        processingStatus.status.slice(1)}
                    </span>
                  </div>

                  {/* Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>
                        {Math.round(
                          (processingStatus.completed_frames /
                            processingStatus.total_frames) *
                          100
                        ) || 0}
                        %
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${processingStatus.status === "completed"
                          ? "bg-green-500"
                          : processingStatus.status === "failed"
                            ? "bg-gray-300"
                            : "bg-primary"
                          }`}
                        style={{
                          width: `${(processingStatus.completed_frames /
                            processingStatus.total_frames) *
                            100 || 0
                            }%`,
                        }}
                      />
                    </div>
                  </div>

                  {/* Frame Details */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Total Frames:</span>
                      <span>{processingStatus.total_frames}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Completed Frames:</span>
                      <span>{processingStatus.completed_frames}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Failed Frames:</span>
                      <span
                        className={
                          processingStatus.failed_frames > 0
                            ? "text-red-600"
                            : "text-gray-600"
                        }
                      >
                        {processingStatus.failed_frames}
                      </span>
                    </div>
                  </div>

                  {/* Error Message if any */}
                  {processingStatus.error_message && (
                    <div className="text-sm text-red-600 bg-red-50 p-3 rounded">
                      {processingStatus.error_message}
                    </div>
                  )}

                  {/* Last Updated */}
                  <div className="text-xs text-gray-500">
                    Last updated:{" "}
                    {new Date(processingStatus.time_updated).toLocaleString()}
                  </div>
                </div>
              </>
            ) : (
              <p className="text-gray-600">No status information available</p>
            )}
          </div>
          <div className="flex justify-end pt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

// Add Figma Design Modal
const AddFigmaDesignModal = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading,
  figmaFiles,
  selectedFigmaFiles,
  isLoadingFigma,
  expandedFigmaDesigns,
  onToggleFigmaDesign,
  figmaJsonFiles,
  onSelectFigmaJson,
  selectedFigmaJsonFiles,
  isLoadingFigmaJson,
  processingDesigns,
  onCheckStatus,
  jsonProcessingStatus,
  lastUpdateTime,
  onRebuildFigmaDesign,
  figmaErrors,
  showProcessing,
  setShowProcessing,
  setShowInitialProcessing,
  showInitialProcessing,
  setShowFrameSelector,
  showFrameSelector,
  framesList,
  setFramesList,
  projectId,
  setFrameId,
  frameId,
  designList,
  showDesignSelector,
  setShowDesignSelector,
  setEnableBackDesign,
  enablebackDesign,
  setUploadedAttachments,
  task_id,
  setDesignList,
  switchToFigmaTab,
  setFigmaLoader
}) => {
  const [designName, setDesignName] = useState("");
  const [figmaLink, setFigmaLink] = useState("");
  const [submittedDesign, setSubmittedDesign] = useState(null);
  const [hoveredImage, setHoveredImage] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [selectedDesignId, setSelectedDesignId] = useState(null);
  const [selectedFrames, setSelectedFrames] = useState(new Set());
  const [selectedDesigns, setSelectedDesigns] = useState(new Set());

  // Add search functionality
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredFrames, setFilteredFrames] = useState([]);
  const [isExtractingFrames, setIsExtractingFrames] = useState(false);
  const [isFetchingScreens, setIsFetchingScreens] = useState(false)
  const [showExistingDesign, setShowExistingDesign] = useState(null)
  const { showAlert } = useContext(AlertContext)

  // Maximum frame selection limit
  // const MAX_FRAME_SELECTION = 3;

  const getExtractedFrames = () => {
    try {
      const extracted = sessionStorage.getItem(`extractedFrames_${projectId}`);
      return extracted ? new Set(JSON.parse(extracted)) : new Set();
    } catch (error) {
      console.error('Error reading extracted frames from sessionStorage:', error);
      return new Set();
    }
  };

  // Save extracted frames to sessionStorage
  const saveExtractedFrames = (frameIds) => {
    try {
      const currentExtracted = getExtractedFrames();
      frameIds.forEach(id => currentExtracted.add(id));
      sessionStorage.setItem(`extractedFrames_${projectId}`, JSON.stringify([...currentExtracted]));
    } catch (error) {
      console.error('Error saving extracted frames to sessionStorage:', error);
    }
  };

  const [extractedFrames, setExtractedFrames] = useState(new Set());

  useEffect(() => {
    if (isOpen) {
      setExtractedFrames(getExtractedFrames());
    }
  }, [isOpen, projectId]);

  // Filter frames based on search term
  useEffect(() => {
    if (!framesList) {
      setFilteredFrames([]);
      return;
    }

    if (!searchTerm.trim()) {
      setFilteredFrames(framesList);
    } else {
      const filtered = framesList.filter(frame =>
        frame?.screen_name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredFrames(filtered);
    }
  }, [framesList, searchTerm]);

  // Check if there's already a design being processed when modal opens
  useEffect(() => {
    if (isOpen && figmaFiles && figmaFiles.length > 0) {
      // Check if any design is currently processing
      const processingDesign = figmaFiles.find(design =>
        ["processing", "processing_wait"].includes(design.status)
      );

      if (processingDesign) {
        setShowProcessing(true);
        setSubmittedDesign(processingDesign);
        setSelectedDesignId(processingDesign.id);
        // Expand the processing design by default
        if (!expandedFigmaDesigns.includes(processingDesign.id)) {
          onToggleFigmaDesign(processingDesign.id);
        }
      } else {
        // Check if there's a completed design that should show frame selector
        const completedDesigns = figmaFiles.filter(design =>
          ["completed", "partially_completed"].includes(design.status)
        );
        if (completedDesigns.length > 0 && showProcessing) {
          setSelectedDesignId(completedDesigns[0].id);
          // Show frame selector for completed designs
          setShowFrameSelector(true);
        }
      }
    }
  }, [isOpen, figmaFiles]);

  // Handle mouse movement for image preview
  const handleMouseMove = (e) => {
    setMousePosition({ x: e.clientX, y: e.clientY });
  };

  // Handle mouse enter for JSON file with image
  const handleJsonFileMouseEnter = (jsonFile) => {
    if (jsonFile.image_url) {
      setHoveredImage(jsonFile.image_url);
    }
  };

  // Handle mouse leave for JSON file
  const handleJsonFileMouseLeave = () => {
    setHoveredImage(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (designName.trim() && figmaLink.trim()) {
      // Show initial processing screen immediately
      setShowInitialProcessing(true);

      const newDesign = await onSubmit(designName.trim(), figmaLink.trim());

      // After submission, switch to frames processing view and track the new design
      if (newDesign) {
        setSubmittedDesign(newDesign);
        setSelectedDesignId(newDesign.id);
        setShowInitialProcessing(false);
        setShowProcessing(true);
      }
    }
  };

  const handleClose = () => {
    if (!isLoading && !showInitialProcessing) {
      setDesignName("");
      setFigmaLink("");
      setShowProcessing(false);
      setSubmittedDesign(null);
      setSelectedDesignId(null);
      setHoveredImage(null);
      setShowInitialProcessing(false);
      setShowFrameSelector(false);
      setShowDesignSelector(false)
      setEnableBackDesign(false)
      setSelectedFrames(new Set());
      setSearchTerm("")
      onClose();
    }
  };

  const handleBackToForm = () => {
    setShowProcessing(false);
    setSubmittedDesign(null);
    setShowDesignSelector(false)
    setSelectedDesignId(null);
    setShowFrameSelector(false);
    setShowExistingDesign(true)
    setSearchTerm("");
  };
  const handleFetchDesign = async () => {
    try {
      const response = await getFigmaDesignList(projectId)
      if (response?.data.length > 0) {
        setDesignList(response.data);
      }
    } catch (error) {
      console.error("error in fetching the design list", error)
    }
  }



  const handleBackToDesign = () => {
    handleFetchDesign()
    setShowDesignSelector(true)
    setShowProcessing(false);
    setSubmittedDesign(null);
    setSelectedDesignId(null);
    setShowInitialProcessing(false);
    setShowFrameSelector(false);
    setShowExistingDesign(false)
    setSearchTerm("");
  };
  const handleSelectFrames = () => {
    setShowProcessing(false);
    setShowDesignSelector(false)
    setSubmittedDesign(null);
    setSelectedDesignId(null);
    setShowFrameSelector(true);
    setSearchTerm("");
  };


  const handleViewProcessing = async () => {
    try {
      // Find any design that's currently processing first, otherwise show completed designs
      const figmaData = {
        name: "",
        url: figmaLink
      };
      const response = await getFramesList(projectId, figmaData)
      if (response?.screen_list) {
        setFramesList(response?.screen_list)
      }
      const processingDesign = figmaFiles.find(design =>
        ["processing", "processing_wait"].includes(design.status)
      );

      const targetDesign = processingDesign || figmaFiles.find(design =>
        ["completed", "partially_completed"].includes(design.status)
      );

      if (targetDesign) {
        setSubmittedDesign(targetDesign);
        setSelectedDesignId(targetDesign.id);

        // Show frame selector if design is completed, otherwise show processing
        if (["completed", "partially_completed"].includes(targetDesign.status)) {
          setShowFrameSelector(true);
          setShowProcessing(false);
        } else {
          setShowProcessing(true);
          setShowFrameSelector(false);
        }

        // Expand the target design by default
        if (!expandedFigmaDesigns.includes(targetDesign.id)) {
          onToggleFigmaDesign(targetDesign.id);
        }
      }
    } catch (error) {
      console.error("Error in fetching the frames")
    }

  };

  const handleSelectDesign = (designId) => {
    const selectedDesign = figmaFiles.find(design => design.id === designId);
    if (selectedDesign) {
      setSubmittedDesign(selectedDesign);
      setSelectedDesignId(designId);

      // Show appropriate view based on design status
      if (["completed", "partially_completed"].includes(selectedDesign.status)) {
        setShowFrameSelector(false);
        setShowProcessing(true);
      } else {
        setShowProcessing(true);
        setShowFrameSelector(false);
      }

      // Expand the selected design by default
      if (!expandedFigmaDesigns.includes(designId)) {
        onToggleFigmaDesign(designId);
      }
    }
  };

  const handleDesignToggle = (designId) => {
    setSelectedDesigns(new Set([designId]));
  };

  const handleDisplayScreens = async (designId) => {
    try {
      setIsFetchingScreens(true)
      const response = await getFigmaDesignList(projectId, designId)
      if (response?.data) {
        setFramesList(response?.data?.screen_list)
        setFrameId(response?.data?.figma_id)
        setShowFrameSelector(true)
        setEnableBackDesign(true)
        setShowDesignSelector(false)
      }
    } catch (error) {
      console.error("error in fetching the designs", error)
    } finally {
      setIsFetchingScreens(false)
    }
  }

  // Frame selector functions
  const handleFrameToggle = (frameId) => {
    // Check if frame is already extracted
    if (extractedFrames.has(frameId)) {
      return; // Do nothing if frame is already extracted
    }

    const newSelected = new Set(selectedFrames);
    if (newSelected.has(frameId)) {
      newSelected.delete(frameId);
    } else {
      // REMOVED: Maximum selection limit check - now unlimited
      newSelected.add(frameId);
    }
    setSelectedFrames(newSelected);
  };

  // Updated handleSelectAll function - UPDATED: Removed limit check
  const handleSelectAll = () => {
    // Filter available frames (not extracted and within search results)
    const availableFrames = filteredFrames.filter(frame =>
      !extractedFrames.has(frame.screen_id)
    );

    // Get currently selected frames from available frames
    const selectedFromAvailable = availableFrames.filter(frame =>
      selectedFrames.has(frame.screen_id)
    );

    if (selectedFromAvailable.length === availableFrames.length && availableFrames.length > 0) {
      // If all available filtered frames are selected, deselect them
      const newSelected = new Set(selectedFrames);
      availableFrames.forEach(frame => {
        newSelected.delete(frame.screen_id);
      });
      setSelectedFrames(newSelected);
    } else {
      // UPDATED: Select ALL available frames (no limit)
      const newSelected = new Set(selectedFrames);
      availableFrames.forEach(frame => {
        newSelected.add(frame.screen_id);
      });
      setSelectedFrames(newSelected);
    }
  };

  // New deselect all function
  const handleDeselectAll = () => {
    setSelectedFrames(new Set());
  };

  const handleExtractFrames = async () => {
    // if (selectedFrames.size > 3) {
    //   showAlert("You can only extract up to 3 frames at a time.", "error");
    //   return;
    // }
    try {
      setIsExtractingFrames(true);
      const figmaData = {
        selected_screen_ids: Array.from(selectedFrames),
        figma_id: frameId
      };
      const response = await WriteScreenList(projectId, task_id, figmaData);

      // Process the response to create attachment objects
      if (response && response.screens_path && response.processed_screen_ids) {
        const extractedAttachments = [];

        // Process each screen path from the response
        Object.entries(response.screens_path).forEach(([filename, filePath]) => {
          // Find the corresponding screen ID
          const screenId = response.processed_screen_ids.find(id =>
            filename.includes(id.replace(':', '_')) || filename.includes(id)
          );

          if (screenId) {
            extractedAttachments.push({
              attachment_id: screenId,
              filename: filename,
              file_location: filePath,
              file_type: "figma_extracted_screen",
              size: null // You can add size if available in response
            });
          }
        });

        // Add the extracted attachments to the uploadedAttachments state
        // so they can be included in the next message
        // setUploadedAttachments((prev) => [...prev, ...extractedAttachments]);
        setFigmaLoader(true);
        showAlert("Screens extracted successfully! You're all set to proceed with code generation.", "success")

        // Save extracted frames to sessionStorage
        saveExtractedFrames([...selectedFrames]);

        // Update local state
        const newExtractedFrames = new Set([...extractedFrames, ...selectedFrames]);
        setExtractedFrames(newExtractedFrames);

        // Clear current selection
        setSelectedFrames(new Set());

        setShowFrameSelector(false);
        setShowProcessing(true);
        setSearchTerm("");

       if (switchToFigmaTab) {
          setTimeout(() => {
            switchToFigmaTab(projectId);
            handleClose();
            
            // Hide loader after 1 second
            setTimeout(() => {
              setFigmaLoader(false);
            }, 1000);
          }, 100); // Small delay to ensure modal states are updated first
        }

      }
    } catch (error) {
      console.error("error", error);
    } finally {
      setIsExtractingFrames(false);
    }
  };
  const handleClearSearch = () => {
    setSearchTerm("");
  };

  // Check if there are any designs that can be viewed (processing or completed)
  const hasViewableDesigns = figmaFiles && figmaFiles.some(design =>
    ["processing", "processing_wait", "completed", "partially_completed"].includes(design.status)
  );

  // Check if there are any designs currently processing
  const hasProcessingDesign = figmaFiles && figmaFiles.some(design =>
    ["processing", "processing_wait"].includes(design.status)
  );

  if (!isOpen) return null;

  const getStatusColor = (status) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "processing":
        return "bg-primary-100 text-primary-800";
      case "processing_wait":
        return "bg-purple-100 text-purple-800";
      case "partially_completed":
        return "bg-yellow-100 text-yellow-800";
      case "failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Find the current design being viewed
  const currentDesign = selectedDesignId ?
    figmaFiles.find(design => design.id === selectedDesignId) :
    submittedDesign;

  // Get available designs for the dropdown
  const availableDesigns = figmaFiles ? figmaFiles.filter(design =>
    ["processing", "processing_wait", "completed", "partially_completed"].includes(design.status)
  ) : [];

  const selectedFilteredFramesCount = filteredFrames.filter(frame =>
    selectedFrames.has(frame.screen_id)
  ).length;

  // Count available frames (not extracted) from filtered results
  const availableFilteredFramesCount = filteredFrames.filter(frame =>
    !extractedFrames.has(frame.screen_id)
  ).length;

  return ReactDOM.createPortal(
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-[10000] flex items-center justify-center"
      onMouseMove={handleMouseMove}
    >
      <div className="bg-white rounded-2xl w-full max-w-2xl relative max-h-[90vh] flex flex-col shadow-2xl border border-gray-100 overflow-hidden">
        {showInitialProcessing ? (
          // Initial Processing Screen
          <>
            {/* Header with close button */}
            <div className="relative bg-white border-b border-gray-200">
              <div className="flex items-center justify-between p-6">
                <h2 className="text-lg font-medium text-gray-900">Import Figma Design into KAVIA</h2>
                <button
                  onClick={handleClose}
                  className="text-gray-500 hover:text-gray-700 transition-colors"
                  disabled={true}
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Gradient Header Section */}
            <div className="relative">
              <div className="h-32 bg-gradient-to-r from-teal-400 via-blue-400 to-orange-400"></div>
              <div className="flex justify-center -mt-12 mb-8">
                <div className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-xl border-4 border-white bg-black">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="48" viewBox="0 0 32 48" fill="none">
                    <path d="M16 0H8C3.582 0 0 3.582 0 8s3.582 8 8 8h8V0z" fill="#F24E1E" />
                    <path d="M16 16H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V16z" fill="#A259FF" />
                    <path d="M16 32H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V32z" fill="#1ABCFE" />
                    <path d="M16 0h8c4.418 0 8 3.582 8 8s-3.582 8-8 8h-8V0z" fill="#FF7262" />
                    <circle cx="16" cy="24" r="8" fill="#0ACF83" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Processing Content */}
            <div className="px-8 pb-8 text-center">
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Processing</h3>
              <p className="text-gray-600 text-base mb-8">We are fetching the screens.</p>

              {/* Loading spinner */}
              <div className="relative flex items-center justify-center">
                <div className="flex items-center space-x-8">
                  <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
                    <div className="w-4 h-4 bg-white rounded"></div>
                  </div>
                  <div className="relative flex-1">
                    <div className="h-0.5 bg-gray-300 w-16"></div>
                    {[0, 1, 2].map((i) => (
                      <div
                        key={i}
                        className="absolute w-2 h-2 bg-blue-500 rounded-full top-1/2 transform -translate-y-1/2"
                        style={{
                          animation: 'bridgeData 2s linear infinite',
                          animationDelay: `${i * 0.3}s`
                        }}
                      ></div>
                    ))}
                  </div>
                  <div className="w-8 h-8 bg-gradient-to-r from-teal-400 to-orange-400 rounded-lg flex items-center justify-center">
                    <div className="w-4 h-4 bg-white rounded"></div>
                  </div>
                </div>
                <style jsx>{`
                  @keyframes bridgeData {
                    0% { left: 0; opacity: 0; }
                    50% { opacity: 1; }
                    100% { left: 100%; opacity: 0; }
                  }
                `}</style>
              </div>
            </div>
          </>
        ) : showFrameSelector ? (
          // Frame Selector Screen - UPDATED VERSION WITH REQUESTED CHANGES
          <>
            {/* Header with close button */}
            <div className="relative bg-white border-b border-gray-200">
              <div className="flex items-center justify-between p-6">
                <h2 className="text-lg font-medium text-gray-900">Import Figma Design into KAVIA</h2>
                <button
                  onClick={handleClose}
                  className="text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Gradient Header Section */}
            <div className="relative">
              <div className="h-24 bg-gradient-to-r from-teal-400 via-blue-400 to-orange-400"></div>
              <div className="flex justify-center -mt-10 mb-4">
                <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg border-3 border-white bg-black">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="36" viewBox="0 0 32 48" fill="none">
                    <path d="M16 0H8C3.582 0 0 3.582 0 8s3.582 8 8 8h8V0z" fill="#F24E1E" />
                    <path d="M16 16H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V16z" fill="#A259FF" />
                    <path d="M16 32H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V32z" fill="#1ABCFE" />
                    <path d="M16 0h8c4.418 0 8 3.582 8 8s-3.582 8-8 8h-8V0z" fill="#FF7262" />
                    <circle cx="16" cy="24" r="8" fill="#0ACF83" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Content Section with improved spacing */}
            <div className="px-6 pb-6 flex-1 overflow-hidden flex flex-col">

              {/* Warning Alert - moved up for better visibility */}
              {/* <div className="mb-4 bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-3">
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0">
                    <svg className="w-4 h-4 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-orange-800">
                      Maximum 3 screens can be selected at once
                    </p>
                  </div>
                </div>
              </div> */}

              {/* Header - UPDATED: Removed "Choose up to 3 screens for processing" text */}
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    Select Screens to Extract
                  </h3>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={
                      selectedFilteredFramesCount === Math.min(availableFilteredFramesCount)
                        ? handleDeselectAll
                        : handleSelectAll
                    }
                    disabled={availableFilteredFramesCount === 0}
                    className={`text-sm font-medium px-3 py-1 rounded-md transition-colors ${availableFilteredFramesCount === 0
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-orange-600 hover:text-orange-700 bg-orange-50 hover:bg-orange-100'
                      }`}
                  >
                    {selectedFilteredFramesCount === Math.min(availableFilteredFramesCount)
                      ? 'Deselect All'
                      : `Select All`}
                  </button>
                  {enablebackDesign ? (
                    <button
                      onClick={handleBackToDesign}
                      className="flex items-center gap-1 border border-orange-500 text-orange-500 hover:bg-orange-50 rounded px-3 py-1.5 text-sm font-medium"
                    >
                      <ArrowLeft size={16} />
                      Back
                    </button>
                  ) : null}

                  <button
                    onClick={handleBackToForm}
                    className="flex items-center gap-1 border border-orange-500 text-orange-500 hover:bg-orange-50 rounded px-3 py-1.5 text-sm font-medium"
                  >
                    <Plus size={16} />
                    Add Figma
                  </button>

                </div>

              </div>

              {/* Search Bar with improved design */}
              <div className="mb-4">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg
                      className="h-4 w-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Search screens by name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm bg-white shadow-sm"
                  />
                  {searchTerm && (
                    <button
                      onClick={handleClearSearch}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <svg
                        className="h-4 w-4 text-gray-400 hover:text-gray-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  )}
                </div>

                {/* Search results info */}
                {searchTerm && (
                  <div className="mt-2 flex items-center justify-between text-xs">
                    <span className="text-gray-500">
                      {filteredFrames.length === 0
                        ? `No screens found for "${searchTerm}"`
                        : `Showing ${filteredFrames.length} of ${framesList.length} screens`
                      }
                    </span>
                    {filteredFrames.length > 0 && (
                      <span className="text-gray-400">
                        {availableFilteredFramesCount} available
                      </span>
                    )}
                  </div>
                )}
              </div>

              {/* Scrollable frame list with improved spacing and design */}
              <div className="flex-1 overflow-hidden mb-4">
                <div
                  className="h-48 border border-gray-200 rounded-lg overflow-y-auto bg-white shadow-sm"
                  style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#d1d5db #f9fafb'
                  }}
                >
                  {filteredFrames.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full text-gray-500 p-6">
                      {searchTerm ? (
                        <>
                          <svg
                            className="w-12 h-12 mb-3 text-gray-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={1.5}
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            />
                          </svg>
                          <p className="text-sm font-medium">No screens found</p>
                          <p className="text-xs text-gray-400 mt-1">Try adjusting your search term</p>
                        </>
                      ) : (
                        <>
                          <svg
                            className="w-12 h-12 mb-3 text-gray-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={1.5}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                          <p className="text-sm font-medium">No screens available</p>
                          <p className="text-xs text-gray-400 mt-1">There are no screens available for the uploaded Figma design.</p>
                        </>
                      )}
                    </div>
                  ) : (
                    <div className="p-4 space-y-2">
                      {filteredFrames.map((frame, index) => {
                        const isSelected = selectedFrames.has(frame.screen_id);
                        const isExtracted = extractedFrames.has(frame.screen_id);
                        const isDisabled = isExtracted;

                        return (
                          <div
                            key={frame.screen_id}
                            className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${isExtracted
                              ? 'bg-green-50 border-green-200 cursor-not-allowed'
                              : isDisabled && !isSelected
                                ? 'bg-gray-50 border-gray-200 cursor-not-allowed opacity-60'
                                : isSelected
                                  ? 'bg-orange-50 border-orange-200 cursor-pointer shadow-sm'
                                  : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300 cursor-pointer'
                              }`}
                            onClick={() => !isDisabled && handleFrameToggle(frame.screen_id)}
                          >
                            <div className="flex items-center gap-3">
                              {/* Custom checkbox with improved design */}
                              <div
                                className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${isExtracted
                                  ? 'bg-green-100 border-green-400'
                                  : isSelected
                                    ? 'bg-orange-500 border-orange-500 shadow-sm'
                                    : isDisabled
                                      ? 'border-gray-200 bg-gray-100'
                                      : 'border-gray-300 hover:border-orange-400'
                                  }`}
                              >
                                {isExtracted ? (
                                  <svg
                                    className="w-3 h-3 text-green-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={3}
                                      d="M5 13l4 4L19 7"
                                    />
                                  </svg>
                                ) : isSelected && (
                                  <svg
                                    className="w-3 h-3 text-white"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M5 13l4 4L19 7"
                                    />
                                  </svg>
                                )}
                              </div>

                              <div className="flex flex-col">
                                <span className="text-sm font-medium text-gray-900">
                                  {frame?.screen_name}
                                </span>
                                {isExtracted && (
                                  <span className="text-xs text-green-600">
                                    Already extracted
                                  </span>
                                )}
                              </div>
                            </div>

                            {/* Status indicator */}
                            <div className="flex items-center">
                              {isExtracted ? (
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              ) : isSelected ? (
                                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                              ) : (
                                <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>

              {/* Action buttons with improved design */}
              <div className="border-t border-gray-200 pt-4 space-y-3">
                <button
                  onClick={handleExtractFrames}
                  disabled={selectedFrames.size === 0 || isExtractingFrames}
                  className={`w-full py-3 px-6 rounded-lg font-medium text-base transition-all flex items-center justify-center gap-3 ${selectedFrames.size === 0 || isExtractingFrames
                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    : 'bg-orange-500 hover:bg-orange-600 text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5'
                    }`}
                >
                  {isExtractingFrames ? (
                    <>
                      <Loader2 size={20} className="animate-spin" />
                      <span>Extracting screens...</span>
                    </>
                  ) : (
                    <>
                      <Upload size={20} />
                      <span>Extract Selected Screen{selectedFrames.size !== 1 ? 's' : ''} ({selectedFrames.size})</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </>
        ) : showDesignSelector ? (
          <>
            {/* Header with close button */}
            <div className="relative bg-white border-b border-gray-200">
              <div className="flex items-center justify-between p-6">
                <h2 className="text-lg font-medium text-gray-900">Import Figma Design into KAVIA</h2>
                <button
                  onClick={handleClose}
                  className="text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Gradient Header Section */}
            <div className="relative">
              <div className="h-32 bg-gradient-to-r from-teal-400 via-blue-400 to-orange-400"></div>
              <div className="flex justify-center -mt-12 mb-6">
                <div className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-xl border-4 border-white bg-black">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="48" viewBox="0 0 32 48" fill="none">
                    <path d="M16 0H8C3.582 0 0 3.582 0 8s3.582 8 8 8h8V0z" fill="#F24E1E" />
                    <path d="M16 16H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V16z" fill="#A259FF" />
                    <path d="M16 32H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V32z" fill="#1ABCFE" />
                    <path d="M16 0h8c4.418 0 8 3.582 8 8s-3.582 8-8 8h-8V0z" fill="#FF7262" />
                    <circle cx="16" cy="24" r="8" fill="#0ACF83" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="px-6 pb-6 flex-1 overflow-hidden flex flex-col">
              {/* Header with selection info */}
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">
                  List of designs for this project
                </h3>
                <div className="flex items-center gap-4">
                  {/* <span className="text-sm text-gray-500">
                    {designList.length}
                  </span> */}
                  <button
                    onClick={handleBackToForm}
                    className="flex items-center gap-1 border border-orange-500 text-orange-500 hover:bg-orange-50 rounded px-3 py-1.5 text-sm font-medium"
                  >
                    <Plus size={16} />
                    Add Figma
                  </button>
                </div>
              </div>

              {/* Scrollable frame list with border */}
              <div className="flex-1 overflow-hidden mb-6">
                <div
                  className="h-80 border border-gray-200 rounded-lg overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-500 p-4"
                  style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#9ca3af #f3f4f6'
                  }}
                >
                  {designList.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full text-gray-500">

                      <>
                        <svg
                          className="w-12 h-12 mb-4 text-gray-300"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={1.5}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                        <p className="text-sm">No designs available for this project </p>
                      </>

                    </div>
                  ) : (
                    <div className="space-y-2">
                      {designList.map((design) => {
                        const isSelected = selectedDesigns.has(design.figma_id);
                        return (
                          <div
                            key={design.figma_id}
                            className={`flex items-center justify-between p-4 rounded-lg transition-colors ${!isSelected
                              ? 'hover:bg-gray-50 cursor-pointer'
                              : 'bg-orange-50 hover:bg-orange-100 cursor-pointer'
                              }`}
                            onClick={() => handleDesignToggle(design.figma_id)}
                          >
                            <div className="flex items-center gap-3">
                              {/* Custom checkbox */}
                              <div
                                className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${isSelected
                                  ? 'bg-orange-500 border-orange-500'
                                  : 'border-gray-300 hover:border-gray-400'
                                  }`}
                              >
                                {isSelected && (
                                  <svg
                                    className="w-3 h-3 text-white"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M5 13l4 4L19 7"
                                    />
                                  </svg>
                                )}
                              </div>

                              <span className="text-sm font-medium text-gray-900">
                                {design?.design_name || design?.figma_id}
                              </span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>

              {/* Extract button */}
              <div className="border-t border-gray-200 pt-4">
                <button
                  onClick={() => handleDisplayScreens(Array.from(selectedDesigns)[0])}
                  disabled={selectedDesigns.size === 0 || isFetchingScreens}
                  className={`w-full py-4 px-6 rounded-lg font-medium text-base transition-all flex items-center justify-center gap-3 ${selectedDesigns.size === 0 || isFetchingScreens
                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    : 'bg-orange-500 hover:bg-orange-600 text-white'
                    }`}
                >
                  {isFetchingScreens ? (
                    <>
                      <Loader2 size={20} className="animate-spin" />
                      <span>Fetching the screens...</span>
                    </>
                  ) : (
                    <span>Fetch screens for this design</span>
                  )}
                </button>
              </div>
            </div>
          </>

        ) : !showProcessing ? (
          // Initial Form Screen
          <>
            {/* Header with close button */}
            <div className="relative bg-white border-b border-gray-200">
              <div className="flex items-center justify-between p-6">
                <h2 className="text-lg font-medium text-gray-900">Import Figma Design into KAVIA</h2>
                <button
                  onClick={handleClose}
                  className="text-gray-500 hover:text-gray-700 transition-colors"
                  disabled={isLoading}
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Gradient Header Section */}
            <div className="relative">
              <div className="h-32 bg-gradient-to-r from-teal-400 via-blue-400 to-orange-400"></div>
              <div className="flex justify-center -mt-12 mb-8">
                <div className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-xl border-4 border-white bg-black">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="48" viewBox="0 0 32 48" fill="none">
                    <path d="M16 0H8C3.582 0 0 3.582 0 8s3.582 8 8 8h8V0z" fill="#F24E1E" />
                    <path d="M16 16H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V16z" fill="#A259FF" />
                    <path d="M16 32H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V32z" fill="#1ABCFE" />
                    <path d="M16 0h8c4.418 0 8 3.582 8 8s-3.582 8-8 8h-8V0z" fill="#FF7262" />
                    <circle cx="16" cy="24" r="8" fill="#0ACF83" />
                    <circle cx="16" cy="24" r="8" fill="#0ACF83" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="px-8 pb-8">
              <div className="text-center mb-8">
                <p className="text-gray-600 text-base leading-relaxed">
                  Turn your design into a live web app. Generate,<br />
                  preview and edit code instantly.
                </p>
              </div>

              {showExistingDesign && (
                <div className="text-center mb-6">
                  <button
                    onClick={handleBackToDesign}
                    className="inline-flex items-center gap-2 text-sm text-orange-600 hover:text-orange-700 font-medium transition-colors"
                  >
                    <span>{'View Existing Designs'}</span>
                    <span className="text-xs">→</span>
                  </button>
                </div>
              )}

              {/* Simple Form */}
              <div className="space-y-6">
                <div>
                  <input
                    id="figmaLink"
                    type="url"
                    value={figmaLink}
                    onChange={(e) => {
                      setFigmaLink(e.target.value);
                      if (!designName && e.target.value) {
                        const urlParts = e.target.value.split('/');
                        const fileName = urlParts[urlParts.length - 1] || 'Figma Design';
                        setDesignName(fileName.replace(/[^a-zA-Z0-9\s]/g, '').trim() || 'Figma Design');
                      }
                    }}
                    className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all placeholder-gray-400 text-base"
                    placeholder="Figma URL here..."
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <button
                    onClick={handleSubmit}
                    className={`w-full py-4 px-6 ${isLoading
                      ? "bg-orange-300 cursor-not-allowed"
                      : "bg-orange-500 hover:bg-orange-600"
                      } text-white rounded-lg font-medium text-base flex items-center justify-center gap-3 transition-all`}
                    disabled={isLoading || !figmaLink.trim()}
                  >
                    Extract Figma Design into KAVIA
                  </button>
                </div>
              </div>
            </div>
          </>
        ) : (
          // Processing Frames View (Image 2) - UPDATED SECTION WITH FIXED SCROLLBAR
          <div className="flex flex-col h-full">
            {/* Header with gradient and Figma icon */}
            <div className="relative flex-shrink-0">
              {/* Header with close button */}
              <div className="relative bg-white border-b border-gray-200 z-10">
                <div className="flex items-center justify-between p-6">
                  <h2 className="text-lg font-medium text-gray-900">Import Figma Design into KAVIA</h2>
                  <button
                    onClick={handleClose}
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    <X size={20} />
                  </button>
                </div>
              </div>

              {/* Teal to Orange Gradient Background */}
              <div className="h-32 bg-gradient-to-r from-teal-400 via-blue-400 to-orange-400"></div>

              {/* Figma Icon - positioned to overlap gradient */}
              <div className="flex justify-center -mt-12 mb-4">
                <div className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-xl border-4 border-white bg-black">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="48" viewBox="0 0 32 48" fill="none">
                    <path d="M16 0H8C3.582 0 0 3.582 0 8s3.582 8 8 8h8V0z" fill="#F24E1E" />
                    <path d="M16 16H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V16z" fill="#A259FF" />
                    <path d="M16 32H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h8V32z" fill="#1ABCFE" />
                    <path d="M16 0h8c4.418 0 8 3.582 8 8s-3.582 8-8 8h-8V0z" fill="#FF7262" />
                    <circle cx="16" cy="24" r="8" fill="#0ACF83" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Processing Frames Content - FIXED HEADER */}
            <div className="px-8 pb-4 flex-shrink-0">
              {/* Header with design selector and Add Figma button */}
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">
                  {'Processing Screens'}
                </h3>

                <div className="flex items-center space-x-3">
                  {!hasProcessingDesign ? (
                    <button
                      onClick={handleSelectFrames}
                      className="flex items-center gap-1 border border-orange-500 text-orange-500 hover:bg-orange-50 rounded px-3 py-1.5 text-sm font-medium"
                    >
                      <ArrowLeft size={16} />
                      Back
                    </button>
                  ) : null}
                  <button
                    onClick={handleBackToForm}
                    className="flex items-center gap-1 border border-orange-500 text-orange-500 hover:bg-orange-50 rounded px-3 py-1.5 text-sm font-medium"
                  >
                    <Plus size={16} />
                    Add Figma
                  </button>
                </div>
              </div>

              {/* Progress info - UPDATED PROGRESS BAR */}
              {filteredFrames && filteredFrames.length > 0 && (
                <div className="mb-4">
                  {(() => {
                    // Get processed frames from sessionStorage
                    const processedFramesIds = getExtractedFrames();
                    const processedFilteredFrames = filteredFrames.filter(frame =>
                      processedFramesIds.has(frame.screen_id)
                    ).length;
                    const totalFilteredFrames = filteredFrames.length;
                    const percentage = totalFilteredFrames > 0 ? (processedFilteredFrames / totalFilteredFrames) * 100 : 0;

                    return (
                      <>
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-gray-600 text-sm">
                            {processedFilteredFrames} of {totalFilteredFrames} screens processed
                          </p>
                          <span className="text-sm font-medium text-gray-900">
                            {processedFilteredFrames === totalFilteredFrames && totalFilteredFrames > 0
                              ? "Completed"
                              : `${Math.round(percentage)}%`}
                          </span>
                        </div>

                        {/* Progress bar */}
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                            style={{
                              width: `${Math.min(percentage, 100)}%`,
                            }}
                          ></div>
                        </div>
                      </>
                    );
                  })()}
                </div>
              )}
            </div>

            {/* Scrollable content area for frame list - UPDATED */}
            <div className="flex-1 overflow-hidden px-8">
              <div
                className="h-72 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-500 pr-2"
                style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#9ca3af #f3f4f6'
                }}
              >
                {isLoadingFigma ? (
                  <div className="text-center py-8">
                    <Loader2 size={24} className="mx-auto mb-2 text-orange-500 animate-spin" />
                    <p className="text-sm text-gray-600">Loading screens...</p>
                  </div>
                ) : filteredFrames && filteredFrames.length > 0 ? (
                  <div className="space-y-3 pb-6">
                    {filteredFrames.map((frame, index) => {
                      // Get processed frames from sessionStorage
                      const processedFramesIds = getExtractedFrames();
                      const isProcessed = processedFramesIds.has(frame.screen_id);

                      return (
                        <div
                          key={`${frame.screen_id}-${index}`}
                          className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg"
                          onMouseEnter={() => frame.image_url && handleJsonFileMouseEnter(frame)}
                          onMouseLeave={handleJsonFileMouseLeave}
                        >
                          <div className="flex items-center gap-3">
                            <div
                              className={`w-2 h-2 rounded-full ${isProcessed ? "bg-green-500" : "bg-gray-300"
                                }`}
                            ></div>

                            <span className="text-sm text-gray-900 font-medium">
                              {frame.screen_name || `Frame ${index + 1}`}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <p className="text-sm">No screens available</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Image Preview */}
        {hoveredImage && (
          <ImagePreview
            imageUrl={hoveredImage}
            isVisible={!!hoveredImage}
            mousePosition={mousePosition}
          />
        )}
      </div>
    </div>,
    document.body
  );
};

const useClickOutside = (ref, handler, exceptRef = null) => {
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        ref.current &&
        !ref.current.contains(event.target) &&
        !(
          exceptRef &&
          exceptRef.current &&
          exceptRef.current.contains(event.target)
        )
      ) {
        handler();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [ref, handler, exceptRef]);
};

const FileAttachmentPopup = ({
  files,
  onRemoveFile,
  onClearFiles,
  onAddMoreFiles,
  onClose,
  triggerButtonRef,
  isUploading,
  figmaFiles,
  onSelectFigmaFile,
  selectedFigmaFiles,
  isLoadingFigma,
  expandedFigmaDesigns,
  onToggleFigmaDesign,
  figmaJsonFiles,
  onSelectFigmaJson,
  selectedFigmaJsonFiles,
  isLoadingFigmaJson,
  onClearFigmaFiles,
  onAddFigmaDesign,
  processingDesigns,
  onCheckStatus,
  jsonProcessingStatus,
  initialActiveTab,
  lastUpdateTime,
  onRebuildFigmaDesign,
  figmaErrors,
}) => {
  const popupRef = useRef(null);
  const firstButtonRef = useRef(null);
  const [activeTab, setActiveTab] = useState(initialActiveTab);
  const [hoveredImage, setHoveredImage] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Update active tab when prop changes
  useEffect(() => {
    setActiveTab(initialActiveTab);
  }, [initialActiveTab]);

  // Handle click outside
  useClickOutside(popupRef, onClose, triggerButtonRef);

  // Handle mouse movement for image preview
  const handleMouseMove = (e) => {
    setMousePosition({ x: e.clientX, y: e.clientY });
  };

  // Handle mouse enter for JSON file with image
  const handleJsonFileMouseEnter = (jsonFile) => {
    if (jsonFile.image_url) {
      setHoveredImage(jsonFile.image_url);
    }
  };

  // Handle mouse leave for JSON file
  const handleJsonFileMouseLeave = () => {
    setHoveredImage(null);
  };

  // Focus management
  useEffect(() => {
    // Store the previously focused element
    const previousFocus = document.activeElement;

    // Focus the first interactive element in the popup
    if (firstButtonRef.current) {
      firstButtonRef.current.focus();
    }

    // Cleanup function to restore focus when component unmounts
    return () => {
      if (triggerButtonRef.current) {
        triggerButtonRef.current.focus();
      } else if (previousFocus) {
        previousFocus.focus();
      }
    };
  }, []);

  // Find the position of the trigger button to position the popup
  const calculatePosition = () => {
    if (!triggerButtonRef.current) return { top: "60px", left: "20px" };

    const rect = triggerButtonRef.current.getBoundingClientRect();
    return {
      bottom: `${window.innerHeight - rect.top + 10}px`,
      left: `${rect.left}px`,
    };
  };

  const position = calculatePosition();

  return ReactDOM.createPortal(
    <div
      className="fixed inset-0 z-[9999] pointer-events-none"
      aria-modal="true"
      role="dialog"
      aria-label="File attachments and Figma designs"
      onMouseMove={handleMouseMove}
    >
      <div
        ref={popupRef}
        className="absolute pointer-events-auto bg-white rounded-lg shadow-2xl border border-gray-300"
        style={{
          bottom: position.bottom,
          left: position.left,
          width: "350px",
          zIndex: 9999,
        }}
        tabIndex={-1}
      >
        {/* Header with tabs */}
        <div className="border-b border-gray-200 bg-gray-50">
          <div className="flex">
            <button
              ref={firstButtonRef}
              onClick={() => setActiveTab("files")}
              className={`flex-1 px-4 py-3 text-sm font-medium ${activeTab === "files"
                ? "text-primary-600 border-b-2 border-primary-600 bg-white"
                : "text-gray-600 hover:text-gray-800"
                }`}
            >
              Files ({files.length})
            </button>
            {/* <button
              onClick={() => setActiveTab("figma")}
              className={`flex-1 px-4 py-3 text-sm font-medium ${activeTab === "figma"
                ? "text-primary-600 border-b-2 border-primary-600 bg-white"
                : "text-gray-600 hover:text-gray-800"
                }`}
            >
              Figma ({selectedFigmaJsonFiles.length})
            </button> */}
          </div>
        </div>

        {/* Content area */}
        <div className="max-h-80 overflow-y-auto">
          {activeTab === "files" && (
            <div>
              {/* Files header */}
              <div className="flex justify-between items-center p-3 border-b border-gray-100">
                <h4 className="text-sm font-medium text-gray-700">
                  Attached Files
                </h4>
                <div className="flex gap-2">
                  <button
                    onClick={onAddMoreFiles}
                    className="text-primary hover:text-primary-700 text-sm font-medium"
                    disabled={isUploading}
                  >
                    + Add
                  </button>
                  {files.length > 0 && (
                    <button
                      onClick={onClearFiles}
                      className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                      disabled={isUploading}
                    >
                      Clear
                    </button>
                  )}
                </div>
              </div>

              {/* Files list */}
              {files.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  <FileText size={24} className="mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No files attached</p>
                  <button
                    onClick={onAddMoreFiles}
                    className="mt-2 text-primary hover:text-primary-700 text-sm font-medium"
                  >
                    Upload files
                  </button>
                </div>
              ) : (
                files.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 hover:bg-gray-50 border-b border-gray-100"
                  >
                    <div className="flex items-center gap-2 overflow-hidden">
                      <FileText
                        size={16}
                        className="text-gray-600 flex-shrink-0"
                      />
                      <div className="flex flex-col overflow-hidden">
                        <span className="text-sm text-gray-700 truncate">
                          {file.name}
                        </span>

                        {/* Show status indicators */}
                        {file.uploading && (
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Loader2
                              size={10}
                              className="text-primary animate-spin"
                            />
                            <span>Uploading...</span>
                          </div>
                        )}

                        {file.uploaded && (
                          <span className="text-xs text-green-600">
                            Uploaded
                          </span>
                        )}

                        {file.error && (
                          <span className="text-xs text-red-600">
                            {file.error}
                          </span>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => onRemoveFile(file)}
                      className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                      aria-label={`Remove file ${file.name}`}
                      disabled={file.uploading}
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))
              )}
            </div>
          )}

          {activeTab === "figma" && (
            <></>
            // <div>
            //   {/* Figma header */}
            //   <div className="flex justify-between items-center p-3 border-b border-gray-100">
            //     <h4 className="text-sm font-medium text-gray-700">
            //       Figma Designs
            //     </h4>
            //     <div className="flex gap-2">
            //       <button
            //         onClick={onAddFigmaDesign}
            //         disabled={
            //           figmaFiles.some((design) =>
            //             ["pending", "processing", "processing_wait"].includes(design.status)
            //           )
            //         }
            //         className={`text-sm font-medium flex items-center gap-1 ${figmaFiles.some((design) =>
            //           ["pending", "processing", "processing_wait"].includes(design.status)
            //         )
            //             ? "text-gray-400 cursor-not-allowed"
            //             : "text-primary hover:text-primary-700"
            //           }`}
            //       >
            //         <Plus size={12} />
            //         Add Design
            //       </button>

            //       {(selectedFigmaFiles.length > 0 ||
            //         selectedFigmaJsonFiles.length > 0) && (
            //           <button
            //             onClick={onClearFigmaFiles}
            //             className="text-gray-600 hover:text-gray-800 text-sm font-medium"
            //           >
            //             Clear All
            //           </button>
            //         )}
            //     </div>
            //   </div>

            //   {/* Figma content */}
            //   {isLoadingFigma ? (
            //     <div className="p-6 text-center">
            //       <Loader2
            //         size={24}
            //         className="mx-auto mb-2 text-primary animate-spin"
            //       />
            //       <p className="text-sm text-gray-600">
            //         Loading Figma designs...
            //       </p>
            //     </div>
            //   ) : figmaFiles.length === 0 ? (
            //     <div className="p-6 text-center text-gray-500">
            //       <div className="w-12 h-12 mx-auto mb-2 bg-gray-100 rounded-lg flex items-center justify-center">
            //         <span className="text-lg font-bold text-gray-400">F</span>
            //       </div>
            //       <p className="text-sm">No Figma designs added</p>
            //       <button
            //         onClick={onAddFigmaDesign}
            //         disabled={
            //           figmaFiles.some((design) =>
            //             ["pending", "processing", "processing_wait"].includes(design.status)
            //           )
            //         }
            //         className={`mt-2 text-sm font-medium ${figmaFiles.some((design) =>
            //           ["pending", "processing", "processing_wait"].includes(design.status)
            //         )
            //             ? "text-gray-400 cursor-not-allowed"
            //             : "text-primary hover:text-primary-700"
            //           }`}
            //       >
            //         Add Figma Design
            //       </button>
            //     </div>
            //   ) : (
            //     figmaFiles.map((design) => {
            //       const isProcessing = processingDesigns.has(design.id);
            //       const jsonStatus = jsonProcessingStatus[design.id];
            //       const getStatusColor = (status) => {
            //         switch (status) {
            //           case "completed":
            //             return "bg-green-100 text-green-800";
            //           case "processing":
            //             return "bg-primary-100 text-primary-800";
            //           case "processing_wait":
            //             return "bg-purple-100 text-purple-800";
            //           case "partially_completed":
            //             return "bg-yellow-100 text-yellow-800";
            //           case "failed":
            //             return "bg-red-100 text-red-800";
            //           default:
            //             return "bg-gray-100 text-gray-800";
            //         }
            //       };

            //       return (
            //         <div
            //           key={`${design.id}-${lastUpdateTime}`}
            //           className="border-b border-gray-100 last:border-b-0"
            //         >
            //           <div className="flex items-center justify-between p-3 hover:bg-gray-50">
            //             <div className="flex items-center gap-3 flex-1 min-w-0">
            //               <div className="w-8 h-8 bg-purple-100 rounded flex items-center justify-center flex-shrink-0">
            //                 <span className="text-xs font-bold text-purple-600">
            //                   F
            //                 </span>
            //               </div>
            //               <div className="flex-1 min-w-0">
            //                 <p className="text-sm font-medium text-gray-900 truncate">
            //                   {design.name}
            //                 </p>
            //                 <div className="flex items-center gap-2">
            //                   <span
            //                     className={`text-xs px-2 py-1 rounded-full ${getStatusColor(
            //                       design.status
            //                     )}`}
            //                   >
            //                     {design.status === "failed" && (figmaErrors[design.id] || design.error_message)
            //                       ? "Error - Click rebuild"
            //                       : design.status?.replace("_", " ")}
            //                     {["processing_wait", "processing"].includes(
            //                       design.status
            //                     ) &&
            //                       jsonStatus &&
            //                       jsonStatus.percentage && (
            //                         <span className="ml-1 text-primary-800">
            //                           ({jsonStatus.percentage}%)
            //                         </span>
            //                       )}
            //                   </span>
            //                   {design.completed_frames !== undefined &&
            //                     design.total_frames !== undefined && (
            //                       <span className="text-xs text-gray-500">
            //                         {design.completed_frames}/
            //                         {design.total_frames} frames
            //                       </span>
            //                     )}
            //                 </div>
            //               </div>
            //             </div>
            //             <div className="flex items-center gap-2">
            //               {/* {(isProcessing ||
            //                 [
            //                   "pending",
            //                   "processing",
            //                   "processing_wait",
            //                 ].includes(design.status)) && (
            //                   // <button
            //                   //   onClick={() => onCheckStatus(design)}
            //                   //   className="text-primary hover:text-primary-700 text-xs"
            //                   //   title="Check status"
            //                   // >
            //                   //   <Info size={14} />
            //                   // </button>
            //                 )} */}
            //               {/* Rebuild icon for failed designs */}

            //               {(figmaErrors[design.id] || design.status === "failed") && (
            //                 <button
            //                   onClick={() => onRebuildFigmaDesign(design.id)}
            //                   className="text-orange-500 hover:text-orange-700 text-xs"
            //                   title="Rebuild design"
            //                 >
            //                   <RefreshCw size={14} />
            //                 </button>

            //               )}
            //               <button
            //                 onClick={() => onToggleFigmaDesign(design.id)}
            //                 className="text-gray-500 hover:text-gray-700"
            //                 aria-label={`${expandedFigmaDesigns.includes(design.id)
            //                   ? "Hide"
            //                   : "Show"
            //                   } JSON files`}
            //               >
            //                 {expandedFigmaDesigns.includes(design.id)
            //                   ? "▼"
            //                   : "▶"}
            //               </button>
            //             </div>
            //           </div>

            //           {/* JSON files list */}
            //           {expandedFigmaDesigns.includes(design.id) && (
            //             <div className="ml-4 border-l border-gray-200">
            //               {isLoadingFigmaJson &&
            //                 figmaJsonFiles[design.id] === undefined ? (
            //                 <div className="p-3 text-center">
            //                   <Loader2
            //                     size={16}
            //                     className="mx-auto text-primary animate-spin"
            //                   />
            //                   <p className="text-xs text-gray-500 mt-1">
            //                     Loading JSON files...
            //                   </p>
            //                 </div>
            //               ) : figmaJsonFiles[design.id] &&
            //                 figmaJsonFiles[design.id].length > 0 ? (
            //                 figmaJsonFiles[design.id].map((jsonFile, index) => {
            //                   const fileKey = `${design.id}-${jsonFile.filename}`;
            //                   const selectedFile = selectedFigmaJsonFiles.find(
            //                     (f) => f.fileKey === fileKey
            //                   );
            //                   const isSelected = !!selectedFile;

            //                   return (
            //                     <div
            //                       key={index}
            //                       className="flex items-center justify-between p-2 pl-4 hover:bg-gray-50"
            //                       onMouseEnter={() =>
            //                         handleJsonFileMouseEnter(jsonFile)
            //                       }
            //                       onMouseLeave={handleJsonFileMouseLeave}
            //                     >
            //                       <div className="flex items-center gap-2 flex-1 min-w-0">
            //                         <div className="w-4 h-4 bg-primary-100 rounded flex items-center justify-center flex-shrink-0">
            //                           <span className="text-xs font-bold text-primary">
            //                             J
            //                           </span>
            //                         </div>
            //                         <div className="flex flex-col overflow-hidden">
            //                           <div className="flex items-center gap-1">
            //                             <span className="text-xs text-gray-700 truncate">
            //                               {jsonFile.frame_name}
            //                             </span>
            //                             {jsonFile.image_url && (
            //                               <div
            //                                 className="w-2 h-2 bg-primary-400 rounded-full flex-shrink-0"
            //                                 title="Has preview image"
            //                               />
            //                             )}
            //                           </div>
            //                           {jsonFile.frame_id && (
            //                             <span className="text-xs text-gray-500">
            //                               Frame: {jsonFile.frame_id}
            //                             </span>
            //                           )}

            //                           {/* Show status indicators */}
            //                           {selectedFile?.uploading && (
            //                             <div className="flex items-center gap-1 text-xs text-gray-500">
            //                               <Loader2
            //                                 size={8}
            //                                 className="text-primary animate-spin"
            //                               />
            //                               <span>Uploading...</span>
            //                             </div>
            //                           )}

            //                           {selectedFile?.uploaded && (
            //                             <span className="text-xs text-green-600">
            //                               Uploaded
            //                             </span>
            //                           )}

            //                           {selectedFile?.error && (
            //                             <span className="text-xs text-red-600">
            //                               {selectedFile.error}
            //                             </span>
            //                           )}
            //                         </div>
            //                       </div>
            //                       <button
            //                         onClick={() =>
            //                           onSelectFigmaJson(design.id, jsonFile)
            //                         }
            //                         disabled={selectedFile?.uploading}
            //                         className={`px-2 py-1 text-xs font-medium rounded ${isSelected
            //                           ? selectedFile?.uploaded
            //                             ? "bg-green-100 text-green-700 border border-green-200"
            //                             : selectedFile?.error
            //                               ? "bg-red-100 text-red-700 border border-red-200"
            //                               : "bg-primary-100 text-primary-700 border border-primary-200"
            //                           : "bg-gray-100 text-gray-600 hover:bg-gray-200"
            //                           } ${selectedFile?.uploading
            //                             ? "opacity-50 cursor-not-allowed"
            //                             : ""
            //                           }`}
            //                       >
            //                         {selectedFile?.uploading ? (
            //                           <Loader2
            //                             size={10}
            //                             className="animate-spin"
            //                           />
            //                         ) : selectedFile?.uploaded ? (
            //                           "✓"
            //                         ) : selectedFile?.error ? (
            //                           "✗"
            //                         ) : isSelected ? (
            //                           "✓"
            //                         ) : (
            //                           "+"
            //                         )}
            //                       </button>
            //                     </div>
            //                   );
            //                 })
            //               ) : (
            //                 <div className="p-3 text-center">
            //                   <p className="text-xs text-gray-500">
            //                     No JSON files available
            //                   </p>
            //                 </div>
            //               )}
            //             </div>
            //           )}
            //         </div>
            //       );
            //     })
            //   )}
            // </div>
          )}
        </div>
      </div>

      {/* Image Preview */}
      <ImagePreview
        imageUrl={hoveredImage}
        isVisible={!!hoveredImage}
        mousePosition={mousePosition}
      />
    </div>,
    document.body
  );
};

// Main ChatInput component
const ChatInput = ({
  isStopped,
  // inputValue,
  // setInputValue,
  handleSendMessage,
  isReady,
  textAreaRef,
  activeReplyTo,
  isAiTyping,
  wsConnection,
}) => {
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [showAttachments, setShowAttachments] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  // const [uploadedAttachments, setUploadedAttachments] = useState([]);
  const [isMultiline, setIsMultiline] = useState(false);
  const [figmaFiles, setFigmaFiles] = useState([]);
  const [selectedFigmaFiles, setSelectedFigmaFiles] = useState([]);
  const [isLoadingFigma, setIsLoadingFigma] = useState(false);
  const [figmaFilesLoaded, setFigmaFilesLoaded] = useState(false);
  const [expandedFigmaDesigns, setExpandedFigmaDesigns] = useState([]);
  const [figmaJsonFiles, setFigmaJsonFiles] = useState({});
  const [selectedFigmaJsonFiles, setSelectedFigmaJsonFiles] = useState([]);
  const [isLoadingFigmaJson, setIsLoadingFigmaJson] = useState(false);
  const [uploadedJsonAttachments, setUploadedJsonAttachments] = useState([]);
  const [jsonProcessingStatus, setJsonProcessingStatus] = useState({});
  const [showProcessing, setShowProcessing] = useState(false);

  // New state for Figma design addition
  const [showAddFigmaModal, setShowAddFigmaModal] = useState(false);
  const [isAddingFigma, setIsAddingFigma] = useState(false);
  const [processingDesigns, setProcessingDesigns] = useState(new Set());
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [currentProgressDesign, setCurrentProgressDesign] = useState(null);
  const [processingStatus, setProcessingStatus] = useState(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  const [showAttachmentModal, setShowAttachmentModal] = useState(false);

  // WebSocket connection state
  const [wsConnectionState, setWsConnectionState] = useState("disconnected");
  const chatWsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const isComponentMountedRef = useRef(true);

  // Polling state and refs
  const pollingIntervalRef = useRef(null);
  const isPollingActiveRef = useRef(false);
  const lastPollingCheckRef = useRef(0);

  // Force re-render state for WebSocket updates
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());

  const searchParams = useSearchParams();
  const [isStop, setIsStop] = useState(false);
  const fileInputRef = useRef(null);
  const plusButtonRef = useRef(null);
  const [initialActiveTab, setInitialActiveTab] = useState("files");
  const [figmaErrors, setFigmaErrors] = useState({});
  const { showAlert } = useContext(AlertContext)
  const [showInitialProcessing, setShowInitialProcessing] = useState(false);
  const [showFrameSelector, setShowFrameSelector] = useState(false);
  const [framesList, setFramesList] = useState(null)
  const [frameId, setFrameId] = useState(null)
  const [designList, setDesignList] = useState(null)
  const [showDesignSelector, setShowDesignSelector] = useState(null)
  const [enablebackDesign, setEnableBackDesign] = useState(null)
  const [figmaList, setFigmaList] = useState([])

  // Get project ID from URL
  const { projectId } = useParams();
  const { switchToFigmaTab, uploadedAttachments, setUploadedAttachments, inputValue, setInputValue, setFigmaLoader, figmaData } = useCodeGeneration();

  // State to track if user has sent first message (for animation control)
  const [hasUserSentFirstMessage, setHasUserSentFirstMessage] = useState(false);

  // States for @ attachment selector
  const [showAttachmentSelector, setShowAttachmentSelector] = useState(false);
  const [attachmentSelectorPosition, setAttachmentSelectorPosition] = useState({ bottom: 0, left: 0 });
  const [cursorPosition, setCursorPosition] = useState(0);
  const [selectedAttachmentForSend, setSelectedAttachmentForSend] = useState(null);
  const [currentSearchQuery, setCurrentSearchQuery] = useState("");
  const [textAreaHeight, setTextAreaHeight] = useState("43px");

  // FIXED: Polling logic for Figma files - only start when status is processing_wait
  const startPolling = useCallback(() => {
    if (isPollingActiveRef.current || !projectId) return;

    
    isPollingActiveRef.current = true;

    const pollFigmaFiles = async () => {
      try {
        // Throttle polling to avoid too frequent calls
        const now = Date.now();
        if (now - lastPollingCheckRef.current < 4000) {
          return;
        }
        lastPollingCheckRef.current = now;

        
        const response = await getFigmaFiles(projectId);

        if (response.designs) {
          // Check if there are any active processing designs from FRESH data
          const hasActiveDesigns = response.designs.some((design) =>
            ["processing", "processing_wait"].includes(design.status)
          );

          if (!hasActiveDesigns) {
            
            stopPolling();
            return;
          }

          setFigmaFiles((prevFiles) => {
            const newFiles = response.designs;

            // Simplified merging - prioritize fresh API data while preserving recent WebSocket updates
            const mergedFiles = newFiles.map((newFile) => {
              const existingFile = prevFiles.find((f) => f.id === newFile.id);

              if (existingFile && existingFile._websocket_update) {
                // If WebSocket update is very recent (within 10 seconds), preserve it
                const wsUpdateTime = new Date(existingFile.time_updated || 0).getTime();
                const now = Date.now();
                const isRecentWSUpdate = (now - wsUpdateTime) < 10000;

                if (isRecentWSUpdate) {
                  
                  return {
                    ...newFile, // Fresh API data as base
                    ...existingFile, // Recent WebSocket data takes priority
                    time_updated: existingFile.time_updated || newFile.time_updated,
                  };
                }
              }

              // Use fresh API data for most cases
              return {
                ...newFile,
                time_updated: newFile.time_updated || new Date().toISOString(),
              };
            });


            // Update processing designs state based on current statuses
            setProcessingDesigns((prev) => {
              const newSet = new Set();
              mergedFiles.forEach((file) => {
                if (["processing", "processing_wait"].includes(file.status)) {
                  newSet.add(file.id);
                }
              });
              return newSet;
            });

            // Check if all designs are now completed or failed
            const allDesignsComplete = mergedFiles.every((design) =>
              ["completed", "failed", "partially_completed"].includes(
                design.status
              )
            );

            if (allDesignsComplete) {
              
              setTimeout(() => stopPolling(), 1000);
            }

            // Force re-render with timestamp
            setLastUpdateTime(Date.now());

            return mergedFiles;
          });

          // Reload JSON files for expanded designs after polling
          setTimeout(() => {
            // Use current state instead of closure variable
            setExpandedFigmaDesigns(currentExpanded => {
              currentExpanded.forEach((designId) => {
                const design = response.designs.find((f) => f.id === designId);
                if (design && design.file_key) {
                  
                  loadFigmaJsonFiles(designId, design.file_key);
                }
              });
              return currentExpanded; // Return same state, just using for side effect
            });
          }, 200);
        }
      } catch (error) {
        // console.error("ChatInput: Error polling Figma files:", error);
        // DON'T stop polling on error - continue trying
      }
    };

    // Start polling immediately, then every 5 seconds
    pollFigmaFiles();
    pollingIntervalRef.current = setInterval(pollFigmaFiles, 20000);
  }, [projectId]);

  const stopPolling = useCallback(() => {
    if (!isPollingActiveRef.current) return;

    
    isPollingActiveRef.current = false;

    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  }, []);
  useEffect(() => {
    const renderFigma = async () => {
      try {
        const initialResult = await getFigmaDesignList(projectId);
        if (!initialResult?.data?.length) {
          return;
        }
        const fullFigmaId = initialResult.data[0].figma_id;
        const parts = fullFigmaId.split("-");
        const shortFigmaId = parts[parts.length - 1];
        const designDetailResult = await getFigmaDesignList(projectId, fullFigmaId);
        const screenList = designDetailResult?.data?.screen_list || [];
        const proceedScreens = screenList.filter(screen => screen.processed === true);
        const screenIds = proceedScreens.map(screen => screen.screen_id);
        const figmaJsonData = await getFigmaFilesScreens(projectId, shortFigmaId, screenIds);
        const combinedData = screenList
          .filter((screen) => figmaJsonData[screen.screen_id]) // Only include processed/available screens
          .map((screen) => ({
            screen_id: screen.screen_id,
            screen_name: screen.screen_name,
            image_url: figmaJsonData[screen.screen_id]
          }));
        setFigmaList(combinedData)
      } catch (error) {
        console.error("error in fetching the figma details", error)
      }
    }
    renderFigma()
  }, [projectId])

  // FIXED: Remove auto-start polling on page load
  // Only start polling when explicitly needed (WebSocket updates or user actions)

  // Load Figma files when component mounts - but DON'T start polling
  useEffect(() => {
    const loadFigmaFiles = async () => {
      if (!projectId || figmaFilesLoaded) return;

      setIsLoadingFigma(true);
      try {
        const response = await getFigmaFiles(projectId);
        setFigmaFiles(response.designs || []);
        setFigmaFilesLoaded(true);

        // Update processing designs state but DON'T start polling
        // Only track processing_wait designs as potential polling triggers
        const activeDesigns = response.designs?.filter((design) =>
          design.status === "processing_wait"
        );

        if (activeDesigns?.length > 0) {
          
          setProcessingDesigns(new Set(activeDesigns.map(d => d.id)));
        }
      } catch (error) {
        console.error("Error loading Figma files:", error);
        setFigmaFiles([]);
      } finally {
        setIsLoadingFigma(false);
      }
    };

    loadFigmaFiles();
  }, [projectId, figmaFilesLoaded]);

  // Initialize hasUserSentFirstMessage from localStorage
  useEffect(() => {
    if (projectId && typeof window !== 'undefined') {
      const stored = localStorage.getItem(`hasUserSentFirstMessage_${projectId}`);
      if (stored === 'true') {
        setHasUserSentFirstMessage(true);
      }
    }
  }, [projectId]);


  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  // WebSocket connection functions (adapted from page.jsx)
  const connectWebSocket = useCallback(() => {
    // Clear any existing reconnection timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Don't connect if component is unmounted
    if (!isComponentMountedRef.current) {
      return;
    }

    // Check if already connected or connecting
    if (chatWsRef.current?.readyState === WebSocket.OPEN) {
      
      setWsConnectionState("connected");
      return;
    }

    if (chatWsRef.current?.readyState === WebSocket.CONNECTING) {
      
      return;
    }

    // Close existing connection if it exists
    if (chatWsRef.current) {
      chatWsRef.current.close();
    }

    try {
      
      setWsConnectionState("connecting");

      const ws = new WebSocket(
        `${process.env.NEXT_PUBLIC_WS_URL}/figma-${projectId}`
      );
      chatWsRef.current = ws;

      // Connection timeout
      const connectionTimeout = setTimeout(() => {
        if (ws.readyState === WebSocket.CONNECTING) {
          
          ws.close();
          handleReconnection();
        }
      }, 10000); // 10 seconds timeout

      ws.onopen = () => {
        
        clearTimeout(connectionTimeout);
        setWsConnectionState("connected");

        // Send initialization message
        ws.send(
          JSON.stringify({
            type: "client",
            task_id: `figma-${projectId}`,
          })
        );
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);

          if (message.type === "figma_update") {
            const { figma_id, update_data } = message.data;

            

            // Update figma files list with WebSocket data (high priority)
            setFigmaFiles((prevFiles) =>
              prevFiles.map((file) =>
                file.id === figma_id
                  ? {
                    ...file,
                    ...update_data,
                    // Ensure WebSocket updates are preserved with timestamp
                    time_updated:
                      update_data.time_updated || new Date().toISOString(),
                    // Mark as WebSocket update for merge priority
                    _websocket_update: true,
                  }
                  : file
              )
            );

            // Update processing designs state (for both processing and processing_wait)
            setProcessingDesigns((prev) => {
              const newSet = new Set(prev);
              if (["processing", "processing_wait"].includes(update_data.status)) {
                newSet.add(figma_id);
              } else {
                newSet.delete(figma_id);
              }
              return newSet;
            });

            // Polling removed - relying on WebSocket figma_json_files messages for updates

            // Update processing status if modal is open for this design
            if (currentProgressDesign?.id === figma_id) {
              setProcessingStatus((prev) => ({
                ...prev,
                ...update_data,
              }));
            }
            // If status is completed, refresh the entire figma files list
            if (
              update_data.status === "completed" ||
              update_data.status === "partially_completed"
            ) {
              refreshFigmaFiles();
            }

            // Force re-render
            setLastUpdateTime(Date.now());
          } else if (message.type === "figma_update_json") {
            const { figma_id, update_data } = message.data;

            

            // Update JSON processing status
            setJsonProcessingStatus((prev) => {
              const newStatus = {
                ...prev,
                [figma_id]: {
                  processed_count: update_data.processed_count,
                  total_count: update_data.total_count,
                  percentage: update_data.percentage,
                  time_updated: update_data.time_updated,
                },
              };
              return newStatus;
            });

            // Force re-render
            setLastUpdateTime(Date.now());
          } else if (message.type === "figma_json_files") {
            
            const { figma_id, updated_files } = message.data;

            

            // Update figma files list with JSON files data
            setFigmaFiles((prevFiles) =>
              prevFiles.map((file) =>
                file.id === figma_id
                  ? {
                    ...file,
                    json_files: updated_files.files || [],
                    total_json_files: updated_files.total_files || updated_files.files?.length || 0,
                    json_directory_path: updated_files.directory_path,
                    valid_frame_ids: updated_files.valid_frame_ids || [],
                    total_valid_frames: updated_files.total_valid_frames || updated_files.files?.length || 0,
                    time_updated: new Date().toISOString(),
                    _websocket_update: true,
                  }
                  : file
              )
            );

            // Update figmaJsonFiles state to ensure the modal gets the new files
            setFigmaJsonFiles((prevJsonFiles) => ({
              ...prevJsonFiles,
              [figma_id]: updated_files.files || []
            }));

            // Force re-render
            setLastUpdateTime(Date.now());
          }
          else if (message.type === "figma_error") {
            const { figma_id, Error_message } = message.data;

            

            // Update figma errors state
            setFigmaErrors((prev) => ({
              ...prev,
              [figma_id]: Error_message,
            }));

            // Update figma files to show error status
            setFigmaFiles((prevFiles) =>
              prevFiles.map((file) =>
                file.id === figma_id
                  ? {
                    ...file,
                    status: "failed",
                    error_message: Error_message,
                    _websocket_update: true,
                  }
                  : file
              )
            );

            // Remove from processing designs
            setProcessingDesigns((prev) => {
              const newSet = new Set(prev);
              newSet.delete(figma_id);
              return newSet;
            });

            // Force re-render
            setLastUpdateTime(Date.now());
          }
          else if (message.type === "cleanup_attachments_response") {
            console.error('Cleanup attachments response:', message.data);
            const responseData = message.data || {};

            if (responseData.status === 'success') {
              console.error(`Successfully cleaned up ${responseData.total_cleaned} files from attachments folder`);
            } else if (responseData.status === 'partial') {
              console.error(`Partially cleaned up: ${responseData.total_cleaned} success, ${responseData.total_failed} failed`);
            } else if (responseData.status === 'error') {
              console.error('Cleanup error:', responseData.message);
            }
          }


        } catch (error) {
          // console.error("ChatInput: WebSocket message error:", error);
        }
      };

      ws.onerror = (error) => {
        // console.error("ChatInput: WebSocket error:", error);
        clearTimeout(connectionTimeout);
        setWsConnectionState("disconnected");
      };

      ws.onclose = (event) => {
        
        clearTimeout(connectionTimeout);
        setWsConnectionState("disconnected");

        // Only attempt to reconnect if the closure wasn't intentional and component is still mounted
        if (isComponentMountedRef.current && event.code !== 1000) {
          handleReconnection();
        }
      };
    } catch (error) {
      // console.error("ChatInput: Error creating WebSocket connection:", error);
      setWsConnectionState("disconnected");
      handleReconnection();
    }
  }, [projectId]);

  // Handle reconnection logic
  const handleReconnection = useCallback(() => {
    if (!isComponentMountedRef.current) {
      return;
    }

    // Clear any existing timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    // Check if there are processing designs that need updates (both processing and processing_wait)
    const hasActiveDesigns = figmaFiles.some((design) =>
      ["processing", "processing_wait"].includes(design.status)
    );

    if (hasActiveDesigns) {
      
      reconnectTimeoutRef.current = setTimeout(() => {
        connectWebSocket();
      }, 3000);
    } else {
      
    }
  }, [figmaFiles, connectWebSocket]);

  // Function to ensure WebSocket is connected
  const ensureWebSocketConnection = useCallback(() => {
    return new Promise((resolve) => {
      if (chatWsRef.current?.readyState === WebSocket.OPEN) {
        
        resolve(true);
        return;
      } else {
        connectWebSocket();
      }

      

      // Wait for connection or timeout
      const checkConnection = () => {
        if (chatWsRef.current?.readyState === WebSocket.OPEN) {
          
          resolve(true);
        } else if (wsConnectionState === "disconnected") {
          
          resolve(false);
        } else {
          // Still connecting, check again
          setTimeout(checkConnection, 500);
        }
      };

      setTimeout(checkConnection, 500);
    });
  }, [connectWebSocket, wsConnectionState]);

  // WebSocket connection for real-time updates
  useEffect(() => {
    isComponentMountedRef.current = true;

    if (projectId) {
      connectWebSocket();
    }

    return () => {
      isComponentMountedRef.current = false;

      // Clear timeouts
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }

      // Close WebSocket
      if (chatWsRef.current) {
        chatWsRef.current.close();
      }

      // Stop polling
      stopPolling();
    };
  }, [projectId, connectWebSocket, stopPolling]);

  // Handle adding new Figma design
  const handleAddFigmaDesign = async () => {
    // Ensure WebSocket connection before opening modal
    
    try {
      const response = await getFigmaDesignList(projectId)
      if (response?.data.length > 0) {
        setDesignList(response.data);
        setShowAddFigmaModal(true);
        setShowDesignSelector(true);
        setShowProcessing(false);
        setShowFrameSelector(false);
        setShowInitialProcessing(false);
        setEnableBackDesign(true)

      }
      else {
        setDesignList([]);
        setShowAddFigmaModal(true);
        setShowInitialProcessing(false);
        setShowProcessing(false);
        setShowDesignSelector(false);
        setShowFrameSelector(false);
        setEnableBackDesign(false)

      }
    } catch (error) {
      console.error("error in listing designs", error)
    }
    // await ensureWebSocketConnection();

  };

  // Handle Figma design submission
  const handleFigmaDesignSubmit = async (designName, figmaLink) => {
    setIsAddingFigma(true);
    try {
      // Ensure WebSocket connection before processing
      
      await ensureWebSocketConnection();

      // const response = await addFigmaFileV2(projectId, designName, figmaLink, searchParams.get("task_id"));
      const figmaData = {
        name: "",
        url: figmaLink
      };

      const extractedResponse = await getFramesList(projectId, figmaData)

      // if (response?.message) {
      //   showAlert(response.message, "success");

      // }
      if (extractedResponse?.screen_list) {
        setFramesList(extractedResponse?.screen_list)
        setFrameId(extractedResponse?.id)
        setShowFrameSelector(true)
        setShowProcessing(false);
        setShowInitialProcessing(false)
      }
      if (extractedResponse?.screen_list) {
        // // Add to figma files list
        // const newDesign = {
        //   id: response.id,
        //   name: designName,
        //   url: figmaLink,
        //   status: "pending",
        //   completed_frames: 0,
        //   total_frames: 0,
        //   failed_frames: 0,
        //   time_created: new Date().toISOString(),
        //   added_by: { name: "You" },
        // };

        // setFigmaFiles((prev) => [newDesign, ...prev]);


        

        // DON'T start polling here - wait for WebSocket to indicate processing has started
        // Ensure WebSocket connection is established
        if (
          !chatWsRef.current ||
          chatWsRef.current.readyState !== WebSocket.OPEN
        ) {
          connectWebSocket();
        }
      } else {
        console.error(
          "ChatInput: Failed to add Figma design:"
        );
      }
    } catch (error) {
      // console.error("ChatInput: Error adding Figma design:", error);
      showAlert(error.message, "error");
      setShowInitialProcessing(false)
      setShowProcessing(false);
    } finally {
      setIsAddingFigma(false);
    }
  };
  // Handle rebuilding a failed Figma design

  const handleRebuildFigmaDesign = async (designId) => {

    try {
      
      // Clear the error state for this design

      setFigmaErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[designId];
        return newErrors;

      });

      // Update the design status to processing
      setFigmaFiles((prevFiles) =>
        prevFiles.map((file) =>

          file.id === designId
            ? {
              ...file,
              status: "processing",
              error_message: undefined,
              _websocket_update: true,

            }

            : file

        )

      );
      // Add back to processing designs

      setProcessingDesigns((prev) => new Set(prev).add(designId));

      // Ensure WebSocket connection before rebuilding

      await ensureWebSocketConnection();

      const response = reloadFigmaDesign(projectId, designId, searchParams.get("task_id"))

      
      // Start polling for updates

      startPolling();
    } catch (error) {

      console.error('Error rebuilding Figma design:', error);

      // Restore error state if rebuild failed

      setFigmaErrors((prev) => ({
        ...prev,
        [designId]: "Failed to restart processing. Please try again.",
      }));

      // Update design status back to failed

      setFigmaFiles((prevFiles) =>
        prevFiles.map((file) =>
          file.id === designId
            ? {
              ...file,
              status: "failed",
              error_message: "Failed to restart processing. Please try again.",
              _websocket_update: true,

            }
            : file
        )
      );
      // Remove from processing designs
      setProcessingDesigns((prev) => {
        const newSet = new Set(prev);
        newSet.delete(designId);
        return newSet;
      });
    }
  };

  // Handle checking status
  const handleCheckStatus = async (design) => {
    setCurrentProgressDesign(design);
    setShowProgressModal(true);
    setIsLoadingStatus(true);

    try {
      const status = await getFigmaProcessingStatus(projectId, design.id);
      setProcessingStatus(status);
    } catch (error) {
      // console.error("Error fetching status:", error);
    } finally {
      setIsLoadingStatus(false);
    }
  };

  // Handle Figma file selection
  const handleSelectFigmaFile = (design, clearAll = false) => {
    if (clearAll) {
      setSelectedFigmaFiles([]);
      return;
    }

    setSelectedFigmaFiles((prev) => {
      const isSelected = prev.some((f) => f.id === design.id);
      if (isSelected) {
        return prev.filter((f) => f.id !== design.id);
      } else {
        return [...prev, design];
      }
    });
  };

  const handleClearFigmaFiles = () => {
    setSelectedFigmaFiles([]);
    setSelectedFigmaJsonFiles([]);
    setUploadedJsonAttachments([]);
    setFigmaErrors({});
  };

  // Handle Figma design expansion/collapse with intelligent loading
  const handleToggleFigmaDesign = async (designId) => {
    setExpandedFigmaDesigns((prev) => {
      const isExpanded = prev.includes(designId);
      if (isExpanded) {
        return prev.filter((id) => id !== designId);
      } else {
        // Load JSON files if not already loaded
        const design = figmaFiles.find((f) => f.id === designId);
        if (design && design.file_key && !figmaJsonFiles[designId]) {
          
          loadFigmaJsonFiles(designId, design.file_key);
        } else if (figmaJsonFiles[designId]) {
          
        }
        return [...prev, designId];
      }
    });
  };

  // Load JSON files for a specific Figma design
  const loadFigmaJsonFiles = async (designId, fileKey) => {
    if (!projectId || !fileKey) return;

    
    setIsLoadingFigmaJson(true);
    try {
      const response = await getFigmaJsonFiles(projectId, fileKey);
      
      setFigmaJsonFiles((prev) => ({
        ...prev,
        [designId]: response.files || [],
      }));
    } catch (error) {
      console.error("Error loading Figma JSON files:", error);
      setFigmaJsonFiles((prev) => ({
        ...prev,
        [designId]: [],
      }));
    } finally {
      setIsLoadingFigmaJson(false);
    }
  };

  // Enhanced refresh figma files list with automatic JSON reload
  const refreshFigmaFiles = async () => {
    if (!projectId) return;

    try {
      const response = await getFigmaFiles(projectId);
      setFigmaFiles((prevFiles) => {
        const newFiles = response.designs || [];

        // Preserve WebSocket updates by merging with existing data
        const mergedFiles = newFiles.map((newFile) => {
          const existingFile = prevFiles.find((f) => f.id === newFile.id);
          if (existingFile) {
            // If existing file has WebSocket updates, prioritize them
            if (existingFile._websocket_update) {
              
              return {
                ...newFile, // API data (base)
                ...existingFile, // WebSocket updates (priority)
                // Always update these fields from API
                completed_frames:
                  newFile.completed_frames || existingFile.completed_frames,
                total_frames: newFile.total_frames || existingFile.total_frames,
                failed_frames:
                  newFile.failed_frames || existingFile.failed_frames,
                file_key: newFile.file_key || existingFile.file_key,
                time_updated: existingFile.time_updated || newFile.time_updated,
              };
            } else {
              // No WebSocket updates, use API data
              return {
                ...existingFile, // Existing data
                ...newFile, // API updates
                time_updated: newFile.time_updated || existingFile.time_updated,
              };
            }
          }
          return newFile;
        });

        // Update processing designs state based on current statuses
        setProcessingDesigns((prev) => {
          const newSet = new Set();
          mergedFiles.forEach((file) => {
            if (
              ["processing", "processing_wait"].includes(file.status)
            ) {
              newSet.add(file.id);
            }
          });
          return newSet;
        });

        // Clear figma errors for designs that are no longer failed

        setFigmaErrors((prev) => {
          const newErrors = { ...prev };
          mergedFiles.forEach((file) => {
            if (file.status !== "failed" && newErrors[file.id]) {
              delete newErrors[file.id];
            }
          });

          return newErrors;
        });
        return mergedFiles;
      });

      // Clear ALL JSON files cache and reload for expanded designs
      setFigmaJsonFiles({});

      // After clearing cache, reload JSON files for currently expanded designs
      setTimeout(() => {
        expandedFigmaDesigns.forEach((designId) => {
          const design = response.designs?.find((f) => f.id === designId);
          if (design && design.file_key) {
            
            loadFigmaJsonFiles(designId, design.file_key);
          }
        });
      }, 100);
    } catch (error) {
      console.error("Error refreshing Figma files:", error);
    }
  };

  // Handle Figma JSON file selection
  const handleSelectFigmaJson = async (
    designId,
    jsonFile,
    clearAll = false
  ) => {
    if (clearAll) {
      setSelectedFigmaJsonFiles([]);
      setUploadedJsonAttachments([]);
      return;
    }

    const fileKey = `${designId}-${jsonFile.filename}`;
    const isSelected = selectedFigmaJsonFiles.some(
      (f) => f.fileKey === fileKey
    );

    if (isSelected) {
      // Remove from selection
      setSelectedFigmaJsonFiles((prev) =>
        prev.filter((f) => f.fileKey !== fileKey)
      );
      setUploadedJsonAttachments((prev) =>
        prev.filter((f) => f.fileKey !== fileKey)
      );
    } else {
      // Add to selection with uploading status
      const jsonFileObj = {
        fileKey,
        designId,
        filename: jsonFile.filename,
        path: jsonFile.path,
        relativePath: jsonFile.relative_path,
        uploading: true,
        uploaded: false,
        error: null,
      };

      setSelectedFigmaJsonFiles((prev) => [...prev, jsonFileObj]);

      // Start upload process
      try {
        // Upload using file path approach
        const uploadResult = await uploadMultipleAttachments(
          [
            {
              path: jsonFile.path,
              filename: jsonFile.filename,
            },
          ],
          projectId
        );

        if (uploadResult[0] && uploadResult[0].success !== false) {
          // Update status to uploaded
          setSelectedFigmaJsonFiles((prev) =>
            prev.map((f) =>
              f.fileKey === fileKey
                ? {
                  ...f,
                  uploading: false,
                  uploaded: true,
                  attachmentId: uploadResult[0].attachment_id,
                }
                : f
            )
          );

          // Add to uploaded attachments
          setUploadedJsonAttachments((prev) => [
            ...prev,
            {
              fileKey,
              attachment_id: uploadResult[0].attachment_id,
              filename: uploadResult[0].filename,
              file_location:
                uploadResult[0].file_location || uploadResult[0].path,
              size: uploadResult[0].size,
            },
          ]);
        } else {
          // Handle upload failure
          setSelectedFigmaJsonFiles((prev) =>
            prev.map((f) =>
              f.fileKey === fileKey
                ? {
                  ...f,
                  uploading: false,
                  error: uploadResult[0]?.error || "Upload failed",
                }
                : f
            )
          );
        }
      } catch (error) {
        console.error("Error uploading JSON file:", error);
        setSelectedFigmaJsonFiles((prev) =>
          prev.map((f) =>
            f.fileKey === fileKey
              ? { ...f, uploading: false, error: "Upload failed" }
              : f
          )
        );
      }
    }
  };

  // Handle file selection
  const handleFileSelect = async (e) => {
    if (e.target.files.length > 0) {
      const newFiles = [...attachedFiles];
      const filesToUpload = [];

      // First, add files to state with uploading status
      for (let i = 0; i < e.target.files.length; i++) {
        const currentFile = e.target.files[i];

        // Check if file already exists in array (by name and size)
        const isDuplicate = newFiles.some(
          (existingFile) =>
            existingFile.name === currentFile.name &&
            existingFile.size === currentFile.size
        );

        if (!isDuplicate) {
          // Add file with uploading status
          const fileObj = {
            name: currentFile.name,
            size: currentFile.size,
            uploaded: false,
            uploading: true,
            file: currentFile,
          };

          newFiles.push(fileObj);
          filesToUpload.push(fileObj);
        }
      }

      // Update state to show files with loading state
      setAttachedFiles(newFiles);
      setShowAttachments(true);

      // Start upload immediately if we have projectId
      if (projectId && filesToUpload.length > 0) {
        setIsUploading(true);

        try {
          const files = filesToUpload.map((fileObj) => fileObj.file);
          const results = await uploadMultipleAttachments(files, projectId);

          setAttachedFiles((prev) => {
            const updatedFiles = [...prev];

            results.forEach((result, index) => {
              if (result.success !== false) {
                const fileIndex = updatedFiles.findIndex(
                  (f) =>
                    f.name === files[index].name && f.size === files[index].size
                );

                if (fileIndex !== -1) {
                  updatedFiles[fileIndex].uploaded = true;
                  updatedFiles[fileIndex].uploading = false;
                  updatedFiles[fileIndex].attachmentId = result.attachment_id;
                }
              } else {
                const fileIndex = updatedFiles.findIndex(
                  (f) =>
                    f.name === files[index].name && f.size === files[index].size
                );

                if (fileIndex !== -1) {
                  updatedFiles[fileIndex].uploading = false;
                  updatedFiles[fileIndex].error =
                    result.error || "Upload failed";
                }
              }
            });

            return updatedFiles;
          });

          const newUploadedAttachments = results
            .filter((result) => result.success !== false)
            .map((result) => ({
              attachment_id: result.attachment_id,
              filename: result.filename,
              file_location: result.file_location,
              size: result.size,
            }));

          setUploadedAttachments((prev) => [
            ...prev,
            ...newUploadedAttachments,
          ]);
        } catch (error) {
          setAttachedFiles((prev) => {
            return prev.map((f) => {
              if (f.uploading) {
                return { ...f, uploading: false, error: "Upload failed" };
              }
              return f;
            });
          });
        } finally {
          setIsUploading(false);
        }
      }
    }
  };

  // Handle removing a file
  const handleRemoveFile = (fileToRemove) => {
    if (fileToRemove.uploading) return;

    const updatedFiles = attachedFiles.filter((file) => file !== fileToRemove);
    setAttachedFiles(updatedFiles);

    if (fileToRemove.attachmentId) {
      setUploadedAttachments((prev) =>
        prev.filter(
          (attachment) => attachment.attachment_id !== fileToRemove.attachmentId
        )
      );
    }

    if (
      updatedFiles.length === 0 &&
      selectedFigmaFiles.length === 0 &&
      selectedFigmaJsonFiles.length === 0
    ) {
      setShowAttachments(false);
    }
  };

  // Handle clearing all files
  const handleClearFiles = () => {
    setAttachedFiles([]);
    setUploadedAttachments([]);

    if (
      selectedFigmaFiles.length === 0 &&
      selectedFigmaJsonFiles.length === 0
    ) {
      setShowAttachments(false);
    }
  };
  const handlePlusFuncClick = () => {
    setShowAttachmentModal(true);
  }

  const calculatePosition = () => {
    if (!plusButtonRef.current) return { top: "60px", left: "20px" };

    const rect = plusButtonRef.current.getBoundingClientRect();
    return {
      bottom: `${window.innerHeight - rect.top + 10}px`,
      left: `${rect.left}px`,
    };
  };

  // Handle the Plus button click
  const handlePlusClick = () => {
    if (
      attachedFiles.length > 0 ||
      selectedFigmaFiles.length > 0 ||
      selectedFigmaJsonFiles.length > 0
    ) {
      setShowAttachments(!showAttachments);
    } else {
      setShowAttachments(true)
    }
  };

  const handleAddFiles = () => {
    const hasExistingAttachments =
      attachedFiles.length > 0 ||
      extractedScreensCount > 0;

    if (hasExistingAttachments) {
      setShowAttachments(!showAttachments);
    } else {

      if (fileInputRef.current) {
        fileInputRef.current.click();
        setShowAttachmentModal(true);
      }

    }

  };

  // Handle Add More Files button in popup
  const handleAddMoreFiles = () => {
    setTimeout(() => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    }, 10);
  };

  // Close popup
  const handleClosePopup = () => {
    setShowAttachments(false);
    setInitialActiveTab("files");
  };

  // Upload files before sending message
  const uploadFiles = async () => {
    if (!projectId || attachedFiles.length === 0) return [];

    const filesToUpload = attachedFiles.filter((file) => !file.uploaded);
    if (filesToUpload.length === 0) return uploadedAttachments;

    setIsUploading(true);

    try {
      const files = filesToUpload.map((fileObj) => fileObj.file);
      const results = await uploadMultipleAttachments(files, projectId);

      const updatedFiles = [...attachedFiles];
      results.forEach((result, index) => {
        if (result.success !== false) {
          const fileIndex = updatedFiles.findIndex(
            (f) => f.name === files[index].name && f.size === files[index].size
          );

          if (fileIndex !== -1) {
            updatedFiles[fileIndex].uploaded = true;
            updatedFiles[fileIndex].attachmentId = result.attachment_id;
          }
        }
      });

      setAttachedFiles(updatedFiles);

      const newUploadedAttachments = results
        .filter((result) => result.success !== false)
        .map((result) => ({
          attachment_id: result.attachment_id,
          filename: result.filename,
          file_location: result.file_location,
          size: result.size,
        }));

      setUploadedAttachments((prev) => [...prev, ...newUploadedAttachments]);
      return newUploadedAttachments;
    } catch (error) {
      return [];
    } finally {
      setIsUploading(false);
    }
  };

  // Utility function to trigger cleanup of attachments folder
  // Call this when streaming is complete
  const triggerAttachmentsCleanup = useCallback(() => {
    if (wsConnection?.readyState === WebSocket.OPEN) {
      const cleanupMessage = {
        type: "cleanup_attachments"
      };

      
      wsConnection.send(JSON.stringify(cleanupMessage));
    } else {
      console.warn('WebSocket not available for cleanup trigger');
    }
  }, [wsConnection]);

  // Adjust textarea height
  const adjustTextAreaHeight = (element) => {
    if (!element) return;

    if (!element.value.trim()) {
      element.style.height = "43px";
      setTextAreaHeight("43px");
      if (isMultiline) setIsMultiline(false);
      return;
    }

    const scrollPosition = element.scrollTop;
    const selectionStart = element.selectionStart;
    const selectionEnd = element.selectionEnd;

    element.style.height = "43px";

    const contentHeight = element.scrollHeight;
    const minHeight = 43;
    const maxHeight = 150;

    const newHeight = Math.min(Math.max(contentHeight, minHeight), maxHeight);
    const newHeightPx = `${newHeight}px`;
    element.style.height = newHeightPx;
    setTextAreaHeight(newHeightPx);

    element.style.overflowY = contentHeight > maxHeight ? "auto" : "hidden";

    const shouldBeMultiline = contentHeight > minHeight + 14;
    if (isMultiline !== shouldBeMultiline) {
      setIsMultiline(shouldBeMultiline);
    }

    element.selectionStart = selectionStart;
    element.selectionEnd = selectionEnd;
    element.scrollTop = scrollPosition;
  };

  // Check if send button should be enabled
  const isSendEnabled =
    (inputValue.trim().length > 0 ||
      attachedFiles.length > 0 ||
      selectedFigmaFiles.length > 0 ||
      selectedFigmaJsonFiles.filter((f) => f.uploaded).length > 0) &&
    !isUploading &&
    !selectedFigmaJsonFiles.some((f) => f.uploading);

  // Modified handleSendClick function
  const handleSendClick = async () => {
    if (!isSendEnabled) return;

    // Set hasUserSentFirstMessage to true on first message send
    if (!hasUserSentFirstMessage) {
      setHasUserSentFirstMessage(true);
      // Persist to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem(`hasUserSentFirstMessage_${projectId}`, 'true');
      }
    }

    try {
      let finalAttachments = [...uploadedAttachments];

      if (attachedFiles.some((file) => !file.uploaded)) {
        const newAttachments = await uploadFiles();
        finalAttachments = [...uploadedAttachments, ...newAttachments];
      }

      const messageData = {
        type: "send_message",
        content: inputValue.trim(),
        parent_id: activeReplyTo,
      };

      // Get uploaded JSON files
      const uploadedJsonFiles = uploadedJsonAttachments.filter((f) =>
        selectedFigmaJsonFiles.some(
          (sf) => sf.fileKey === f.fileKey && sf.uploaded
        )
      );

      // Standardize JSON file format using the existing designId
      const standardizedJsonFiles = uploadedJsonFiles.map((f) => {
        // Find the corresponding selected file to get the designId
        const selectedFile = selectedFigmaJsonFiles.find(
          (sf) => sf.fileKey === f.fileKey
        );

        return {
          attachment_id: f.attachment_id,
          filename: f.filename,
          size: f.size,
          fileKey: selectedFile.designId,
          file_type: "figma_json",
        };
      });

      if (standardizedJsonFiles.length > 0) {
        finalAttachments = [...finalAttachments, ...standardizedJsonFiles];
      }

      // Parse all @ file references and @figma references from the input text
      // First, look for regular @file references
      const atFileReferences = inputValue.match(/@([^\s]+(?:\.json|\.js|\.css|\.html|\.txt)?)\s/g);
      // Then, specifically look for @figma references with a different pattern
      // Updated pattern to match @figma references even without trailing space
      const figmaReferences = inputValue.match(/@figma([^\s]+)(?:\s|$)/g);
      // Process regular @file references
      if (atFileReferences && atFileReferences.length > 0) {
        const availableAttachments = getAvailableAttachments();
        atFileReferences.forEach(atRef => {
          // Skip @figma references as they'll be handled separately
          if (atRef.startsWith('@figma')) return;
          // Remove @ and trim space
          const filename = atRef.replace('@', '').trim();
          // Find matching attachment
          const matchingAttachment = availableAttachments.find(att =>
            att.filename === filename ||
            att.filename === filename.replace('.json', '') + '.json' ||
            att.name === filename
          );

          if (matchingAttachment) {
            // Check if this attachment is not already in finalAttachments
            const alreadyExists = finalAttachments.some(att =>
              att.attachment_id === matchingAttachment.id ||
              att.id === matchingAttachment.id
            );

            if (!alreadyExists) {
              const selectorAttachment = {
                attachment_id: matchingAttachment.id,
                filename: matchingAttachment.filename || matchingAttachment.name,
                file_location: matchingAttachment.file_location || matchingAttachment.image_url || '',
                file_type: matchingAttachment.file_type,
                size: matchingAttachment.size || null,
                id: matchingAttachment.id,
              };

              // Add screen_data for figma screen types (matching FigmaPreviewPanel structure)
              if (matchingAttachment.file_type === 'figma_screen' || matchingAttachment.file_type === 'figma_extracted_screen') {
                selectorAttachment.screen_data = matchingAttachment.screen_data || matchingAttachment;
              }

              finalAttachments = [...finalAttachments, selectorAttachment];
            }
          }
        });
      }

      // Process @figma references specifically
      if (figmaReferences && figmaReferences.length > 0) {
        const availableAttachments = getAvailableAttachments();

        figmaReferences.forEach(figmaRef => {
          // Remove @figma and trim space/end of string
          const filename = figmaRef.replace('@figma', '').replace(/\s+$/, '').trim();

          

          // Find matching figma attachment
          let matchingAttachment = selectedAttachmentForSend
          if (matchingAttachment) {
            // Check if this attachment is not already in finalAttachments
            const alreadyExists = finalAttachments.some(att =>
              att.attachment_id === matchingAttachment.id ||
              att.id === matchingAttachment.id
            );
            
            if (!alreadyExists) {
              const figmaAttachment = {
                attachment_id: matchingAttachment.id,
                filename: matchingAttachment.filename || matchingAttachment.name,
                file_location: matchingAttachment.file_location || matchingAttachment.image_url || '',
                file_type: matchingAttachment.file_type,
                size: matchingAttachment.size || null,
                id: matchingAttachment.id,
                screen_data: matchingAttachment.screen_data || matchingAttachment
              };
              
              finalAttachments = [...finalAttachments, figmaAttachment];
            }
          }
        });
      }

      // Add all attachments (regular files + JSON files) if any
      if (finalAttachments.length > 0) {
        messageData.attachment_ids = finalAttachments.map(
          (a) => a.attachment_id
        );
        messageData.attachments = finalAttachments;
      }

      // Add Figma files as attachments for code generation

      if (selectedFigmaFiles.length > 0) {
        
        const figmaAttachments = selectedFigmaFiles.map((f) => ({
          id: f.id,
          name: f.name,
          fileKey: f.id, // Use the full figma_id (not just file_key) for backend processing
          url: f.url,
          file_type: "figma_json", // Use figma_json to match backend processing
        }));

        // Add to existing attachments array
        if (!messageData.attachments) {
          messageData.attachments = [];
        }
        messageData.attachments = [
          ...messageData.attachments,
          ...figmaAttachments,
        ];

        // Keep figma_files for backward compatibility if needed
        messageData.figma_files = selectedFigmaFiles.map((f) => ({
          id: f.id,
          name: f.name,
          file_key: f.file_key,
          url: f.url,
          file_type: "figma_design",
        }));
      }
      if (
        selectedAttachmentForSend &&
        selectedAttachmentForSend.file_type === "figma_screen"
      ) {
        const { screen_data } = selectedAttachmentForSend;
        const generateMessage = {
          type: "generate_figma_screen_code",
          screen_name: screen_data?.screen_name || selectedAttachmentForSend.name,
          screen_id: screen_data?.screen_id || selectedAttachmentForSend.id,
        };
        sessionStorage.setItem(`currentProcessing_screen-${projectId}`, screen_data?.screen_id)
        if (wsConnection?.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify(generateMessage));
      }
      }

      // Check if this is a figma question (has @figma references but not from play button)
      const hasFigmaReferences = figmaReferences && figmaReferences.length > 0;
      const isFromPlayButton = selectedAttachmentForSend && selectedAttachmentForSend.file_type === "figma_screen";

      if (hasFigmaReferences && !isFromPlayButton) {
        // This is a figma question - use generate_figma_screen_code for each referenced screen
        const figmaAttachments = finalAttachments.filter(att =>
          att.file_type && att.file_type.includes('figma')
        );

        if (figmaAttachments.length > 0) {
          // Send generate_figma_screen_code for each figma attachment
          for (const attachment of figmaAttachments) {
            const screen_data = attachment.screen_data || {};
            const generateMessage = {
              type: "generate_figma_screen_code",
              screen_name: screen_data.screen_name || attachment.name || 'Unknown Screen',
              screen_id: screen_data.screen_id || attachment.id || attachment.attachment_id,
            };

            if (wsConnection?.readyState === WebSocket.OPEN) {
              wsConnection.send(JSON.stringify(generateMessage));
            }
          }

          // Clear everything after sending figma messages...
          setAttachedFiles([]);
          setUploadedAttachments([]);
          setSelectedFigmaFiles([]);
          setSelectedFigmaJsonFiles([]);
          setUploadedJsonAttachments([]);
          setShowAttachments(false);
          setInputValue("");
          setIsMultiline(false);
          setSelectedAttachmentForSend(null);
          setShowAttachmentSelector(false);
          setCurrentSearchQuery("");

          if (textAreaRef.current) {
            setTimeout(() => {
              if (textAreaRef.current) {
                textAreaRef.current.style.height = "43px";
              }
            }, 100);
          }

          return; // Don't send regular message
        }
      }

      // Send the regular message only if not a figma question
      if (wsConnection?.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify(messageData));
      } else {
        handleSendMessage();
      }

      // Clear everything after sending...
      setAttachedFiles([]);
      setUploadedAttachments([]);
      setSelectedFigmaFiles([]);
      setSelectedFigmaJsonFiles([]);
      setUploadedJsonAttachments([]);
      setShowAttachments(false);
      setInputValue("");
      setIsMultiline(false);
      setSelectedAttachmentForSend(null);
      setShowAttachmentSelector(false);
      setCurrentSearchQuery("");

      if (textAreaRef.current) {
        setTimeout(() => {
          if (textAreaRef.current) {
            textAreaRef.current.style.height = "43px";
            textAreaRef.current.style.overflowY = "hidden";
            adjustTextAreaHeight(textAreaRef.current);
            textAreaRef.current.focus();
          }
        }, 0);
      }
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  // Focus textarea when component mounts
  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = "43px";
      textAreaRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isAiTyping == false) {
      setIsStop(false);

      // Trigger cleanup when streaming is complete
      // Small delay to ensure streaming is fully complete
      setTimeout(() => {
        triggerAttachmentsCleanup();
      }, 1000); // 1 second delay
    }
  }, [isAiTyping, triggerAttachmentsCleanup]);


  const extractedScreensCount = uploadedAttachments.filter(attachment =>
    attachment.file_type === "figma_extracted_screen"
  ).length;

  const totalAttachments = attachedFiles.length + selectedFigmaJsonFiles.length + (extractedScreensCount ? extractedScreensCount : 0);

  // Get available files for the @ selector (like VSCode file suggestions)
  const getAvailableAttachments = () => {
    const files = [];

    // console.error('Available attachments for @selector:', files);
    // console.error('figmaData from context:', figmaData);
    // console.error('figmaFiles state:', figmaFiles);
    // console.error('uploadedAttachments state:', uploadedAttachments);
    // console.error('figmaJsonFiles state:', figmaJsonFiles);
    // console.error('figmaList state:', figmaList);

    if (figmaList && figmaList.length > 0) {
      figmaList.forEach(screen => {
        const screenId = screen.screen_id || screen.id;
        files.push({
          id: screenId,
          filename: `screen_${screenId}.json`, // Match FigmaPreviewPanel format
          file_location: screen.image_url || '',
          file_type: 'figma_screen',
          size: null,
          name: screen.screen_name || screen.name,
          path: 'Figma Screens',
          screen_data: screen // Include full screen data for FigmaPreviewPanel compatibility
        });
      });
    }

    // Add Figma screens from figmaData (context) - this is likely what FigmaPreviewPanel uses
    if (figmaData && figmaData.length > 0) {
      figmaData.forEach(screen => {
        const screenId = screen.screen_id || screen.id;
        files.push({
          id: screenId,
          filename: `screen_${screenId}.json`, // Match FigmaPreviewPanel format
          file_location: screen.image_url || '',
          file_type: 'figma_screen',
          size: null,
          name: screen.screen_name || screen.name,
          path: 'Figma Screens',
          screen_data: screen // Include full screen data for FigmaPreviewPanel compatibility
        });
      });
    }

    // Add Figma designs from figmaFiles state (local state)
    if (figmaFiles && figmaFiles.length > 0) {
      figmaFiles.forEach(design => {
        files.push({
          id: design.id,
          filename: `design_${design.id}.json`,
          file_location: design.url || '',
          file_type: 'figma_design',
          size: null,
          name: design.name,
          path: 'Figma Designs',
          screen_data: design
        });
      });
    }

    // Add Figma JSON files (expanded designs)
    if (figmaJsonFiles && Object.keys(figmaJsonFiles).length > 0) {
      Object.entries(figmaJsonFiles).forEach(([designId, jsonFiles]) => {
        const design = figmaFiles.find(f => f.id === designId);
        const designName = design?.name || 'Unknown Design';

        if (Array.isArray(jsonFiles)) {
          jsonFiles.forEach(jsonFile => {
            files.push({
              id: `${designId}-${jsonFile.filename}`,
              filename: jsonFile.filename, // Use actual JSON filename
              file_location: jsonFile.path || jsonFile.relative_path || '',
              file_type: 'figma_json',
              size: null,
              name: jsonFile.frame_name || jsonFile.filename,
              path: `${designName}/frames`,
              screen_data: jsonFile
            });
          });
        }
      });
    }

    // Add extracted Figma screens from uploadedAttachments
    if (uploadedAttachments && uploadedAttachments.length > 0) {
      uploadedAttachments
        .filter(attachment => attachment.file_type === 'figma_extracted_screen')
        .forEach(attachment => {
          files.push({
            id: attachment.attachment_id,
            filename: attachment.filename, // Keep original filename from attachment
            file_location: attachment.file_location || '',
            file_type: 'figma_extracted_screen',
            size: attachment.size || null,
            name: attachment.filename,
            path: 'Extracted Screens',
            screen_data: attachment.screen_data || attachment
          });
        });

      // Add regular uploaded files
      uploadedAttachments
        .filter(attachment => attachment.file_type !== 'figma_extracted_screen')
        .forEach(attachment => {
          files.push({
            id: attachment.attachment_id,
            filename: attachment.filename,
            file_location: attachment.file_location || '',
            file_type: attachment.file_type || 'file',
            size: attachment.size || null,
            name: attachment.filename,
            path: 'Uploaded Files'
          });
        });
    }


    return files;
  };

  // Handle attachment selection from @figma selector
  const handleAttachmentSelect = (attachment) => {
    // Use filename with .json extension for proper reference
    let filename = attachment?.name || attachment?.filename;
    
    // Add .json extension if not present and it's a figma file
    if (attachment.file_type && attachment.file_type.includes('figma') && !filename.endsWith('.json')) {
      filename = filename ;
    }

    const currentInput = inputValue;
    const figmaAtIndex = currentInput.lastIndexOf('@figma');

    // Replace from @figma symbol to current cursor position with the filename
    // Important: No space between @figma and filename for proper attachment recognition
    const beforeAt = currentInput.substring(0, figmaAtIndex);
    const afterCursor = currentInput.substring(textAreaRef.current?.selectionStart || currentInput.length);

    // Store the attachment for later use
    setSelectedAttachmentForSend(attachment);
    

    // Format: @figmafilename.json with a space after
    const newValue = beforeAt + `@figma${filename} ` + afterCursor;

    setInputValue(newValue);
    setShowAttachmentSelector(false);

    // Store selected attachment for sending
    setSelectedAttachmentForSend(attachment);

    // Focus the textarea and set cursor position after the attachment reference
    if (textAreaRef.current) {
      setTimeout(() => {
        textAreaRef.current.focus();
        const newPosition = beforeAt.length + filename.length + 7; // +7 for @figma and space
        textAreaRef.current.setSelectionRange(newPosition, newPosition);
        adjustTextAreaHeight(textAreaRef.current);
      }, 10);
    }
  };

  // Close attachment selector
  const handleCloseAttachmentSelector = () => {
    setShowAttachmentSelector(false);
  };

  return (
    <div className="w-full max-w-lg mx-auto relative">
      {/* Gradient Border Animation Wrapper - only show if user hasn't sent first message */}
      {!hasUserSentFirstMessage && (
        <div className="absolute inset-0 -left-[1px] -right-[1px] -top-[1px] -bottom-[1px] rounded-xl overflow-hidden">
          <div className={`absolute inset-0 conic-gradient-wrapper ${(isUploading || isAiTyping) ? "animate-gradient-rotation-fast-disabled" : "animate-gradient-rotation-fast"}`}>
            <div className="absolute inset-[-200%] bg-conic-gradient-single-line"></div>
          </div>
        </div>
      )}

      {/* Main Input Container - always show border */}
      <div className={`relative w-full bg-white rounded-xl shadow-[0px_2px_4px_-2px_rgba(0,0,0,0.05),0px_-1px_5px_0px_rgba(0,0,0,0.05)] overflow-hidden border border-[hsl(var(--primary))]`}>
        <style
          dangerouslySetInnerHTML={{
            __html: `
        textarea.chat-input {
          caret-color: rgb(107, 114, 128);
          caret-width: 1px;
          border: none !important;
          outline: none !important;
          text-decoration: none !important;
          -webkit-text-decoration: none !important;
          -webkit-text-decoration-line: none !important;
          text-decoration-line: none !important;
          -webkit-appearance: none !important;
          appearance: none !important;
          background-image: none !important;
          -webkit-text-stroke: none !important;
          text-shadow: none !important;
          -webkit-text-fill-color: inherit !important;
        }
        textarea.chat-input:focus {
          border: none !important;
          outline: none !important;
          box-shadow: none !important;
          text-decoration: none !important;
          -webkit-text-decoration: none !important;
          -webkit-text-decoration-line: none !important;
          text-decoration-line: none !important;
        }
        textarea.chat-input::selection {
          background-color: rgba(59, 130, 246, 0.2);
        }
        /* Remove any browser autocorrect styling */
        textarea.chat-input::-webkit-input-placeholder {
          text-decoration: none !important;
        }
        textarea.chat-input::placeholder {
          text-decoration: none !important;
        }
      `,
          }}
        />
        <div className="p-1.5 relative overflow-visible">
          <div className="flex flex-col">
            <div className={`w-full ${isMultiline ? "mb-8" : ""} relative`}>

              <textarea
                ref={textAreaRef}
                value={inputValue}
                placeholder={
                  isReady
                    ? isAiTyping
                      ? isStop
                        ? "Stopping response..."
                        : "Generating your response, please wait..."
                      : "Reply to message..."
                    : "Reply to message..."
                }
                className="chat-input w-full resize-none border-none bg-transparent text-gray-700 placeholder-gray-400 font-medium leading-[1.375rem] focus:outline-none focus:ring-0 focus:border-none relative z-10 text-base"
                style={{
                  height: "43px",
                  maxHeight: "150px",
                  overflowY: "hidden",
                  minHeight: "43px",
                  paddingRight: "40px",
                  paddingLeft: "32px",
                  paddingTop: "15px",
                  paddingBottom: "15px",
                  cursor:
                    isUploading || isStopped || isAiTyping
                      ? "not-allowed"
                      : "text",
                  caretColor: "rgb(107, 114, 128)",
                  fontFamily: "system-ui, -apple-system, sans-serif",
                  lineHeight: "1.375rem",
                  textDecoration: "none",
                  WebkitTextDecoration: "none",
                  color: "rgb(107, 114, 128)",
                }}
                rows={1}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    // If selector is open, don't send message - let selector handle Enter
                    if (showAttachmentSelector) {
                      return; // Let AttachmentSelector handle the Enter key
                    }
                    if (isSendEnabled) {
                      handleSendClick();
                    }
                  }
                }}
                onChange={(e) => {
                  const newValue = e.target.value;
                  const cursorPos = e.target.selectionStart;

                  setInputValue(newValue);
                  setCursorPosition(cursorPos);
                  adjustTextAreaHeight(e.target);

                  // Check for @figma symbol and handle filtering
                  const figmaAtIndex = newValue.lastIndexOf('@figma', cursorPos);
                  if (figmaAtIndex !== -1 && figmaAtIndex < cursorPos) {
                    // Get text after @figma symbol for filtering
                    const searchQuery = newValue.substring(figmaAtIndex + 6, cursorPos);

                    

                    // Check if there's already a complete file selection at this @figma position
                    const textAfterAt = newValue.substring(figmaAtIndex + 6);
                    const spaceIndex = textAfterAt.indexOf(' ');
                    const hasCompleteSelection = spaceIndex !== -1 && spaceIndex < (cursorPos - figmaAtIndex - 6);

                    // Show selector only if @figma is found, cursor is after it, and there's no complete selection
                    if ((figmaAtIndex === 0 || newValue[figmaAtIndex - 1] === ' ') && !hasCompleteSelection) {
                      // Get proper position for the selector based on textarea and cursor
                      const textareaRect = e.target.getBoundingClientRect();

                      // Calculate cursor position within textarea
                      const textBeforeCursor = newValue.substring(0, cursorPos);
                      const dummySpan = document.createElement('span');
                      dummySpan.style.font = window.getComputedStyle(e.target).font;
                      dummySpan.style.whiteSpace = 'pre-wrap';
                      dummySpan.textContent = textBeforeCursor;
                      document.body.appendChild(dummySpan);

                      // Get approximate cursor position
                      const cursorX = Math.min(dummySpan.offsetWidth % textareaRect.width, textareaRect.width - 100);
                      const linesBeforeCursor = Math.floor(dummySpan.offsetWidth / textareaRect.width);
                      const cursorY = linesBeforeCursor * parseInt(window.getComputedStyle(e.target).lineHeight);

                      document.body.removeChild(dummySpan);

                      // Position selector near cursor position
                      setAttachmentSelectorPosition({
                        bottom: window.innerHeight - (textareaRect.top + cursorY) - 10,
                        left: textareaRect.left + cursorX
                      });

                      setCurrentSearchQuery(searchQuery);
                      setShowAttachmentSelector(true);
                    } else {
                      setShowAttachmentSelector(false);
                      setCurrentSearchQuery("");
                    }
                  } else {
                    setShowAttachmentSelector(false);
                    setCurrentSearchQuery("");
                  }
                }}
                disabled={isUploading || isStopped || isAiTyping}
              />
            </div>

            {/* Plus button */}
            <div
              className={`absolute ${isMultiline ? "bottom-1.5" : "top-1/2 -translate-y-1/2"
                } left-1.5 z-20`}
            >
              <button
                ref={plusButtonRef}
                onClick={handlePlusFuncClick}
                className={`size-6 bg-white rounded-full outline outline-1 outline-offset-[-1px] outline-gray-200 flex items-center justify-center hover:bg-gray-50 relative ${isUploading ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                aria-label="Add attachments"
                aria-haspopup="dialog"
                aria-expanded={showAttachmentModal}
                disabled={isUploading}
              >
                {isUploading ? (
                  <Loader2 size={16} className="text-gray-600 animate-spin" />
                ) : (
                  <Plus size={16} className="text-gray-600" />
                )}

                {/* Badge showing total attachments */}
                {totalAttachments > 0 && (
                  <div className="absolute -top-1.5 -right-1.5 flex items-center justify-center min-w-[14px] h-[14px] text-[9px] font-weight-semibold text-white bg-primary rounded-full px-0.5">
                    {totalAttachments > 99 ? "99+" : totalAttachments}
                  </div>
                )}
              </button>
            </div>

            {/* Send button */}
            <div
              className={`absolute ${isMultiline
                ? "bottom-1.5 right-1.5"
                : "top-1/2 -translate-y-1/2 right-1.5"
                } z-20`}
            >
              <BootstrapTooltip
                title={
                  isUploading
                    ? "Uploading files..."
                    : isAiTyping
                      ? isStop
                        ? "Stopping response..."
                        : "Stop response"
                      : "Send message"
                }
                placement="top"
              >
                <button
                  onClick={
                    isAiTyping
                      ? () => {
                        setIsStop(true);
                        if (wsConnection?.readyState === WebSocket.OPEN) {
                          wsConnection.send(
                            JSON.stringify({
                              type: "stop_streaming",
                              task_id: searchParams.get("task_id"),
                            })
                          );
                        }
                        setInputValue(" ");
                      }
                      : handleSendClick
                  }
                  disabled={
                    isUploading || (isAiTyping ? isStop : !isSendEnabled || !isReady)
                  }
                  className={`size-6 rounded-full flex items-center justify-center 
                  ${isUploading
                      ? 'bg-primary text-white opacity-50 cursor-not-allowed'
                      : isAiTyping
                        ? isStop
                          ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                          : 'bg-red-500 text-white hover:bg-red-600'
                        : isSendEnabled && isReady
                          ? 'bg-primary text-white hover:bg-primary-600'
                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    }
                `}
                  aria-label={
                    isAiTyping
                      ? isStop
                        ? "Stopping response..."
                        : "Stop response"
                      : "Send message"
                  }
                >
                  {isUploading ? (
                    <Loader2 size={16} className="animate-spin" />
                  ) : isAiTyping ? (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect x="6" y="6" width="12" height="12" rx="2" fill="currentColor" />
                    </svg>
                  ) : (
                    <ArrowUp size={16} />
                  )}
                </button>
              </BootstrapTooltip>
            </div>
          </div>

          {/* Hidden file input */}
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            multiple
            onChange={handleFileSelect}
            aria-hidden="true"
            disabled={isUploading}
            accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt,.md,.json,.csv,.ppt,.pptx"
            onClick={(e) => {
              e.currentTarget.value = "";
            }}
          />
          {showAttachmentModal && ReactDOM.createPortal(
            <div
              className="fixed inset-0 z-[9999] pointer-events-none"
              aria-modal="true"
              role="dialog"
              aria-label="Attachment options"
            >
              {/* Backdrop with subtle blur */}
              <div className="absolute inset-0 pointer-events-auto" onClick={() => setShowAttachmentModal(false)} />

              <div
                className="absolute pointer-events-auto bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden"
                style={{
                  bottom: `${window.innerHeight - (plusButtonRef.current?.getBoundingClientRect().top || 0) + 10}px`,
                  left: `${plusButtonRef.current?.getBoundingClientRect().left || 0}px`,
                  width: "390px",
                  zIndex: 9999,
                }}
                tabIndex={-1}
              >
                {/* Header with gradient background */}
                <div className="bg-gradient-to-r from-primary-50 to-purple-50 px-5 py-4 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                      <Plus className="h-5 w-5 text-primary" />
                      Add Attachments
                    </h2>
                    <button
                      onClick={() => setShowAttachmentModal(false)}
                      className="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Options */}
                <div className="p-4 space-y-3">
                  <button
                    className="w-full group relative overflow-hidden bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 rounded-xl transition-all duration-200 border border-blue-200 hover:border-blue-300 hover:shadow-md"
                    onClick={() => {
                      handleAddFiles();
                      setShowAttachmentModal(false);
                    }}
                  >
                    <div className="flex items-center space-x-4 p-4">
                      <div className="flex-shrink-0 w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <Paperclip className="h-5 w-5 text-white" />
                      </div>
                      <div className="flex-1 text-left">
                        <h3 className="font-medium text-gray-800 group-hover:text-blue-900">Attach Files</h3>
                      </div>
                      <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all duration-200" />
                    </div>
                  </button>

                  <button
                    className="w-full group relative overflow-hidden bg-gradient-to-r from-purple-50 to-pink-50 hover:from-purple-100 hover:to-pink-100 rounded-xl transition-all duration-200 border border-purple-200 hover:border-purple-300 hover:shadow-md"
                    onClick={() => {
                      handleAddFigmaDesign();
                      setShowAttachmentModal(false);
                    }}
                  >
                    <div className="flex items-center space-x-4 p-4">
                      <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <Figma className="h-5 w-5 text-white" />
                      </div>
                      <div className="flex-1 text-left">
                        <h3 className="font-medium text-gray-800 group-hover:text-purple-900">Figma Design</h3>
                      </div>
                      <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-purple-600 group-hover:translate-x-1 transition-all duration-200" />
                    </div>
                  </button>
                </div>

                {/* Decorative gradient accent */}
                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary via-purple-500 to-pink-500"></div>
              </div>
            </div>,
            document.body
          )}

          {/* File attachment popup with portal */}
          {showAttachments && (
            <FileAttachmentPopup
              files={attachedFiles}
              onRemoveFile={handleRemoveFile}
              onClearFiles={handleClearFiles}
              onAddMoreFiles={handleAddMoreFiles}
              onClose={handleClosePopup}
              triggerButtonRef={plusButtonRef}
              isUploading={isUploading}
              figmaFiles={figmaFiles}
              onSelectFigmaFile={handleSelectFigmaFile}
              selectedFigmaFiles={selectedFigmaFiles}
              isLoadingFigma={isLoadingFigma}
              expandedFigmaDesigns={expandedFigmaDesigns}
              onToggleFigmaDesign={handleToggleFigmaDesign}
              figmaJsonFiles={figmaJsonFiles}
              onSelectFigmaJson={handleSelectFigmaJson}
              selectedFigmaJsonFiles={selectedFigmaJsonFiles}
              isLoadingFigmaJson={isLoadingFigmaJson}
              onClearFigmaFiles={handleClearFigmaFiles}
              onAddFigmaDesign={handleAddFigmaDesign}
              processingDesigns={processingDesigns}
              onCheckStatus={handleCheckStatus}
              jsonProcessingStatus={jsonProcessingStatus}
              initialActiveTab={initialActiveTab}
              lastUpdateTime={lastUpdateTime}
              onRebuildFigmaDesign={handleRebuildFigmaDesign}
              figmaErrors={figmaErrors}

            />
          )}

          {/* Add Figma Design Modal */}
          <AddFigmaDesignModal
            isOpen={showAddFigmaModal}
            onClose={() => setShowAddFigmaModal(false)}
            onSubmit={handleFigmaDesignSubmit}
            isLoading={isAddingFigma}
            figmaFiles={figmaFiles}
            onSelectFigmaFile={handleSelectFigmaFile}
            selectedFigmaFiles={selectedFigmaFiles}
            isLoadingFigma={isLoadingFigma}
            expandedFigmaDesigns={expandedFigmaDesigns}
            onToggleFigmaDesign={handleToggleFigmaDesign}
            figmaJsonFiles={figmaJsonFiles}
            onSelectFigmaJson={handleSelectFigmaJson}
            selectedFigmaJsonFiles={selectedFigmaJsonFiles}
            isLoadingFigmaJson={isLoadingFigmaJson}
            onClearFigmaFiles={handleClearFigmaFiles}
            onAddFigmaDesign={handleAddFigmaDesign}
            processingDesigns={processingDesigns}
            onCheckStatus={handleCheckStatus}
            jsonProcessingStatus={jsonProcessingStatus}
            initialActiveTab={initialActiveTab}
            lastUpdateTime={lastUpdateTime}
            onRebuildFigmaDesign={handleRebuildFigmaDesign}
            figmaErrors={figmaErrors}
            showProcessing={showProcessing}
            setShowProcessing={setShowProcessing}
            setShowInitialProcessing={setShowInitialProcessing}
            showInitialProcessing={showInitialProcessing}
            setShowFrameSelector={setShowFrameSelector}
            showFrameSelector={showFrameSelector}
            framesList={framesList}
            setFramesList={setFramesList}
            projectId={projectId}
            setFrameId={setFrameId}
            frameId={frameId}
            designList={designList}
            showDesignSelector={showDesignSelector}
            setShowDesignSelector={setShowDesignSelector}
            setEnableBackDesign={setEnableBackDesign}
            enablebackDesign={enablebackDesign}
            setUploadedAttachments={setUploadedAttachments}
            task_id={searchParams.get("task_id")}
            setDesignList={setDesignList}
            switchToFigmaTab={switchToFigmaTab}
            setFigmaLoader={setFigmaLoader}
          />

          {/* Attachment Selector */}
          <AttachmentSelector
            isVisible={showAttachmentSelector}
            position={attachmentSelectorPosition}
            attachments={getAvailableAttachments()}
            onSelect={handleAttachmentSelect}
            onClose={handleCloseAttachmentSelector}
            searchQuery={currentSearchQuery}
          />

          {/* Progress Modal */}
          <ProgressModal
            isOpen={showProgressModal}
            onClose={() => setShowProgressModal(false)}
            design={currentProgressDesign}
            processingStatus={processingStatus}
            isLoadingStatus={isLoadingStatus}
          />
        </div>
      </div>
    </div>
  );
};

export default ChatInput;