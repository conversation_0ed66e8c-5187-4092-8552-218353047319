/* Chat Interface Font */
.chat-panel, .chat-panel * {
  font-family: 'Inter', sans-serif;
}

/* Ensure message content has normal font weight - only in chat panel */
.chat-panel .message-content, 
.chat-panel .message-content * {
  font-weight: normal !important;
}

/* Keep KaviaAI label as medium weight - only in chat panel */
.chat-panel .font-medium {
  font-weight: 500 !important;
}

/* Prevent textarea animation conflicts */
textarea {
  transform: translateZ(0);
  backface-visibility: hidden;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(242, 106, 27, 0.5);
  border-radius: 20px;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(242, 106, 27, 0.7);
}

/* Rocket animation keyframes */
@keyframes rocket {
  0% {
    transform: translateY(0px) rotate(-45deg);
  }
  50% {
    transform: translateY(-10px) rotate(-45deg);
  }
  100% {
    transform: translateY(0px) rotate(-45deg);
  }
}

.animate-rocket {
  animation: rocket 2s infinite ease-in-out;
}

/* Flame animations */
@keyframes flame {
  0% {
    height: 4px;
    opacity: 0.7;
  }
  50% {
    height: 5px;
    opacity: 0.9;
  }
  100% {
    height: 4px;
    opacity: 0.7;
  }
}

.animate-flame {
  animation: flame 0.5s infinite ease-in-out;
}

@keyframes flame-delay {
  0% {
    height: 3px;
    opacity: 0.6;
  }
  50% {
    height: 4px;
    opacity: 0.8;
  }
  100% {
    height: 3px;
    opacity: 0.6;
  }
}

.animate-flame-delay {
  animation: flame-delay 0.5s infinite ease-in-out 0.15s;
}

/* Spark animations */
@keyframes spark-1 {
  0% { transform: translate(-2px, 0px); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translate(-3px, 3px); opacity: 0; }
}

@keyframes spark-2 {
  0% { transform: translate(2px, 0px); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translate(3px, 3px); opacity: 0; }
}

@keyframes spark-3 {
  0% { transform: translate(-1px, 0px); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translate(-2px, 4px); opacity: 0; }
}

.animate-spark-1 {
  animation: spark-1 1s infinite ease-out;
}

.animate-spark-2 {
  animation: spark-2 1.3s infinite ease-out 0.2s;
}

.animate-spark-3 {
  animation: spark-3 0.9s infinite ease-out 0.5s;
}

/* Add these new styles to fix horizontal scrolling */
pre, code {
  white-space: pre-wrap !important;
  word-break: break-word !important;
  max-width: 100% !important;
}

.message-content {
  word-break: break-word;
  overflow-wrap: break-word;
}

/* Add smooth transition for content updates */
.message-container {
  will-change: transform, opacity;
  transform: translateZ(0);
  transition: opacity 0.2s ease;
}

/* Prevent layout shifts */
.messages-wrapper {
  contain: content;
}

/* Prevent flickering during updates */
.messages-container {
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
}

/* New message indicator animation */
@keyframes bounce-slow {
  0%, 100% {
    transform: translate(-50%, 0);
  }
  50% {
    transform: translate(-50%, -3px);
  }
}

.animate-bounce-slow {
  animation: bounce-slow 2s infinite ease-in-out;
}
@keyframes bounce {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-2px);  /* Reduced from default -4px to -2px for gentler bounce */
    }
}

.animate-bounce {
    animation: bounce 1s infinite;
}
/* Make sure the message container has pointer-events set properly */
.messages-container {
  position: relative;
  pointer-events: auto;
}
