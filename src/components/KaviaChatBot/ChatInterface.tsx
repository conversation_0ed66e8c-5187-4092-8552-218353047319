"use client";

import React, { useState, useCallback, useRef, useEffect } from "react";
import { ArrowUp, ChevronLeft } from "lucide-react";
import Image from "next/image";
import kaviaLogo from "../../../public/logo/kavia_logo.svg";
import { askKaviaAI } from "../../utils/KaviaChatAPI";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import SyntaxHighlighter from "react-syntax-highlighter";

// Types
interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  sources?: string[];
}

interface ChatInterfaceProps {
  onBack: () => void;
}

// Components
const ChatHeader: React.FC<{ onBack: () => void }> = React.memo(
  ({ onBack }) => (
    <div className="flex items-center justify-between p-4 bg-white border-b border-gray-100">
      <div className="flex items-center gap-3">
        <button
          onClick={onBack}
          className="flex items-center justify-center p-1 hover:bg-gray-100 rounded-full transition-colors"
          aria-label="Go back"
        >
          <ChevronLeft strokeWidth={1.25} className="text-gray-700 size-5" />
        </button>
        <span className="font-medium text-lg text-gray-900">KAVIA AI</span>
      </div>
      {/* <button className="p-1 hover:bg-gray-100 rounded-full transition-colors">
        <Maximize2 strokeWidth={1.25} className="text-gray-700 size-4" />
      </button> */}
    </div>
  )
);

ChatHeader.displayName = "ChatHeader";

const ChatMessage: React.FC<{ message: Message }> = React.memo(
  ({ message }) => (
    <div className="mb-4">
      {message.isUser ? (
        <div className="flex justify-end">
          <div className="bg-[#1916160D] text-black px-3 py-2.5 rounded-xl rounded-br-none max-w-[80%] break-words">
            <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
              {message.text}
            </p>
          </div>
        </div>
      ) : (
        <div className="flex flex-col">
          <div className="flex items-center gap-2 mb-2">
            <Image src={kaviaLogo} alt="Kavia Logo" width={18} height={18} />
            <span className="font-medium text-gray-900 text-sm">KAVIA AI</span>
          </div>
          <div className="max-w-full break-words">
            <div className="text-sm font-medium leading-relaxed text-gray-900 overflow-hidden">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  code({ node, inline, className, children, ...props }: any) {
                    const match = /language-(\w+)/.exec(className || "");
                    return !inline && match ? (
                      <div className="max-w-full overflow-x-auto">
                        <SyntaxHighlighter
                          PreTag="div"
                          language={match[1]}
                          customStyle={{
                            borderRadius: 8,
                            fontSize: 13,
                            margin: "8px 0",
                            maxWidth: "100%",
                          }}
                          wrapLongLines={true}
                        >
                          {String(children).replace(/\n$/, "")}
                        </SyntaxHighlighter>
                      </div>
                    ) : (
                      <code
                        className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono break-all"
                        {...props}
                      >
                        {children}
                      </code>
                    );
                  },
                  a: ({ node, ...props }) => (
                    <a
                      {...props}
                      className="underline text-blue-600 hover:text-blue-800 break-all"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      🔗 {props.children}
                    </a>
                  ),
                  ul: ({ node, ...props }) => (
                    <ul className="list-disc ml-5 my-2" {...props} />
                  ),
                  ol: ({ node, ...props }) => (
                    <ol className="list-decimal ml-5 my-2" {...props} />
                  ),
                  li: ({ node, ...props }) => (
                    <li className="mb-1 break-words" {...props} />
                  ),
                  p: ({ node, ...props }) => (
                    <p className="mb-2 break-words" {...props} />
                  ),
                  h1: ({ node, ...props }) => (
                    <h1
                      className="text-lg font-bold mb-2 mt-4 break-words"
                      {...props}
                    />
                  ),
                  h2: ({ node, ...props }) => (
                    <h2
                      className="text-base font-bold mb-2 mt-3 break-words"
                      {...props}
                    />
                  ),
                  h3: ({ node, ...props }) => (
                    <h3
                      className="text-sm font-bold mb-1 mt-2 break-words"
                      {...props}
                    />
                  ),
                  blockquote: ({ node, ...props }) => (
                    <blockquote
                      className="border-l-4 border-gray-300 pl-3 italic my-2 break-words"
                      {...props}
                    />
                  ),
                }}
              >
                {message.text}
              </ReactMarkdown>
            </div>

            {/* Sources Section
            {message.sources && message.sources.length > 0 && (
              <div className="mt-4 rounded-lg">
                <h4 className="text-sm font-semibold text-gray-700 mb-2">
                  Sources:
                </h4>
                <div className="space-y-1">
                  {message.sources.map((source, index) => (
                    <a
                      key={index}
                      href={source}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block text-xs text-blue-600 hover:text-blue-800 break-all"
                    >
                      🔗 {source}
                    </a>
                  ))}
                </div>
              </div>
            )} */}
          </div>
        </div>
      )}
    </div>
  )
);

ChatMessage.displayName = "ChatMessage";

const ThinkingIndicator: React.FC = React.memo(() => {
  const [currentMessage, setCurrentMessage] = useState(0);
  const thinkingMessages = [
    "Kavia is thinking...",
    "Kavia is searching...",
    "Kavia is analyzing...",
    "Kavia is processing...",
  ];

  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMessage((prev) => (prev + 1) % thinkingMessages.length);
    }, 1500);
    return () => clearInterval(interval);
  }, [thinkingMessages.length]);

  return (
    <div className="mb-4">
      <div className="flex flex-col">
        <div className="flex items-center gap-2 mb-2">
          <Image src={kaviaLogo} alt="Kavia Logo" width={18} height={18} />
          <span className="font-medium text-gray-900 text-sm">KAVIA AI</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
          </div>
          <p className="text-sm text-gray-600">
            {thinkingMessages[currentMessage]}
          </p>
        </div>
      </div>
    </div>
  );
});

ThinkingIndicator.displayName = "ThinkingIndicator";

const EmptyState: React.FC = React.memo(() => (
  <div className="flex-1 flex flex-col items-center justify-center px-4 h-full">
    <div className="flex flex-col items-center text-center">
      <Image
        src={kaviaLogo}
        alt="Kavia Logo"
        width={64}
        height={64}
        className="mb-4 opacity-60"
      />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        Welcome to KAVIA AI
      </h3>
      <p className="text-sm text-gray-600 max-w-xs leading-relaxed">
        How can I assist you today?
      </p>
    </div>
  </div>
));

EmptyState.displayName = "EmptyState";

// Main Chat Interface Component
const ChatInterface: React.FC<ChatInterfaceProps> = ({ onBack }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isBotThinking, setIsBotThinking] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea based on content
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      const newHeight = Math.min(Math.max(textarea.scrollHeight, 48), 200);
      textarea.style.height = `${newHeight}px`;
    }
  }, [inputValue]);

  const handleSendMessage = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      if (!inputValue.trim() || isBotThinking) return;

      const userMessage: Message = {
        id: Date.now().toString(),
        text: inputValue,
        isUser: true,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, userMessage]);
      setInputValue("");
      setIsBotThinking(true);

      try {
        const botReply = await askKaviaAI(userMessage.text);
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          text: botReply.answer,
          isUser: false,
          timestamp: new Date(),
          sources: botReply.sources,
        };
        setMessages((prev) => [...prev, aiResponse]);
      } catch (error) {
        console.error("Error getting AI response:", error);
        const errorResponse: Message = {
          id: (Date.now() + 1).toString(),
          text: "Sorry, there was an error contacting Kavia AI. Please try again.",
          isUser: false,
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, errorResponse]);
      } finally {
        setIsBotThinking(false);
      }
    },
    [inputValue, isBotThinking]
  );

  const handleInputKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSendMessage(e);
      }
    },
    [handleSendMessage]
  );

  return (
    <div className="flex flex-col h-full rounded-xl overflow-hidden relative">
      <ChatHeader onBack={onBack} />

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 pb-2 min-h-0 relative z-10">
        {messages.length === 0 && !isBotThinking ? (
          <EmptyState />
        ) : (
          <>
            {messages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))}
            {isBotThinking && <ThinkingIndicator />}
          </>
        )}
      </div>

      {/* Input Form */}
      <div className="pb-4 px-4 bg-transparent flex-shrink-0 relative z-10">
        <form onSubmit={handleSendMessage} className="relative">
          <textarea
            ref={textareaRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleInputKeyDown}
            placeholder="Ask Kavia AI about Kavia"
            disabled={isBotThinking}
            rows={1}
            className="w-full px-3 py-3 pr-12 border text-gray-800 border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-gray-400 focus:border-gray-400 text-sm placeholder-gray-500 disabled:opacity-50 disabled:cursor-not-allowed resize-none overflow-hidden min-h-[48px] max-h-[200px]"
          />
          <button
            type="submit"
            disabled={!inputValue.trim() || isBotThinking}
            className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-gray-200 rounded-full text-gray-800 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            aria-label="Send message"
          >
            <ArrowUp size={16} />
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChatInterface;
