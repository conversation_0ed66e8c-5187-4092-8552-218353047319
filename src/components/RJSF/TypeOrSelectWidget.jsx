import React, { useEffect, useMemo, useRef, useState } from 'react';

/**
 * A simple RJSF widget that allows both typing a custom value and selecting
 * from provided enum options using a native datalist. Works with any schema
 * string field that may optionally provide enum/enumNames.
 */
const TypeOrSelectWidget = ({
  id,
  value,
  required,
  disabled,
  readonly,
  onChange,
  onBlur,
  onFocus,
  options = {},
  placeholder,
  formContext = {},
}) => {
  const wrapperRef = useRef(null);
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value ?? '');
  const [highlightIndex, setHighlightIndex] = useState(-1);

  useEffect(() => {
    setInputValue(value ?? '');
  }, [value]);

  const handleExternalClick = (e) => {
    if (wrapperRef.current && !wrapperRef.current.contains(e.target)) {
      setOpen(false);
    }
  };

  useEffect(() => {
    if (open) {
      document.addEventListener('mousedown', handleExternalClick);
      return () => document.removeEventListener('mousedown', handleExternalClick);
    }
  }, [open]);

  // Normalize suggestion sources from ui:options
  const normalize = (arr) => {
    if (!Array.isArray(arr)) return [];
    return arr
      .map((opt) => {
        if (opt == null) return null;
        if (typeof opt === 'string') return { label: opt, value: opt };
        if (typeof opt === 'object') {
          const label = opt.label ?? opt.value ?? opt.key ?? '';
          const val = opt.value ?? opt.key ?? label;
          return { label: String(label), value: String(val) };
        }
        return null;
      })
      .filter(Boolean);
  };

  const containerType = formContext.containerType;
  const suggestions = normalize(options.suggestions);
  const suggestionsByType = options.suggestionsByContainerType || {};
  const byTypeList = containerType ? normalize(suggestionsByType[containerType]) : [];
  const enumOptions = options.enumOptions ? normalize(options.enumOptions) : [];

  const optionList = useMemo(() => {
    let base = [];
    if (byTypeList.length > 0) base = byTypeList;
    else if (suggestions.length > 0) base = suggestions;
    else if (enumOptions.length > 0) base = enumOptions;
    else base = normalize(Object.values(suggestionsByType).flat());

    // Move selected to the top (like your screenshot)
    if (value) {
      const idx = base.findIndex((o) => o.value === value);
      if (idx > 0) {
        const copy = [...base];
        const [sel] = copy.splice(idx, 1);
        copy.unshift(sel);
        return copy;
      }
    }
    return base;
  }, [byTypeList, suggestions, enumOptions, suggestionsByType, value]);

  const filteredOptions = useMemo(() => {
    const q = (inputValue || '').toLowerCase();
    if (!q) return optionList;
    return optionList.filter((o) => o.label.toLowerCase().includes(q) || o.value.toLowerCase().includes(q));
  }, [optionList, inputValue]);

  const inputPlaceholder = placeholder || options.placeholder || 'Select or type...';

  const selectValue = (val) => {
    onChange(val === '' ? undefined : val);
    setOpen(false);
  };

  const handleInputChange = (event) => {
    const next = event.target.value;
    setInputValue(next);
    onChange(next === '' ? undefined : next);
  };

  const handleKeyDown = (e) => {
    if (!open && (e.key === 'ArrowDown' || e.key === 'ArrowUp')) {
      setOpen(true);
      return;
    }
    if (!open && e.key === 'Enter') {
      selectValue(inputValue.trim());
      return;
    }
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setHighlightIndex((i) => Math.min((i < 0 ? -1 : i) + 1, filteredOptions.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setHighlightIndex((i) => Math.max(i - 1, 0));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (highlightIndex >= 0 && filteredOptions[highlightIndex]) {
        selectValue(filteredOptions[highlightIndex].value);
      } else {
        selectValue(inputValue.trim());
      }
    } else if (e.key === 'Escape') {
      setOpen(false);
    }
  };

  const handleBlur = (event) => {
    onBlur && onBlur(id, event.target.value);
  };

  const handleFocus = (event) => {
    onFocus && onFocus(id, event.target.value);
    setOpen(true);
  };

  return (
    <div ref={wrapperRef} className="relative">
      <input
        id={id}
        className="block w-full rounded-md border border-gray-300 pl-3 pr-8 py-2 text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 disabled:bg-gray-100"
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onBlur={handleBlur}
        onFocus={handleFocus}
        onKeyDown={handleKeyDown}
        required={required}
        disabled={disabled || readonly}
        placeholder={inputPlaceholder}
        autoComplete="off"
        aria-autocomplete="list"
        aria-expanded={open}
        aria-controls={`${id}-options`}
      />
      <svg
        className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 text-gray-400"
        width="16" height="16" viewBox="0 0 20 20" fill="currentColor"
        aria-hidden="true"
      >
        <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.25 8.29a.75.75 0 01-.02-1.08z" clipRule="evenodd" />
      </svg>

      {open && filteredOptions.length > 0 && (
        <div
          id={`${id}-options`}
          role="listbox"
          className="absolute z-10 mt-1 w-full max-h-64 overflow-y-auto rounded-md border border-gray-200 bg-white shadow-lg"
        >
          {filteredOptions.map((opt, idx) => {
            const isSelected = (value ?? '') === opt.value;
            const isActive = idx === highlightIndex;
            return (
              <button
                type="button"
                key={String(opt.value)}
                role="option"
                aria-selected={isSelected}
                onMouseEnter={() => setHighlightIndex(idx)}
                onMouseLeave={() => setHighlightIndex(-1)}
                onClick={() => selectValue(opt.value)}
                className={`${isActive ? 'bg-gray-100' : ''} w-full px-3 py-2 text-left text-sm flex items-center justify-between hover:bg-gray-100`}
              >
                <span className="truncate pr-2">{opt.label ?? opt.value}</span>
                {isSelected && (
                  <svg width="14" height="14" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M2 6L4 8L10 2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                )}
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default TypeOrSelectWidget;


