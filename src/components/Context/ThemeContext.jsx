"use client";

import React, { createContext, useContext, useState } from 'react';

// Create ThemeContext
export const ThemeContext = createContext({
  isDarkMode: true,
  toggleDarkMode: () => {}
});

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider = ({ children }) => {
  // Force light mode for all pages except home - matching website design
  const [isDarkMode, setIsDarkMode] = useState(false); // Always default to light mode

  // Function to toggle dark mode (only used by home page)
  const toggleDarkMode = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('isDarkMode', JSON.stringify(newMode));
    }
  };

  // Ensure light theme is applied globally (except home page which handles its own dark theme)
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      // Remove any dark class that might be applied globally
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('dark');
    }
  }, []);

  return (
    <ThemeContext.Provider value={{ isDarkMode, toggleDarkMode }}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider; 