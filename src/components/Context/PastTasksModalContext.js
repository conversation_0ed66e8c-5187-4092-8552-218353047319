// context/PastTasksModalContext.js
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter ,usePathname,useSearchParams} from 'next/navigation';
import PastTaskDiscussionModal from "@/components/Modal/PastTaskDiscussionModal";
import {getPastDiscussionById} from "@/utils/api"

const PastTasksModalContext = createContext();

export const PastTasksModalProvider = ({ children }) => {
  const [isPastTasksModalOpen, setIsPastTasksModalOpen] = useState(false);
  const [pastTasks, setPastTasks] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(10); 
  const [skip, setSkip] = useState(0);
  const pathname = usePathname();
  const searchParams = useSearchParams()
  const router = useRouter();

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const viewPastDiscussions = params.get('view_past_discussions');
    const discussionType = params.get('discussion_type');
    const nodeId = params.get('node_id');

    if (viewPastDiscussions === 'true' && discussionType) {
      setIsPastTasksModalOpen(true);
      fetchPastDiscussions(nodeId)
    }
  }, [searchParams]);

  const fetchPastDiscussions = async (nodeId) => {
    try {
      const response = await getPastDiscussionById(nodeId);
      setPastTasks(response); // Assuming API returns data as a list of tasks
      setTotalCount(response.length); // Assuming API returns a total count
    } catch (error) {
      
      // Optionally handle the error state here
    }
  };

  const closeModal = () => {
    setIsPastTasksModalOpen(false);
    setPastTasks([]);
    // Remove the query parameters when the modal is closed
    const newSearchParams = new URLSearchParams(window.location.search);
    newSearchParams.delete('view_past_discussions');
    newSearchParams.delete('node_id');
    newSearchParams.delete('discussion_type');
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  return (
    <PastTasksModalContext.Provider
      value={{
        setIsPastTasksModalOpen,
        closeModal,
      }}
    >
      {children}

      {/* Render the modal when open */}
      {isPastTasksModalOpen && (
        <PastTaskDiscussionModal
          isOpen={isPastTasksModalOpen}
          onClose={closeModal}
          tasks={pastTasks}
          totalCount={totalCount || 0}
          limit={limit || 10}
          skip={skip || 0}
          onPageChange={(newPage) => {
            setSkip((newPage - 1) * limit); 
          }}
          onLimitChange={(newLimit) => {
            setLimit(newLimit);
            setSkip(0); 
          }}
          title="Past Discussion"
        />
      )}
    </PastTasksModalContext.Provider>
  );
  
};

export const usePastTasksModal = () => {
  return useContext(PastTasksModalContext);
};
