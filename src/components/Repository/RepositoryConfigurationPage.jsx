import React, { useState, useEffect } from 'react';

import {
  Copy as CopyIcon,
  Plus as AddIcon,
  Link as LinkIcon,
  Github as GithubIcon,
  GitBranch as GitlabIcon,
  Search as SearchIcon,
  Loader2 as LoaderIcon
} from 'lucide-react';
import { createRepository, getRepository, listRepositories, linkRepository } from '@/utils/repositoryAPI';
import { getSCMConfiguration, SCMType } from '@/utils/scmAPI';
import { decrypt } from '@/utils/hash';
import { updateNodeByPriority } from "@/utils/api";

// Repository Search Component
const RepositorySearch = ({ onSearch }) => {
  const [filters, setFilters] = useState({
    search: '',
    organization: '',
    visibility: ''
  });

  const handleSubmit = (e) => {
    e?.preventDefault();
    onSearch(filters);
  };

  const handleClear = () => {
    setFilters({
      search: '',
      organization: '',
      visibility: ''
    });
    onSearch({}); // Clear filters
  };

  const handleVisibilityChange = (e) => {
    const newFilters = {
      ...filters,
      visibility: e.target.value
    };
    setFilters(newFilters);
    onSearch(newFilters); // Auto-submit when visibility changes
  };

  return (
    <div className="mb-6">
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-12 gap-4">
          <div className="col-span-12 md:col-span-4">
            <div className="relative">
              <input
                type="text"
                className="w-full px-4 py-2 pl-10 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Search repositories..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              />
              <SearchIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
          </div>
          <div className="col-span-12 md:col-span-3">
            <input
              type="text"
              className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Organization name..."
              value={filters.organization}
              onChange={(e) => setFilters(prev => ({ ...prev, organization: e.target.value }))}
            />
          </div>
          <div className="col-span-12 md:col-span-3">
            <select
              className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              value={filters.visibility}
              onChange={handleVisibilityChange}
            >
              <option value="">All Visibility</option>
              <option value="public">Public</option>
              <option value="private">Private</option>
            </select>
          </div>
          <div className="col-span-12 md:col-span-2">
            <div className="flex gap-2">
              <button
                type="submit"
                className="w-full px-4 py-2 text-white bg-primary rounded-lg hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                Search
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

const DetailRow = ({ label, value, canCopy = false, onCopy }) => (
  <div className="grid grid-cols-12 gap-4 mb-4">
    <div className="col-span-4">
      <p className="typography-body-sm font-weight-medium text-gray-500">{label}</p>
    </div>
    <div className="col-span-8 flex items-center">
      <p className="typography-body-sm break-all">{value}</p>
      {canCopy && (
        <button
          onClick={() => onCopy(value)}
          className="ml-2 p-1 text-gray-500 hover:text-gray-700"
        >
          <CopyIcon className="h-4 w-4" />
        </button>
      )}
    </div>
  </div>
);

// Create Repository Form Component
const CreateRepositoryForm = ({ repository, onSubmit }) => {
  const [repositoryName, setRepositoryName] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [forceNew, setForceNew] = useState(true);
  const [nameError, setNameError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    setNameError('');
    setIsLoading(true);
    try {
      await onSubmit({ repositoryName, isPrivate, forceNew: false });
      // The modal will be closed by the parent component through onSuccess callback
    } catch (error) {
      setNameError(error.message || 'Failed to create repository');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNameChange = (e) => {
    setRepositoryName(e.target.value);
    if (nameError) setNameError('');
  };

  return (
    <div className="space-y-4 mt-4">
      <div>
        <input
          type="text"
          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${nameError ? 'border-red-500' : 'border-gray-300'
            }`}
          placeholder="Repository Name (Optional)"
          value={repositoryName}
          onChange={handleNameChange}
        />
        {nameError && (
          <p className="mt-1 typography-body-sm text-red-500">{nameError}</p>
        )}
        <p className="mt-1 typography-body-sm text-gray-500">Leave empty for auto-generated name</p>
      </div>
      <div className="flex items-center">
        <input
          type="checkbox"
          id="private"
          checked={isPrivate}
          onChange={(e) => setIsPrivate(e.target.checked)}
          className="h-4 w-4 text-primary focus:ring-primary-500 border-gray-300 rounded"
        />
        <label htmlFor="private" className="ml-2 typography-body-sm text-gray-700">
          Private Repository
        </label>
      </div>
      {repository && (
        <div className="flex items-center">
          <input
            type="checkbox"
            id="forceNew"
            checked={true}
            className="h-4 w-4 text-primary focus:ring-primary-500 border-gray-300 rounded"
          />
          <label htmlFor="forceNew" className="ml-2 typography-body-sm text-gray-700">
            Force Create New Repository
          </label>
        </div>
      )}
      <button
        onClick={handleSubmit}
        disabled={isLoading}
        className={`w-full px-4 py-2 text-white bg-primary rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 flex items-center justify-center ${isLoading ? 'opacity-75 cursor-not-allowed' : ''
          }`}
      >
        {isLoading ? (
          <>
            <LoaderIcon className="animate-spin h-5 w-5 mr-2" />
            Creating Repository...
          </>
        ) : (
          'Create Repository'
        )}
      </button>
    </div>
  );
};

// Main Repository Configuration Component
const RepositoryConfigurationPage = ({ projectId, containerId, onSuccess, handleRepoChange, initialSCMType = null }) => {
  const [repository, setRepository] = useState(null);
  const [loading, setLoading] = useState(true);
  const [scmConfigLoading, setScmConfigLoading] = useState(false);
  const [error, setError] = useState(null);
  const [copySuccess, setCopySuccess] = useState('');
  const [activeStep, setActiveStep] = useState(initialSCMType ? 1 : 0); // Skip to step 1 if SCM type is provided
  const [selectedSCMType, setSelectedSCMType] = useState(initialSCMType);
  const [scmConfigurations, setSCMConfigurations] = useState([]);
  const [selectedSCM, setSelectedSCM] = useState(null);
  const [repositoryList, setRepositoryList] = useState([]);
  const [selectedExistingRepo, setSelectedExistingRepo] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    organization: '',
    visibility: ''
  });
  const [activeTab, setActiveTab] = useState(0); // 0: Create, 1: Link

  useEffect(() => {
    fetchRepositoryDetails();

    // If initialSCMType is provided, set it as the selected SCM type
    if (initialSCMType) {
      setSelectedSCMType(initialSCMType);
      setActiveStep(1); // Skip to step 1 (Select Configuration)
    }
  }, [projectId, containerId, initialSCMType]);

  // Fetch SCM configurations when SCM type is selected
  useEffect(() => {
    if (selectedSCMType) {
      fetchSCMConfigurations();
    }
  }, [selectedSCMType]);

  const fetchRepositoryDetails = async () => {
    try {
      setLoading(true);
      const response = await getRepository(projectId, containerId);

      if (response.repository) {
        setRepository(response.repository);
        setActiveStep(3); // Skip to view step if repository exists
      } else {
        setRepository(null);
        setActiveStep(0);
      }
    } catch (err) {
      setError('');
    } finally {
      setLoading(false);
      // Fetch SCM configurations after repository details regardless of result
      fetchSCMConfigurations();
    }
  };

  const fetchSCMConfigurations = async () => {
    try {
      setScmConfigLoading(true);
      setError(null); // Clear any existing errors
      const response = await getSCMConfiguration();
      if (response.status === "success") {
        if (response.data === null || !response.data?.configurations) {
          setSCMConfigurations([]);
          // Don't set error here - let the UI handle the empty state
        } else {
          setSCMConfigurations(response.data.configurations || []);
        }
      } else {
        setSCMConfigurations([]);
      }
    } catch (err) {
      setSCMConfigurations([]);
      setError('Failed to fetch SCM configurations');
    } finally {
      setScmConfigLoading(false);
    }
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess('Copied!');
      setTimeout(() => setCopySuccess(''), 2000);
    } catch (err) {
      setCopySuccess('Failed to copy');
    }
  };

  const handleSCMTypeSelect = (scmType) => {
    setSelectedSCMType(scmType);
    setSelectedSCM(null);
    setRepositoryList([]);
    setSelectedExistingRepo(null);
    setFilters({
      search: '',
      organization: '',
      visibility: ''
    });
    // Reset SCM configurations, clear errors, and start loading
    setSCMConfigurations([]);
    setError(null);
    setScmConfigLoading(true);
  };

  const handleSCMSelect = async (config) => {
    try {
      const decryptedId = decrypt(config.encrypted_scm_id);
      setSelectedSCM({
        ...config,
        decryptedId,
        organization: config.credentials.organization
      });
      setCurrentPage(1);
      setRepositoryList([]);
      setSelectedExistingRepo(null);
      setFilters({
        search: '',
        organization: '',
        visibility: ''
      });
      setActiveStep(2);
    } catch (err) {
      setError('Failed to decrypt SCM ID');
    }
  };

  const updateContainerWithRepository = async (containerId, repository) => {
    try {
      // Update repository name
      await updateNodeByPriority(
        containerId, 
        'repository_name', 
        repository.repositoryName
      );
      
      // Update branch
      await updateNodeByPriority(
        containerId, 
        'branch', 
        repository.default_branch
      );
      
      // Update additional repository properties
      if (repository.repositoryId) {
        await updateNodeByPriority(
          containerId, 
          'repository_id', 
          repository.repositoryId
        );
      }
      
      if (repository.cloneUrlHttp) {
        await updateNodeByPriority(
          containerId, 
          'clone_url', 
          repository.cloneUrlHttp
        );
      }
      
      if (repository.cloneUrlHttp) {
        await updateNodeByPriority(
          containerId, 
          'http_url', 
          repository.cloneUrlHttp
        );
      }
      
      if (repository.cloneUrlSsh) {
        await updateNodeByPriority(
          containerId, 
          'ssh_url', 
          repository.cloneUrlSsh
        );
      }
      
      if (repository.organization) {
        await updateNodeByPriority(
          containerId, 
          'organization', 
          repository.organization
        );
      }
      
      // Update repository status
      await updateNodeByPriority(
        containerId, 
        'repository_status', 
        'initialized'
      );
      
    } catch (updateError) {
      console.error('Failed to update repository details:', updateError);
      throw updateError;
    }
  };

  const handleCreateRepository = async ({ repositoryName, isPrivate, forceNew }) => {
    try {
      setLoading(true);
      setError(null);
      const response = await createRepository(
        projectId,
        containerId,
        selectedSCM.decryptedId,
        {
          repository_name: repositoryName,
          is_private: isPrivate,
          force_new: true
        }
      );

      if (response.repository) {
        // First update the local state
        setRepository(response.repository);

         try {
          await updateContainerWithRepository(containerId, response.repository);

          // Update parent components and close modal
          handleRepoChange?.(response.repository);
          
          // Close the modal by calling onSuccess
          onSuccess?.(response.repository);
          
          // Return success to allow the form component to handle UI updates
          return true;
        } catch (updateError) {
          console.error('Failed to update repository details:', updateError);
          setError('Repository created but failed to update container details');
          return false;
        }
      } else {
        setError(response.error || 'Failed to create repository');
        return false;
      }
    } catch (err) {
      if (err.message.includes('already exists')) {
        setError('Repository name already exists. Please choose a different name.');
      } else if (err.message.includes('Organization') && err.message.includes('not found')) {
        setError('Organization not found. Repository will be created in your personal account.');
      } else {
        setError('Failed to create repository');
      }
      throw err; // Re-throw the error to be caught by the form component
    } finally {
      setLoading(false);
    }
  };

  const handleLinkRepository = async () => {
    try {
      setLoading(true);
      const response = await linkRepository(
        projectId,
        containerId,
        selectedSCM.decryptedId,
        {
          service: selectedExistingRepo.scm_type,
          repositoryName: selectedExistingRepo.repositoryName,
          repositoryId: selectedExistingRepo.repositoryId,
          cloneUrlHttp: selectedExistingRepo?.http_url || selectedExistingRepo?.clone_url,
          cloneUrlSsh: selectedExistingRepo?.ssh_url,
          organization: selectedExistingRepo.organization,
          encrypted_scm_id: selectedSCM.encrypted_scm_id,
          repositoryStatus: 'initialized'
        }
      );

      if (response.repository) {
        setRepository(response.repository);
        handleRepoChange?.(response.repository);
        setActiveStep(3);
        setError(null);
        onSuccess?.(response.repository);
      } else {
        setError('Failed to link repository');
      }
    } catch (err) {
      setError('Failed to link repository');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (newFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  useEffect(() => {
    const fetchRepositories = async () => {
      if (activeStep === 2 && selectedSCM && activeTab === 1) {
        try {
          setLoading(true);
          setError(null);

          const response = await listRepositories(
            selectedSCM.scm_type,
            selectedSCM.decryptedId,
            false,
            currentPage,
            perPage,
            filters
          );

          if (response.status === "success" && response.data?.repositories) {
            setRepositoryList(response.data.repositories);
            setTotalPages(response.data.pagination?.total_pages || 1);
            setPerPage(response.data.pagination?.per_page || 10);
          } else {
            setRepositoryList([]);
            setError('No repositories found');
          }
        } catch (err) {
          setRepositoryList([]);
          setError('Failed to fetch repository list: ' + (err.message || 'Unknown error'));
        } finally {
          setLoading(false);
        }
      }
    };

    fetchRepositories();
  }, [activeStep, selectedSCM, currentPage, perPage, filters, activeTab]);

  // Render different content based on the active step
  const renderStepContent = () => {
    return (
      <div className="space-y-6">
        {/* Step 0: Select SCM Type */}
        <div>
          <h3 className="typography-body-lg font-weight-semibold mb-4 text-gray-900">Select SCM</h3>
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => handleSCMTypeSelect(SCMType.GITHUB)}
              className={`flex items-center justify-center gap-2 p-4 border rounded-lg transition-colors ${selectedSCMType === SCMType.GITHUB
                  ? 'bg-primary-50 border-primary'
                  : 'border-gray-200 hover:bg-gray-50'
                }`}
            >
              <GithubIcon className={`h-6 w-6 ${selectedSCMType === SCMType.GITHUB ? 'text-primary' : ''}`} />
              <span className={selectedSCMType === SCMType.GITHUB ? 'text-primary font-weight-medium' : ''}>GitHub</span>
            </button>
            <button
              onClick={() => handleSCMTypeSelect(SCMType.GITLAB)}
              className={`flex items-center justify-center gap-2 p-4 border rounded-lg transition-colors ${selectedSCMType === SCMType.GITLAB
                  ? 'bg-primary-50 border-primary'
                  : 'border-gray-200 hover:bg-gray-50'
                }`}
            >
              <GitlabIcon className={`h-6 w-6 ${selectedSCMType === SCMType.GITLAB ? 'text-primary' : ''}`} />
              <span className={selectedSCMType === SCMType.GITLAB ? 'text-primary font-weight-medium' : ''}>GitLab</span>
            </button>
          </div>
        </div>

        {/* Step 1: Select SCM Configuration */}
        {selectedSCMType && (
          <div>
            <h2 className="typography-body-lg font-weight-semibold mb-6 text-gray-900">
              Select {selectedSCMType} Organisation
            </h2>
            {scmConfigLoading ? (
              <div className="flex items-center justify-center p-8">
                <div className="flex flex-col items-center">
                  <LoaderIcon className="animate-spin h-8 w-8 text-primary mb-3" />
                  <span className="typography-body-sm text-gray-600">Loading organisations...</span>
                </div>
              </div>
            ) : scmConfigurations.filter(config => config.scm_type === selectedSCMType).length > 0 ? (
              <div className="relative">
                <select
                  value={selectedSCM?.encrypted_scm_id || ''}
                  onChange={(e) => {
                    const selectedConfig = scmConfigurations.find(
                      config => config.encrypted_scm_id === e.target.value
                    );
                    if (selectedConfig) {
                      handleSCMSelect(selectedConfig);
                    }
                  }}
                  className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-none"
                >
                  <option value="" disabled>Select a Organisation</option>
                  {scmConfigurations
                    .filter(config => config.scm_type === selectedSCMType)
                    .map((config) => (
                      <option
                        key={config.encrypted_scm_id}
                        value={config.encrypted_scm_id}
                        className="py-2"
                      >
                        {config.credentials.organization}
                      </option>
                    ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-primary-50 text-primary-700 rounded-md">
                No repository configurations found. Please contact your organization administrator to connect to a {selectedSCMType} account.
              </div>
            )}
          </div>
        )}

        {/* Step 2: Create or Link Repository */}
        {selectedSCM && (
          <div>
            <div className="border-b border-gray-200 mb-6">
              <div className="flex space-x-8">
                <button
                  onClick={() => setActiveTab(0)}
                  className={`pb-4 px-1 border-b-2 font-weight-medium typography-body-sm ${activeTab === 0
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                >
                  <div className="flex items-center gap-2">
                    <AddIcon className="h-4 w-4" />
                    Create New Repository
                  </div>
                </button>
                <button
                  onClick={() => setActiveTab(1)}
                  className={`pb-4 px-1 border-b-2 font-weight-medium typography-body-sm ${activeTab === 1
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                >
                  <div className="flex items-center gap-2">
                    <LinkIcon className="h-4 w-4" />
                    Link Existing Repository
                  </div>
                </button>
              </div>
            </div>

            {activeTab === 0 ? (
              // Create Repository Tab
              <div>
                <h2 className="typography-body-lg font-weight-semibold mb-4 text-gray-900">
                  Create New Repository
                </h2>
                <CreateRepositoryForm
                  repository={repository}
                  onSubmit={handleCreateRepository}
                />
              </div>
            ) : (
              // Link Repository Tab
              <div>
                <h2 className="typography-body-lg font-weight-semibold mb-4 text-gray-900">
                  Link Existing Repository
                </h2>

                <RepositorySearch onSearch={handleSearch} />

                {loading ? (
                  <div className="flex justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <>
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden mb-4">
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="w-12 px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider"></th>
                              <th className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider">Repository Name</th>
                              <th className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider">Organization</th>
                              <th className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider">Visibility</th>
                              <th className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider">Last Activity</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {repositoryList.length > 0 ? (
                              repositoryList.map(repo => (
                                <tr
                                  key={repo.repositoryId}
                                  className={`hover:bg-gray-50 cursor-pointer ${selectedExistingRepo?.repositoryId === repo.repositoryId ? 'bg-primary-50' : ''
                                    }`}
                                  onClick={() => setSelectedExistingRepo({
                                    ...repo,
                                    scm_type: selectedSCM.scm_type,
                                    encrypted_scm_id: selectedSCM.encrypted_scm_id,
                                    clone_url: repo.http_url
                                  })}
                                >
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <input
                                      type="radio"
                                      name="repository-select"
                                      checked={selectedExistingRepo?.repositoryId === repo.repositoryId}
                                      onChange={() => setSelectedExistingRepo({
                                        ...repo,
                                        scm_type: selectedSCM.scm_type,
                                        encrypted_scm_id: selectedSCM.encrypted_scm_id,
                                        clone_url: repo.http_url
                                      })}
                                      className="h-4 w-4 text-primary focus:ring-primary-500 border-gray-300"
                                    />
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap truncate max-w-[300px] typography-body-sm text-gray-900" title={repo.repositoryName}>
                                    {repo.repositoryName}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap typography-body-sm text-gray-500">{repo.organization}</td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`px-2 py-1 inline-flex typography-caption leading-5 font-weight-semibold rounded-full ${repo.visibility === 'private' ? 'bg-red-100 text-red-800' :
                                      repo.visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                      }`}>
                                      {repo.visibility}
                                    </span>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap typography-body-sm text-gray-500">
                                    {new Date(repo.last_activity_at).toLocaleDateString()}
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan={5} className="px-6 py-8 text-center typography-body-sm text-gray-500">
                                  {filters.search
                                    ? `No repositories found matching "${filters.search}"`
                                    : "No repositories found"
                                  }
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    {repositoryList.length > 0 && totalPages > 1 && (
                      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-4">
                        <div className="typography-body-sm text-gray-700">
                          Showing page {currentPage} of {totalPages}
                        </div>
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                            className={`px-3 py-1 rounded-md typography-body-sm font-weight-medium ${currentPage === 1
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                              }`}
                          >
                            Previous
                          </button>
                          <div className="flex items-center gap-1">
                            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                              const pageNum = i + 1;
                              return (
                                <button
                                  key={pageNum}
                                  onClick={() => setCurrentPage(pageNum)}
                                  className={`px-3 py-1 rounded-md typography-body-sm font-weight-medium ${currentPage === pageNum
                                    ? 'bg-primary text-white'
                                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                                    }`}
                                >
                                  {pageNum}
                                </button>
                              );
                            })}
                          </div>
                          <button
                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages}
                            className={`px-3 py-1 rounded-md typography-body-sm font-weight-medium ${currentPage === totalPages
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                              }`}
                          >
                            Next
                          </button>
                        </div>
                      </div>
                    )}

                    <div className="mt-6 flex justify-end">
                      <button
                        onClick={handleLinkRepository}
                        disabled={!selectedSCM || !selectedExistingRepo || loading}
                        className={`px-4 py-2 rounded-lg text-white font-weight-medium ${!selectedSCM || !selectedExistingRepo || loading
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-primary hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2'
                          }`}
                      >
                        LINK REPOSITORY
                      </button>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full">
      {error && (
        <div className="mb-4 p-4 bg-red-50 text-red-700 rounded-md">
          {error}
        </div>
      )}
      <div className="mt-4">
        {renderStepContent()}
      </div>
    </div>
  );
};

export default RepositoryConfigurationPage;