// components/dashboard/DeploymentDetails.jsx
import React from 'react';
import { formatDate, getDeploymentStatusColor, getDeploymentStatusIcon, truncateId } from '../../lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import Button from '../ui/Button';

const DeploymentDetails = ({ 
  deploymentData, 
  onBackToDeployments,
  loading
}) => {

  // Loading state
  if (loading || !deploymentData) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-6 bg-gray-200 rounded w-48 animate-pulse" />
          <div className="h-8 bg-gray-200 rounded w-32 animate-pulse" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Array.from({ length: 4 }, (_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Array.from({ length: 5 }, (_, j) => (
                    <div key={j} className="h-3 bg-gray-200 rounded" />
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const { deployment, project_summary, related_deployments } = deploymentData;

  if (!deployment) {
    return (
      <div className="text-center py-12">
        <div className="text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Deployment Not Found</h3>
        <p className="text-sm text-gray-500 mb-4">
          The requested deployment could not be found or may have been deleted.
        </p>
        <Button onClick={onBackToDeployments} variant="outline">
          ← Back to Deployments
        </Button>
      </div>
    );
  }

  // Safe formatting functions
  const safeFormatDate = (dateString) => {
    try {
      return dateString ? formatDate(dateString) : 'Not available';
    } catch (error) {
      return 'Invalid date';
    }
  };



  // Get status display info - maps backend status to display format
  const getStatusDisplay = (status) => {
    // Normalize status to lowercase for consistent matching
    const normalizedStatus = status?.toLowerCase() || '';
    
    // Map various backend statuses to standardized display categories
    if (normalizedStatus === 'success') {
      return { label: 'Completed', color: 'text-green-700 bg-green-50 border-green-200' };
    }
    
    if (normalizedStatus === 'completed') {
      return { label: 'Completed', color: 'text-green-700 bg-green-50 border-green-200' };
    }
    
    if (normalizedStatus === 'processing' || normalizedStatus === 'in_progress' || normalizedStatus === 'deploying') {
      return { label: 'Processing', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' };
    }
    
    // Handle various failure statuses
    if (normalizedStatus.includes('failed') || normalizedStatus.includes('error') || normalizedStatus === 'fail') {
      return { label: 'Failed', color: 'text-red-700 bg-red-50 border-red-200' };
    }
    
    // Default fallback for unknown statuses
    return { 
      label: status || 'Unknown', 
      color: 'text-gray-700 bg-gray-50 border-gray-200' 
    };
  };

  const statusDisplay = getStatusDisplay(deployment.status);

  return (
    <div className="space-y-8">
      {/* Enhanced Header */}
      <div className="bg-white border-b border-gray-200 pb-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Deployment Overview</h1>
            <p className="text-gray-600 mt-1 text-lg">
              Managing deployment: <span className="font-mono font-semibold">{truncateId(deployment.deployment_id, 20)}</span>
            </p>
          </div>
          <Button variant="outline" onClick={onBackToDeployments} className="flex items-center gap-2">
            <span>←</span> Back to Deployments
          </Button>
        </div>

        {/* Status Banner */}
        <div className={`border rounded-lg p-6 ${statusDisplay.color}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-4xl">
                {getDeploymentStatusIcon(deployment.status)}
              </div>
              <div>
                <h3 className="text-xl font-semibold">
                  Deployment Status: {statusDisplay.label}
                </h3>
                                 <p className="text-sm opacity-80 mt-1">
                   {deployment.message || `Deployment status: ${statusDisplay.label}`}
                 </p>
                {deployment.app_url && (
                  <a 
                    href={deployment.app_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 mt-2 text-sm font-medium hover:underline"
                  >
                    🌐 View Live Application
                  </a>
                )}
              </div>
            </div>
            

          </div>
        </div>
      </div>

      {/* Key Information Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Application Access */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🌐 Application Access
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-1">Application URL</label>
              {deployment.app_url ? (
                <a 
                  href={deployment.app_url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 text-sm break-all block p-2 bg-blue-50 rounded border"
                >
                  {deployment.app_url}
                </a>
              ) : (
                <span className="text-gray-400 text-sm block p-2 bg-gray-50 rounded border">Not available</span>
              )}
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-1">Custom Domain</label>
              {deployment.custom_domain ? (
                <a 
                  href={deployment.custom_domain} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 text-sm block p-2 bg-blue-50 rounded border"
                >
                  {deployment.custom_domain}
                </a>
              ) : (
                <span className="text-gray-400 text-sm block p-2 bg-gray-50 rounded border">Not configured</span>
              )}
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 block mb-1">Subdomain</label>
              <span className="text-sm block p-2 bg-gray-50 rounded border font-mono">
                {deployment.subdomain || 'Not assigned'}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Project & Build Info */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🔧 Project & Build Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-1">Project ID</label>
                  <span className="font-mono text-sm block p-2 bg-gray-50 rounded border">
                    {deployment.project_id || 'Not assigned'}
                  </span>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-1">Branch</label>
                  <span className="font-medium bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm inline-block">
                    {deployment.branch_name || 'Not specified'}
                  </span>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-1">Task ID</label>
                  <span className="font-mono text-sm block p-2 bg-gray-50 rounded border">
                    {deployment.task_id || 'Not assigned'}
                  </span>
                </div>
              </div>
              
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-1">Build Command</label>
                  <code className="text-sm bg-gray-900 text-gray-100 p-2 rounded block font-mono">
                    {deployment.command || 'Not specified'}
                  </code>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-1">Created</label>
                  <span className="text-sm block p-2 bg-gray-50 rounded border">
                    {safeFormatDate(deployment.created_at)}
                  </span>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-1">Last Updated</label>
                  <span className="text-sm block p-2 bg-gray-50 rounded border">
                    {safeFormatDate(deployment.updated_at)}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Technical Details Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            ⚙️ Technical Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-1">Deployment ID</label>
              <span className="font-mono text-xs block p-2 bg-gray-50 rounded border break-all">
                {deployment.deployment_id}
              </span>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-1">App ID</label>
              <span className="font-mono text-xs block p-2 bg-gray-50 rounded border">
                {deployment.app_id || 'Not assigned'}
              </span>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-1">Job ID</label>
              <span className="font-mono text-xs block p-2 bg-gray-50 rounded border">
                {deployment.job_id || 'Not assigned'}
              </span>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-1">Root Path</label>
              <span className="font-mono text-xs block p-2 bg-gray-50 rounded border break-all">
                {deployment.root_path || 'Not specified'}
              </span>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-1">Build Path</label>
              <span className="font-mono text-xs block p-2 bg-gray-50 rounded border break-all">
                {deployment.build_path || 'Not specified'}
              </span>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-1">Artifact Path</label>
              <span className="font-mono text-xs block p-2 bg-gray-50 rounded border break-all">
                {deployment.artifact_path || 'Not specified'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Project Summary */}
      {project_summary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📊 Project Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <div className="text-4xl font-bold text-gray-900">
                  {project_summary.total_deployments || 0}
                </div>
                <div className="text-sm text-gray-600 mt-2">Total Deployments</div>
                <div className="text-xs text-gray-500 mt-1">
                  Project deployments count
                </div>
              </div>
              
              <div className="text-center p-6 bg-blue-50 rounded-lg">
                <div className="text-4xl font-bold text-blue-600">
                  {project_summary.unique_branches ? project_summary.unique_branches.length : 0}
                </div>
                <div className="text-sm text-gray-600 mt-2">Branches</div>
                <div className="text-xs text-gray-500 mt-1">
                  Latest: {project_summary.latest_deployment ? 
                    safeFormatDate(project_summary.latest_deployment) : 
                    'Never'
                  }
                </div>
              </div>
            </div>
            
            {/* Latest Deployment Info */}
            {project_summary.latest_deployment && (
              <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-green-800">Latest Deployment</h4>
                    <p className="text-xs text-green-600 mt-1">
                      {safeFormatDate(project_summary.latest_deployment)}
                    </p>
                  </div>
                  <div className="text-2xl">🚀</div>
                </div>
              </div>
            )}
            
            {/* Active Branches */}
            {project_summary.unique_branches && Array.isArray(project_summary.unique_branches) && project_summary.unique_branches.length > 0 && (
              <div className="mt-6 pt-4 border-t border-gray-200">
                <label className="text-sm font-medium text-gray-700 block mb-3">Active Branches</label>
                <div className="flex flex-wrap gap-2">
                  {project_summary.unique_branches.map((branch, index) => (
                    <span 
                      key={index} 
                      className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium border border-blue-200"
                    >
                      📋 {branch}
                    </span>
                  ))}
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  {project_summary.unique_branches.length} active branch{project_summary.unique_branches.length !== 1 ? 'es' : ''} in this project
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Related Deployments */}
      {related_deployments && related_deployments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🔄 Related Deployments
              <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-normal">
                {related_deployments.length}
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {related_deployments.map((relatedDeployment, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <span className="text-xl">
                      {getDeploymentStatusIcon(relatedDeployment.status)}
                    </span>
                    <div>
                      <div className="text-sm font-medium font-mono">
                        {truncateId(relatedDeployment.deployment_id, 16)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {safeFormatDate(relatedDeployment.created_at)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`px-2 py-1 text-xs rounded-full border ${getDeploymentStatusColor(relatedDeployment.status)}`}>
                      {relatedDeployment.status}
                    </span>
                    {relatedDeployment.app_url && (
                      <a 
                        href={relatedDeployment.app_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 p-1"
                        title="View Application"
                      >
                        🔗
                      </a>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}


    </div>
  );
};

export default DeploymentDetails;