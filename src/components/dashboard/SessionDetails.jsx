// components/dashboard/SessionDetails.jsx
import { formatCurrency, getStatusColor } from '../../lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import Button from '../ui/Button';

const AgentCostBar = ({ agent, cost, totalCost }) => {
  // Handle edge cases for cost calculation
  const safeCost = cost || 0;
  const safeTotalCost = totalCost || 0;
  const percentage = safeTotalCost > 0 ? (safeCost / safeTotalCost) * 100 : 0;
  
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <span className="font-weight-medium typography-body-sm">{agent || 'Unknown Agent'}</span>
        <span className="typography-body-sm font-weight-semibold">{formatCurrency(safeCost)}</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-primary-600 h-2 rounded-full transition-all duration-500 ease-out" 
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
      <div className="typography-caption text-gray-500">
        {percentage.toFixed(1)}% of total cost
      </div>
    </div>
  );
};

const SessionDetails = ({ session, onBackToSessions }) => {
  // Loading state
  if (!session) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-6 bg-gray-200 rounded w-48 animate-pulse" />
          <div className="h-8 bg-gray-200 rounded w-32 animate-pulse" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Array.from({ length: 4 }, (_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Array.from({ length: 5 }, (_, j) => (
                    <div key={j} className="h-3 bg-gray-200 rounded" />
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Safely extract nested data with fallbacks
  const sessionData = session.session_data || {};
  const sessionName = sessionData.session_name || 'Unnamed Session';
  const platform = sessionData.platform || 'unknown';
  const llmModel = sessionData.llm_model || 'unknown';
  const status = session.status || 'unknown';
  const statusColor = getStatusColor(status);
  
  // Handle agent costs with proper validation
  const agentCosts = session.agent_costs || {};
  const activeAgents = Object.entries(agentCosts)
    .filter(([agent, cost]) => agent && cost > 0)
    .sort(([,a], [,b]) => (b || 0) - (a || 0));

  // Safe formatting functions
  const safeFormatDate = (input) => {
  try {
    const timestamp = input?.endsWith('Z') ? input : input + 'Z';
    const date = new Date(timestamp);

    if (isNaN(date.getTime())) return 'Invalid date';

   const formatted = date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    });
  return formatted.replace(/\b(am|pm)\b/i, (match) => match.toUpperCase());
  } catch {
    return 'Invalid date';
  }
};



  const safeFormatCurrency = (amount) => {
    try {
      return formatCurrency(amount || 0);
    } catch (error) {
      return '$0.00';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="typography-heading-4 font-weight-semibold text-gray-900">Session Details</h2>
        <Button variant="ghost" onClick={onBackToSessions}>
          ← Back to Sessions
        </Button>
      </div>

      {/* Session Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Session Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-500">Session Name:</span>
                <span className="font-weight-medium">{sessionName}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-500">Status:</span>
                <span className={`px-2 py-1 typography-caption font-weight-medium rounded-full ${statusColor}`}>
                  {status}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Task ID:</span>
                <span className=" typography-body-sm">
                  {session.task_id || 'Not assigned'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Project ID:</span>
                <span className=" typography-body-sm">
                  {session.project_id || 'Not assigned'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Container ID:</span>
                <span className=" typography-body-sm">
                  {session.container_id || 'Not assigned'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Service Type:</span>
                <span className="font-weight-medium capitalize">
                  {session.service_type 
                    ? session.service_type.replace('-', ' ')
                    : 'Not specified'
                  }
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Platform:</span>
                <span className="font-weight-medium capitalize">{platform}</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Timing & Cost</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-500">Started:</span>
                <span className="font-weight-medium">
                  {safeFormatDate(session.session_start)}
                </span>
              </div>
              {session.session_end ? (
                <div className="flex justify-between">
                  <span className="text-gray-500">Ended:</span>
                  <span className="font-weight-medium">
                    {safeFormatDate(session.session_end)}
                  </span>
                </div>
              ) : (
                <div className="flex justify-between">
                  <span className="text-gray-500">Status:</span>
                  <span className="font-weight-medium text-primary">In Progress</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-500">Last Updated:</span>
                <span className="font-weight-medium">
                  {safeFormatDate(session.last_updated)}
                </span>
              </div>
              <div className="flex justify-between border-t pt-3">
                <span className="text-gray-500">Total Cost:</span>
                <span className="font-weight-bold text-green-600 typography-body-lg">
                  {safeFormatCurrency(session.total_cost)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">LLM Model:</span>
                <span className=" typography-caption">
                  {llmModel !== 'unknown' 
                    ? llmModel.split('/').pop() 
                    : 'Not specified'
                  }
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Agent Costs Breakdown */}
      {activeAgents.length > 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>Agent Costs Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {activeAgents.map(([agent, cost]) => (
                <AgentCostBar
                  key={agent}
                  agent={agent}
                  cost={cost}
                  totalCost={session.total_cost}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Agent Costs Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-gray-500">
              <p>No agent cost data available for this session.</p>
              {Object.keys(agentCosts).length === 0 && (
                <p className="typography-body-sm mt-2">
                  Agent costs will appear here once the session generates expenses.
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SessionDetails;