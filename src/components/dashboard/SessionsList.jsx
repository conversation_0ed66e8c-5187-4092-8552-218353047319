// components/dashboard/SessionsList.jsx
import React, { useState, useMemo } from 'react';
import { formatCurrency, getStatusColor } from '../../lib/utils';
import Button from '../ui/Button';

const SessionsList = ({ 
  sessions, 
  userId, 
  dateFrom, 
  dateTo, 
  onDateFromChange, 
  onDateToChange, 
  onFilterSessions,
  onSessionSelect, 
  onBackToUsers,
  loading,
  error,
  onRetry
}) => {
  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState('session_start');
  const [sortDirection, setSortDirection] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');
  const [serviceTypeFilter, setServiceTypeFilter] = useState('all');

  // Safe sessions array handling
  const sessionsList = Array.isArray(sessions) ? sessions : [];

  // Filter sessions based on search term and service type
  const filteredSessions = useMemo(() => {
    let filtered = sessionsList;

    // Apply service type filter
    if (serviceTypeFilter !== 'all') {
      filtered = filtered.filter(session => 
        session?.service_type === serviceTypeFilter
      );
    }

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(session => {
        const sessionName = session?.session_data?.session_name || '';
        const taskId = session?.task_id || '';
        const description = session?.session_data?.description || '';
        
        return sessionName.toLowerCase().includes(searchLower) ||
               taskId.toLowerCase().includes(searchLower) ||
               description.toLowerCase().includes(searchLower);
      });
    }

    return filtered;
  }, [sessionsList, searchTerm, serviceTypeFilter]);

  // Sort sessions
  const sortedSessions = useMemo(() => {
    return [...filteredSessions].sort((a, b) => {
      let aVal = a[sortField];
      let bVal = b[sortField];

      // Handle nested properties
      if (sortField === 'session_name') {
        aVal = a?.session_data?.session_name || '';
        bVal = b?.session_data?.session_name || '';
      } else if (sortField === 'platform') {
        aVal = a?.session_data?.platform || '';
        bVal = b?.session_data?.platform || '';
      }

      // Handle different data types
      if (sortField === 'session_start' || sortField === 'last_updated') {
        aVal = aVal ? new Date(aVal).getTime() : 0;
        bVal = bVal ? new Date(bVal).getTime() : 0;
      } else if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase();
        bVal = bVal?.toLowerCase() || '';
      } else if (typeof aVal === 'number') {
        aVal = aVal || 0;
        bVal = bVal || 0;
      }

      if (sortDirection === 'asc') {
        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
      } else {
        return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
      }
    });
  }, [filteredSessions, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(sortedSessions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedSessions = sortedSessions.slice(startIndex, startIndex + itemsPerPage);

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
    setCurrentPage(1);
  };

  // Handle search
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle service type filter
  const handleServiceTypeFilter = (value) => {
    setServiceTypeFilter(value);
    setCurrentPage(1);
  };

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1);
  };

  // Safe formatting functions
  const safeFormatDate = (input) => {
  try {
    const timestamp = input?.endsWith('Z') ? input : input + 'Z';
    const date = new Date(timestamp);

    if (isNaN(date.getTime())) return 'Invalid date';

   const formatted = date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    });
  return formatted.replace(/\b(am|pm)\b/i, (match) => match.toUpperCase());
  } catch {
    return 'Invalid date';
  }
};



  const safeFormatCurrency = (amount) => {
    try {
      return formatCurrency(amount || 0);
    } catch (error) {
      return '$0.00';
    }
  };

  const safeGetStatusColor = (status) => {
    try {
      return getStatusColor(status);
    } catch (error) {
      return 'bg-gray-100 text-gray-800';
    }
  };

  // Sort icon component
  const SortIcon = ({ field }) => {
    if (sortField !== field) {
      return <span className="text-gray-400 ml-1">↕️</span>;
    }
    return (
      <span className="text-primary ml-1">
        {sortDirection === 'asc' ? '↑' : '↓'}
      </span>
    );
  };

  // Pagination component
  const Pagination = () => {
    const getVisiblePages = () => {
      const pages = [];
      const showPages = 5;
      let start = Math.max(1, currentPage - Math.floor(showPages / 2));
      let end = Math.min(totalPages, start + showPages - 1);
      
      if (end - start < showPages - 1) {
        start = Math.max(1, end - showPages + 1);
      }
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    };

    return (
      <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <p className="text-sm text-gray-700 whitespace-nowrap">
              Showing{' '}
              <span className="font-medium">{Math.min(startIndex + 1, sortedSessions.length)}</span>
              {' '}to{' '}
              <span className="font-medium">
                {Math.min(startIndex + itemsPerPage, sortedSessions.length)}
              </span>
              {' '}of{' '}
              <span className="font-medium">{sortedSessions.length}</span>
              {' '}results
              {(searchTerm || serviceTypeFilter !== 'all') && (
                <span className="text-gray-500"> (filtered)</span>
              )}
            </p>
            
            <div className="flex items-center space-x-2 whitespace-nowrap">
              <label className="text-sm text-gray-700">Show:</label>
              <select
                value={itemsPerPage}
                onChange={(e) => handleItemsPerPageChange(e.target.value)}
                className="border border-gray-300 rounded-md px-6 py-1 text-sm focus:ring-2 focus:ring-primary focus:border-primary bg-white min-w-[100px]"
              >
                <option value={5}>5 per page</option>
                <option value={10}>10 per page</option>
                <option value={25}>25 per page</option>
                <option value={50}>50 per page</option>
                <option value={100}>100 per page</option>
              </select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {totalPages > 1 ? (
              <nav className="flex items-center space-x-1">
                <button
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className="px-2 py-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  First
                </button>
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-2 py-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  ←
                </button>
                
                {getVisiblePages().map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-1 text-sm font-medium border rounded-md ${
                      page === currentPage
                        ? 'bg-primary-600 text-white border-primary-600 hover:bg-primary-700'
                        : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-2 py-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  →
                </button>
                <button
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                  className="px-2 py-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Last
                </button>
              </nav>
            ) : (
              <div className="text-sm text-gray-500 whitespace-nowrap">
                Page 1 of 1
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Loading skeleton
  const LoadingSkeleton = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="h-6 bg-gray-200 rounded w-48 animate-pulse" />
        <div className="flex space-x-4">
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse" />
          <div className="h-8 bg-gray-200 rounded w-32 animate-pulse" />
        </div>
      </div>
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            <tr>
              {Array.from({ length: 8 }, (_, i) => (
                <th key={i} className="px-6 py-3">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {Array.from({ length: 10 }, (_, i) => (
              <tr key={i}>
                {Array.from({ length: 8 }, (_, j) => (
                  <td key={j} className="px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Error state
  const ErrorState = () => (
    <div className="text-center py-12">
      <div className="text-6xl mb-6">⚠️</div>
      <h3 className="text-xl font-medium mb-2 text-gray-900">Unable to Load Sessions</h3>
      <p className="text-sm max-w-md mx-auto mb-4 text-gray-500">
        {error?.message || 'There was an error loading sessions. Please try again.'}
      </p>
      {onRetry && (
        <Button onClick={onRetry} variant="outline">
          Try Again
        </Button>
      )}
    </div>
  );

  // Empty state
  const EmptyState = () => {
    const hasFilters = searchTerm || serviceTypeFilter !== 'all' || dateFrom || dateTo;
    
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-6">
          {hasFilters ? '🔍' : '📝'}
        </div>
        <h3 className="text-xl font-medium mb-2 text-gray-900">
          {hasFilters ? 'No Sessions Found' : 'No Sessions Available'}
        </h3>
        <p className="text-sm max-w-md mx-auto text-gray-500">
          {hasFilters 
            ? 'No sessions match your current filters. Try adjusting your search criteria.'
            : `No sessions have been found for this user. Sessions will appear here once they are created.`
          }
        </p>
        {hasFilters && (
          <div className="mt-4 space-x-2">
            <Button 
              onClick={() => {
                setSearchTerm('');
                setServiceTypeFilter('all');
                onDateFromChange?.('');
                onDateToChange?.('');
              }} 
              variant="outline"
            >
              Clear All Filters
            </Button>
          </div>
        )}
      </div>
    );
  };

  // Handle loading state
  if (loading) {
    return <LoadingSkeleton />;
  }

  // Handle error state
  if (error) {
    return <ErrorState />;
  }

  // Validate required props
  if (!userId) {
    return (
      <div className="text-center text-red-500 py-8">
        <p>Error: User ID is required to display sessions.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Filters and Back Button */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="typography-heading-4 font-weight-semibold text-gray-900">
          Sessions for User{' '}
          <span className="text-primary typography-body-sm break-all">
            {userId}
          </span>
        </h2>
        <Button variant="ghost" onClick={onBackToUsers}>
          ← Back to Users
        </Button>
      </div>

      {/* Filters Section */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 items-end">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Sessions
            </label>
            <input
              type="text"
              placeholder="Search by name, task ID"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Service Type
            </label>
            <select
              value={serviceTypeFilter}
              onChange={(e) => handleServiceTypeFilter(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
            >
              <option value="all">All Service Types</option>
              <option value="code-generation">Code Generation</option>
              <option value="code-maintenance">Code Maintenance</option>
              <option value="deep-query">Deep Query</option>
              <option value="code-query">Code Query</option>
              <option value="interactive-configuration">Interactive Configuration</option>
              <option value="auto-configuration">Auto Configuration</option>
            </select>
          </div>
          
          {/* <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              From Date
            </label>
            <input
              type="date"
              value={dateFrom || ''}
              onChange={(e) => onDateFromChange?.(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
              max={dateTo || undefined}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              To Date
            </label>
            <input
              type="date"
              value={dateTo || ''}
              onChange={(e) => onDateToChange?.(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
              min={dateFrom || undefined}
            />
          </div>
          
          <div className="flex space-x-2">
            <Button 
              onClick={onFilterSessions} 
              loading={loading} 
              size="sm"
              disabled={!dateFrom && !dateTo}
            >
              Filter by Date
            </Button>
          </div> */}
        </div>
      </div>

      {/* Table with improved layout */}
      {sortedSessions.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg bg-white">
          {/* Make table horizontally scrollable on smaller screens */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-300">
              <thead className="bg-gray-50">
                <tr>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors min-w-[280px]"
                    onClick={() => handleSort('session_name')}
                  >
                    <div className="flex items-center">
                      <span>Session</span>
                      <SortIcon field="session_name" />
                    </div>
                  </th>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors min-w-[120px]"
                    onClick={() => handleSort('task_id')}
                  >
                    <div className="flex items-center">
                      <span>Task ID</span>
                      <SortIcon field="task_id" />
                    </div>
                  </th>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors min-w-[140px]"
                    onClick={() => handleSort('service_type')}
                  >
                    <div className="flex items-center">
                      <span>Service Type</span>
                      <SortIcon field="service_type" />
                    </div>
                  </th>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors min-w-[100px]"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center">
                      <span>Status</span>
                      <SortIcon field="status" />
                    </div>
                  </th>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors min-w-[100px]"
                    onClick={() => handleSort('platform')}
                  >
                    <div className="flex items-center">
                      <span>Platform</span>
                      <SortIcon field="platform" />
                    </div>
                  </th>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors min-w-[150px]"
                    onClick={() => handleSort('session_start')}
                  >
                    <div className="flex items-center">
                      <span>Started</span>
                      <SortIcon field="session_start" />
                    </div>
                  </th>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors min-w-[100px]"
                    onClick={() => handleSort('total_cost')}
                  >
                    <div className="flex items-center">
                      <span>Total Cost</span>
                      <SortIcon field="total_cost" />
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedSessions.map((session, index) => {
                  // Ensure session has valid structure
                  if (!session || typeof session !== 'object') {
                    return (
                      <tr key={`invalid-session-${index}`} className="bg-yellow-50">
                        <td colSpan={8} className="px-4 py-4 text-center text-yellow-600">
                          Invalid session data
                        </td>
                      </tr>
                    );
                  }

                  const sessionData = session?.session_data || {};
                  const sessionName = sessionData.session_name || 'Unnamed Session';
                  const description = sessionData.description || 'No description available';
                  const platform = sessionData.platform || 'unknown';
                  const status = session?.status || 'unknown';
                  const taskId = session?.task_id || 'Not assigned';
                  const serviceType = session?.service_type || 'not-specified';
                  const sessionStart = session?.session_start;
                  const totalCost = session?.total_cost || 0;
                  const statusColor = safeGetStatusColor(status);
                  const hasValidTaskId = taskId !== 'Not assigned';

                  const truncateTaskId = (id, maxLength = 8) => {
                    if (id.length <= maxLength) return id;
                    return `${id.substring(0, maxLength)}...`;
                  };

                  return (
                    <tr 
                      key={session._id || session.id || taskId || index}
                      className={`transition-colors cursor-pointer ${
                        hasValidTaskId ? 'hover:bg-gray-50' : 'cursor-default opacity-75'
                      }`}
                      onClick={() => hasValidTaskId && onSessionSelect?.(taskId)}
                    >
                      {/* Session Column with better text handling */}
                      <td className="px-4 py-4">
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                              <span className="text-primary font-medium text-sm">
                                📝
                              </span>
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-900 break-words">
                              <div title={sessionName} className="line-clamp-2">
                                {sessionName}
                              </div>
                            </div>
                            <div className="text-xs text-gray-500 mt-1 break-words">
                              <div title={description} className="line-clamp-2">
                                {description}
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                      
                      {/* Task ID Column */}
                      <td className="px-4 py-4">
                       {serviceType === 'auto-configuration' ? (
                          <span 
                            className="text-sm text-gray-900 font-mono break-all cursor-help" 
                            title={taskId}
                          >
                            {truncateTaskId(taskId)}
                          </span>
                        ) : (
                          <span className="text-sm text-gray-900 font-mono break-all">
                            {taskId}
                          </span>
                        )}
                      </td>
                      
                      {/* Service Type Column */}
                      <td className="px-4 py-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 capitalize whitespace-nowrap">
                          {serviceType.replace('-', ' ')}
                        </span>
                      </td>
                      
                      {/* Status Column */}
                      <td className="px-4 py-4">
                        {serviceType === 'interactive-configuration' ? (
                          <span className="inline-flex items-center text-xs font-medium text-gray-400 italic">
                            -
                          </span>
                        ) : (
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor} whitespace-nowrap`}>
                            {status}
                          </span>
                        )}
                      </td>
                      
                      {/* Platform Column */}
                      <td className="px-4 py-4">
                        <span className="text-sm text-gray-900 capitalize whitespace-nowrap">
                          {platform}
                        </span>
                      </td>
                      
                      {/* Started Column */}
                      <td className="px-4 py-4">
                        <span className="text-sm text-gray-500 whitespace-nowrap">
                          {safeFormatDate(sessionStart)}
                        </span>
                      </td>
                      
                      {/* Total Cost Column */}
                      <td className="px-4 py-4">
                        <span className="text-sm font-semibold text-green-600 whitespace-nowrap">
                          {safeFormatCurrency(totalCost)}
                        </span>
                      </td>
                      
                      {/* Actions Column */}
                      <td className="px-4 py-4 text-right">
                        <Button
                          size="sm"
                          variant="outline"
                          disabled={!hasValidTaskId}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (hasValidTaskId && onSessionSelect) {
                              onSessionSelect(taskId);
                            }
                          }}
                        >
                          View Details
                        </Button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          <Pagination />
        </div>
      )}

      {/* Summary */}
      {!loading && sortedSessions.length > 0 && (
        <div className="text-sm text-gray-500">
          {(searchTerm || serviceTypeFilter !== 'all') ? (
            <span>
              Found {filteredSessions.length} session{filteredSessions.length !== 1 ? 's' : ''} matching your filters
              out of {sessionsList.length} total session{sessionsList.length !== 1 ? 's' : ''}
            </span>
          ) : (
            <span>
              Showing {sessionsList.length} session{sessionsList.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default SessionsList;