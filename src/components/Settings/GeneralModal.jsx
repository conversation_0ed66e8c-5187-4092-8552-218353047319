import React, { useState, useContext, useEffect, useRef } from "react";
import moment from "moment-timezone";
import { AlertContext } from "../NotificationAlertService/AlertList";
import {
  getUserConfigurationDetails,
  updateConfigurationDetails,
  getUserById,
  editUserProfile,
  checkUserActive,
  uploadProfilePicture,
  deleteProfilePicture
} from "@/utils/api";
import Image from "next/image";
import { getCookie } from "@/utils/auth";
import { FaEdit, FaSave, FaClock, FaSearch } from "react-icons/fa";

import StatusIndicator from "../UIComponents/Indicators/StatusIndicator";
import { ProfileDetailsLoadingSkeleton } from "@/components/UIComponents/Loaders/LoaderGroup"
import DeleteConfirmationModal from "../Modal/DeleteConfirmationModal";
import { Camera, Trash2, Loader2 } from 'lucide-react';

const GeneralSettings = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [userId, setUserId] = useState(null);
  const [showTimezoneDropdown, setShowTimezoneDropdown] = useState(false);
  const [timezoneSearch, setTimezoneSearch] = useState("");
  const dropdownRef = useRef(null);
  const { showAlert } = useContext(AlertContext);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeletingImage, setIsDeletingImage] = useState(false);

  const [profile, setProfile] = useState({
    firstName: "",
    lastName: "",
    email: "",
    designation: "",
    department: "",
    timezone: "",
    profilePicture: null
  });

  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const fileInputRef = useRef(null);
  const [imageError, setImageError] = useState(false);

  const [isUserActive, setIsUserActive] = useState(false);
  const [isCheckingActive, setIsCheckingActive] = useState(true);

  useEffect(() => {
    const checkActive = async () => {
      try {
        setIsCheckingActive(true); // Start loading
        const activeStatus = await checkUserActive();
        setIsUserActive(activeStatus.active);
      } catch (error) {

        showAlert("Error checking user status", "error");
      } finally {
        setIsCheckingActive(false); // End loading regardless of result
      }
    };

    checkActive();
  }, []);


  const handleDeleteProfilePicture = async () => {
    setIsDeleteModalOpen(false);
    try {
      setIsDeletingImage(true);
      await deleteProfilePicture(userId);
      setProfile(prev => ({ ...prev, profilePicture: null }));
      setPreviewImage(null);
      setImageError(false);
      showAlert("Profile picture deleted successfully", "success");
    } catch (error) {
      showAlert("Error deleting profile picture", "error");
    } finally {
      setIsDeletingImage(false);
    }
  };
  // Load user details on mount
  useEffect(() => {
    loadUserDetails();
  }, []);

  // Handle clicks outside timezone dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowTimezoneDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const loadUserDetails = async () => {
    try {
      setIsLoading(true);
      const userIdVal = await getCookie("userId");
      setUserId(userIdVal);

      // Load user profile
      const userResponse = await getUserById(userIdVal);
      const names = userResponse.Name.split(" ");

      // Load timezone configuration
      const tzConfig = await getUserConfigurationDetails(userIdVal, "timezone");

      setProfile({
        firstName: names[0],
        lastName: names.length > 1 ? names[1] : "",
        email: userResponse.Email,
        designation: userResponse.Designation,
        department: userResponse.Department,
        timezone: tzConfig?.configuration?.timezone || "",
        profilePicture: userResponse.Picture || null
      });

      localStorage.setItem("username", userResponse.Name);
    } catch (error) {
      showAlert("Error loading user details", "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Preview image
    const reader = new FileReader();
    reader.onloadend = () => {
      setPreviewImage(reader.result);
    };
    reader.readAsDataURL(file);

    // Upload image
    setIsUploadingImage(true);
    try {
      // Remove userId parameter - just pass the file
      const response = await uploadProfilePicture(file);
      // Update to use the correct response property based on your API
      setProfile(prev => ({ ...prev, profilePicture: response.url })); // Note: changed from response.imageUrl to response.url
      showAlert("Profile picture updated successfully", "success");
    } catch (error) {
      // Add this to see detailed error
      showAlert("Error uploading profile picture", "error");
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleSave = async (e) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // Update profile
      const updatedProfile = {
        Name: `${profile.firstName} ${profile.lastName}`,
        Designation: profile.designation,
        Department: profile.department,
      };

      await editUserProfile(userId, updatedProfile);

      // Update timezone if changed
      if (profile.timezone) {
        await updateConfigurationDetails(userId, "timezone", profile.timezone);
      }

      showAlert("Profile updated successfully", "success");
      setIsEditing(false);
    } catch (error) {
      showAlert("Error updating profile", "error");
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setProfile(prev => ({ ...prev, [name]: value }));
  };

  const filteredTimezones = moment.tz.names().filter(tz =>
    tz.toLowerCase().includes(timezoneSearch.toLowerCase())
  );

  if (isLoading) {
    return (
      <ProfileDetailsLoadingSkeleton />
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={handleSave} className="space-y-4">
        {/* Profile Header Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-4">
            <div className="relative group">
              <div className="h-20 w-20 rounded-full overflow-hidden">
                {(profile.profilePicture || previewImage) && !imageError ? (
                  <Image
                    src={previewImage || profile.profilePicture}
                    alt="Profile"
                    width={80}
                    height={80}
                    className="object-cover"
                    onError={() => {
                      setImageError(true);
                    }}
                    unoptimized
                  />
                ) : (
                  <div className="h-full w-full bg-primary flex items-center justify-center text-white typography-heading-3">
                    <span>
                      {profile.firstName[0]}{profile.lastName[0]}
                    </span>
                  </div>
                )}
                {isEditing && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex flex-col items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity gap-1">
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="text-white typography-caption hover:text-primary-300 flex items-center gap-1 transition-colors"
                      disabled={isUploadingImage || isDeletingImage}
                    >
                      {isUploadingImage ? (
                        <>
                          <Loader2 size={14} className="animate-spin" />
                          <span>Uploading...</span>
                        </>
                      ) : (
                        <>
                          <Camera size={14} />
                          <span>Change</span>
                        </>
                      )}
                    </button>
                    {profile.profilePicture && (
                      <button
                        type="button"
                        onClick={() => setIsDeleteModalOpen(true)}
                        className="text-red-300 typography-caption hover:text-red-400 flex items-center gap-1 transition-colors"
                        disabled={isUploadingImage || isDeletingImage}
                      >
                        {isDeletingImage ? (
                          <>
                            <Loader2 size={14} className="animate-spin" />
                            <span>Deleting...</span>
                          </>
                        ) : (
                          <>
                            <Trash2 size={14} />
                            <span>Delete</span>
                          </>
                        )}
                      </button>
                    )}
                  </div>
                )}
              </div>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={handleImageUpload}
                disabled={!isEditing || isUploadingImage || isDeletingImage}
              />
            </div>
            <div className="flex-1">
              <h2 className="typography-heading-4 font-weight-bold text-gray-900">
                {profile.firstName} {profile.lastName}
              </h2>
                 {(profile.department || profile.designation) ? (
                <p className="typography-body-sm text-gray-600 mt-1">
                  {profile.designation} • {profile.department}
                </p>
              ) : null}
              <p className="typography-caption text-gray-500 mt-1">
                {isUploadingImage ? 'Uploading profile picture...' :
                  isDeletingImage ? 'Deleting profile picture...' :
                    isEditing ? 'Click profile picture to change' :
                      profile.email}
              </p>
              <div className="mt-2">
                <StatusIndicator
                  isLoading={isCheckingActive}
                  isActive={isUserActive}
                  label={isUserActive ? 'Active User' : 'Inactive User'}
                />
              </div>
            </div>

            {/* Action Buttons - moved to header */}
            <div className="flex space-x-2">
              {!isEditing ? (
                <button
                  type="button"
                  onClick={() => setIsEditing(true)}
                  className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm typography-body-sm font-weight-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <FaEdit className="mr-1" size={14} />
                  Edit Profile
                </button>
              ) : (
                <>
                  <button
                    type="button"
                    onClick={() => setIsEditing(false)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm typography-body-sm font-weight-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSaving}
                    className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm typography-body-sm font-weight-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <FaSave className="mr-1" size={14} />
                    {isSaving ? 'Saving...' : 'Save'}
                  </button>
                </>
              )}
            </div>

            {/* Add Delete Confirmation Modal */}
            <DeleteConfirmationModal
              isOpen={isDeleteModalOpen}
              onClose={() => setIsDeleteModalOpen(false)}
              onConfirm={handleDeleteProfilePicture}
              title="Delete Profile Picture"
              message="Are you sure you want to delete your profile picture? This action cannot be undone."
              isLoading={isDeletingImage}
            />
          </div>
        </div>

        {/* Personal Information */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="typography-body-lg font-weight-medium text-gray-900 mb-3">Personal Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <label className="block typography-caption font-weight-medium text-gray-700 mb-1">First Name</label>
              <input
                type="text"
                name="firstName"
                value={profile.firstName}
                onChange={handleInputChange}
                disabled={!isEditing}
                className={`w-full px-3 py-2 rounded-md border typography-body-sm ${isEditing ? 'border-gray-300 bg-white' : 'bg-gray-50 border-gray-200'
                  } focus:ring-primary focus:border-primary`}
              />
            </div>

            <div>
              <label className="block typography-caption font-weight-medium text-gray-700 mb-1">Last Name</label>
              <input
                type="text"
                name="lastName"
                value={profile.lastName}
                onChange={handleInputChange}
                disabled={!isEditing}
                className={`w-full px-3 py-2 rounded-md border typography-body-sm ${isEditing ? 'border-gray-300 bg-white' : 'bg-gray-50 border-gray-200'
                  } focus:ring-primary focus:border-primary`}
              />
            </div>

            <div>
              <label className="block typography-caption font-weight-medium text-gray-700 mb-1">Email</label>
              <input
                type="email"
                name="email"
                value={profile.email}
                disabled
                className="w-full px-3 py-2 rounded-md border border-gray-200 bg-gray-50 typography-body-sm"
              />
            </div>

            <div>
              <label className="block typography-caption font-weight-medium text-gray-700 mb-1">Designation</label>
              <input
                type="text"
                name="designation"
                value={profile.designation}
                onChange={handleInputChange}
                disabled={!isEditing}
                placeholder="e.g. Product Manager"
                className={`w-full px-3 py-2 rounded-md border typography-body-sm ${isEditing ? 'border-gray-300 bg-white' : 'bg-gray-50 border-gray-200'
                  } focus:ring-primary focus:border-primary`}
              />
            </div>

            <div>
              <label className="block typography-caption font-weight-medium text-gray-700 mb-1">Department</label>
              <input
                type="text"
                name="department"
                value={profile.department}
                onChange={handleInputChange}
                disabled={!isEditing}
                placeholder="e.g. Marketing"
                className={`w-full px-3 py-2 rounded-md border typography-body-sm ${isEditing ? 'border-gray-300 bg-white' : 'bg-gray-50 border-gray-200'
                  } focus:ring-primary focus:border-primary`}
              />
            </div>

            {/* Timezone Selector */}
            <div className="relative">
              <label className="block typography-caption font-weight-medium text-gray-700 mb-1">Timezone</label>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => isEditing && setShowTimezoneDropdown(!showTimezoneDropdown)}
                  className={`w-full text-left rounded-md border px-3 py-2 typography-body-sm ${isEditing ? 'border-gray-300 bg-white hover:border-gray-400' : 'bg-gray-50 border-gray-200'
                    } focus:outline-none focus:ring-primary focus:border-primary`}
                  disabled={!isEditing}
                >
                  <div className="flex items-center">
                    <FaClock className="mr-2 text-gray-400" size={14} />
                    <span className="truncate">{profile.timezone || "Select timezone"}</span>
                  </div>
                </button>

                {showTimezoneDropdown && (
                  <div ref={dropdownRef} className="absolute z-10 mt-1 w-full bg-white rounded-md shadow-lg border border-gray-200">
                    <div className="p-2 border-b">
                      <div className="relative flex items-center">
                        <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={12} />
                        <input
                          type="text"
                          value={timezoneSearch}
                          onChange={(e) => setTimezoneSearch(e.target.value)}
                          placeholder="Search timezone..."
                          className="w-full pl-8 pr-3 py-1.5 border rounded-md typography-caption focus:outline-none focus:ring-1 focus:ring-primary"
                        />
                      </div>
                    </div>
                    <ul className="max-h-48 overflow-y-auto py-1 scrollbar-hide">
                      {filteredTimezones.map((tz) => (
                        <li
                          key={tz}
                          onClick={() => {
                            setProfile(prev => ({ ...prev, timezone: tz }));
                            setShowTimezoneDropdown(false);
                          }}
                          className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer typography-caption"
                        >
                          {tz}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>


      </form>
    </div>
  );
};

export default GeneralSettings;
