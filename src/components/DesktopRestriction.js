"use client";

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import useIsMobile from '@/hooks/useIsMobile';

/**
 * Desktop Restriction Component
 * Displays a message when the application is accessed on a non-desktop device
 */
const DesktopRestriction = () => {
  const isMobile = useIsMobile();
  const [isClient, setIsClient] = useState(false);
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    setIsClient(true);
    const checkTouchDevice = () => {
      setIsTouchDevice(
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0
      );
    };

    checkTouchDevice();

    if (isMobile) {
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMobile]);

  if (!isClient) return null;
  const hasDesktopViewParam = isClient && window.location.search.includes('desktop_view=true');
  const displayStyle = isMobile && !hasDesktopViewParam ? { display: 'flex' } : { display: 'none' };
  return (
    <div className="desktop-restriction-container" style={displayStyle}>
      <div className="max-w-md w-full text-center p-8 rounded-xl shadow-xl bg-white animate-fade-in">
        <div className="flex justify-center mb-6">
          <div className="relative w-16 h-16 mb-2">
            <Image
              src="/logo/kavia_light_logo.svg"
              alt="Kavia Logo"
              fill
              className="object-contain"
            />
          </div>
        </div>

        <h2 className="typography-heading-2 font-weight-bold mb-3 text-gray-800">Desktop Only Application</h2>

        <div className="mb-6 p-4 bg-primary-50 rounded-lg border border-primary-200">
          <p className="text-gray-700 typography-body-lg mb-2">
            This application requires a desktop environment to function properly.
          </p>
          <p className="text-gray-600">
            Please use a desktop device with a screen width of at least 1024px for the best experience.
          </p>
        </div>

        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="flex items-center justify-center space-x-2 typography-body-sm text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span>Required: Desktop Computer</span>
          </div>

          <button
            onClick={() => {
              if (isTouchDevice) {
                const url = new URL(window.location.href);
                url.searchParams.set('desktop_view', 'true');
                window.location.href = url.toString();
              } else {
                window.open(window.location.href, '_blank');
              }
            }}
            className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-600 transition-colors duration-300 flex items-center space-x-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            <span>Go to Desktop</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default DesktopRestriction;
