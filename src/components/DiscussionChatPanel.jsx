/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/display-name */
"use client";
import { DiscussionChatContext } from "./Context/DiscussionChatContext";
import { useContext } from "react";
import React from 'react';
import { useState, useEffect, useRef, useCallback, memo } from "react";
import Image from "next/image";
import Cookies from 'js-cookie'
import { useParams, useSearchParams } from "next/navigation";
import { extractTextV1, uploadFile } from "@/utils/fileAPI";
import FileContentModal from "./Modal/FileContentModal";
import AttachmentButton from "./Buttons/AttachmentButton";
import AttachmentWindow from "./File/AttachmentWindow";
import FileAttachment from "./File/FileAttachment";
import { fetchChatHistory } from "@/utils/api";
import { AlertContext } from "./NotificationAlertService/AlertList";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
// 1. Import the dynamic/lazy version of CodeBlock
import dynamic from 'next/dynamic';
import { FixedSizeList as List } from 'react-window';
import ModelSelector from "./ModelSelector/ModelSelector";
import { decryptToken } from "@/utils/auth";
import { getLatestSubscription } from '@/utils/paymentAPI';
import { ArrowUp, Loader2 } from "lucide-react";
import { FaStopCircle } from 'react-icons/fa';
import CreditBadge from "@/components/ui/CreditBadge"
import DownloadLogsButton from "./Buttons/DownloadLogsButton";

const CodeBlock = dynamic(() => import('./CodeBlock'), {
  ssr: false,
  loading: () => <div className="p-2">Loading code formatter...</div>
});

// 2. Optimize the MessageItem component with memo
const MessageItem = memo(({ message, onFileView, name }) => {
  if (message.sender === "User") {
    return (
      <div className="flex flex-row px-4 py-2 sm:px-4">
        <Image
          className="mr-2 flex h-8 w-8 rounded-full"
          src={`https://ui-avatars.com/api/?name=${message.userDetails?.name || name}&background=00000&color=FFFFFF`}
          alt="User Avatar"
          width={32}
          height={32}
        />
        <div className="flex flex-col max-w-3xl items-center">
          <React.Suspense fallback={<div>Loading...</div>}>
            <CodeBlock
              markdownString={message.text}
              message_end={message.message_end}
              timestamp={message.timestamp ? message.timestamp : "Unknown"}
              user="user"
            />
          </React.Suspense>
          {message.file_attachments && message.file_attachments.length > 0 && (
            <div className="mt-2 space-y-2">
              {message.file_attachments.map((attachment, index) => (
                <FileAttachment
                  key={attachment.file_name || attachment.id || `attachment-${index}`}
                  attachment={attachment}
                  onView={() => onFileView(attachment)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="px-3 w-full">
      <div className="flex w-full px-4 py-4 bg-gray-100 sm:px-4 rounded-lg">
        <React.Suspense fallback={<div>Loading...</div>}>
          <CodeBlock
            markdownString={message.text}
            message_end={message.message_end}
            timestamp={message.timestamp ? message.timestamp : "Unknown"}
          />
        </React.Suspense>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Only re-render if the message content changes
  return prevProps.message.text === nextProps.message.text &&
    prevProps.message.message_end === nextProps.message.message_end;
});

// Add displayName for debugging
MessageItem.displayName = 'MessageItem';



const MessagesSection = memo(({ messages, name, endOfMessagesRef, onFileView }) => {
  // For very long lists, consider using virtualization
  if (messages.length > 50) {
    const itemSize = 150; // Approximate height of a message item in pixels

    const Row = ({ index, style }) => (
      <div style={style} className={`mb-2 ${messages[index].streaming ? 'animate-fade-in' : ''}`}>
        <MessageItem
          message={messages[index]}
          onFileView={onFileView}
          name={name}
        />
      </div>
    );

    return (
      <div className="flex-1 overflow-hidden mt-1 typography-body-sm chat-custom-scrollbar leading-6 text-slate-900 sm:typography-body sm:leading-7">
        <List
          height={600} // Adjust based on your container height
          itemCount={messages.length}
          itemSize={itemSize}
          width="100%"
        >
          {Row}
        </List>
        <div ref={endOfMessagesRef} />
      </div>
    );
  }

  // For shorter lists, use the regular rendering
  return (
    <div className="flex-1 overflow-y-auto mt-1 typography-body-sm custom-scrollbar leading-6 text-slate-900 sm:typography-body sm:leading-7">
      {messages.map((message) => (
        <div
          key={message.messageUuid || message.id}
          className={`mb-2 ${message.streaming ? 'animate-fade-in' : ''}`}
        >
          <MessageItem
            message={message}
            onFileView={onFileView}
            name={name}
          />
        </div>
      ))}
      <div ref={endOfMessagesRef} />
    </div>
  );
}, (prevProps, nextProps) => {
  // Only re-render if messages array changes
  if (prevProps.messages.length !== nextProps.messages.length) {
    return false;
  }

  // Check if any message content has changed
  for (let i = 0; i < prevProps.messages.length; i++) {
    if (prevProps.messages[i].text !== nextProps.messages[i].text ||
      prevProps.messages[i].message_end !== nextProps.messages[i].message_end) {
      return false;
    }
  }

  return true;
});

// Add accepted file types constant at the top
const ACCEPTED_FILE_TYPES = ['.pdf', '.xlsx', '.xls', '.txt', '.csv', '.log', '.py', '.js', '.json', '.yml', '.yaml', '.xml'];

export default function DiscussionChatPanel({ maxFiles = 5, attachmentEnabled = true }) {

  const {
    steps,
    loading,
    setLoading,
    messages,
    addMessage,
    handleStepExecution,
    processing,
    setProcessing,
    discussionId,
    setMessages,
    wsConnection,
    wsStatus,
    connectWebSocket,
    availableModels,
    selectedModel,
    setSelectedModel,
    isLoadingModels,
    stopStreaming
  } = useContext(DiscussionChatContext);

  const { showAlert } = useContext(AlertContext);

  const searchParams = useSearchParams();
  const params = useParams();
  const name = Cookies.get('username');
  const textareaRef = useRef(null);
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [showAttachmentWindow, setShowAttachmentWindow] = useState(false);
  const fileInputRef = useRef(null);
  const attachmentButtonRef = useRef(null);

  const [fileStatuses, setFileStatuses] = useState({});
  const [selectedFile, setSelectedFile] = useState(null);

  const [isUploading, setIsUploading] = useState(false);
  const isFirstRender = useRef(true);
  const tenantId = Cookies.get('tenant_id');


  const { currentMessage, setCurrentMessage, currentMessageRef } = useContext(DiscussionChatContext);
  const [scheduleMessageOnWsConnect, setScheduleMessageOnWsConnect] = useState("");
  const endOfMessagesRef = useRef(null);
  const dropZoneRef = useRef(null);
  const [subscriptionData, setSubscriptionData] = useState({
    currentPlan: 'Loading...',
    planCredits: null,
    organizationCost: 0
  });
  const [isRefreshingPlan, setIsRefreshingPlan] = useState(false);

  useEffect(() => {
    if (processing && loading) {
      setLoading(false);
    }
  })

  useEffect(() => {
    setCurrentMessage("");
  }, [searchParams]);

  useEffect(() => {
    if (isFirstRender.current) {
      setLoading(false);
      setProcessing(false);
      isFirstRender.current = false;
    }
  }, [])

  useEffect(() => {
    const loadConversationHistory = async () => {
      const queryDiscussionId = searchParams.get('query_discussion_id');
      if (queryDiscussionId) {
        try {
          const conversationHistory = await fetchChatHistory(queryDiscussionId);

          // Clear existing messages first
          setMessages([]);

          // Add each message from the history
          addMessage({
            text: `Welcome to the Code Query Discussion Panel! Dive into your selected codebase and ask any questions`,
            sender: 'AI'
          });
          // Add all messages except the last one
          conversationHistory.forEach(msg => {
            addMessage({
              id: msg.id,
              text: msg.text,
              sender: msg.sender === 'user' ? 'User' : 'AI',
              timestamp: msg.timestamp ? msg.timestamp : "Unknown",
              message_end: true,
              file_attachments: msg.file_attachments ? msg.file_attachments : null
            });
          });

          // Add the last message separately with message_end: true
          // if (conversationHistory.length > 0) {
          //   const lastMsg = conversationHistory[conversationHistory.length - 1];
          //   addMessage({
          //       id: lastMsg.id,
          //       text: lastMsg.text,
          //       sender: lastMsg.sender === 'user' ? 'User' : 'AI',
          //       message_end: true
          //   });
          // }

        } catch (error) {

        }
      }
    };

    loadConversationHistory();
  }, [discussionId]);
  const fetchCurrentPlan = async () => {

    try {

      const idToken = Cookies.get("idToken");
      const userData = decryptToken(idToken);
      const userId = userData.sub

      if (tenantId ==='b2c' && userId) {
        const subscription = await getLatestSubscription(userId);

        if (subscription && subscription.price_id) {
          const cleanedCost = subscription.current_cost
            ? typeof subscription.current_cost === 'string'
              ? parseFloat(subscription.current_cost.replace('$', '').trim())
              : parseFloat(subscription.current_cost) 
            : 0
          // Now we can directly use the fields from the enhanced endpoint response
          setSubscriptionData({
            currentPlan: subscription.product_name,
            planCredits: subscription.credits || 50000, //50000 is for free
            organizationCost: cleanedCost
          });
        }
        else {
          setSubscriptionData({
            currentPlan: 'Free',
            planCredits: 50000,
            organizationCost: 0
          });
        }
      }
    } catch (error) {

      setSubscriptionData({
        currentPlan: 'Free',
        planCredits: 50000,
        organizationCost: 0
      });
      // showAlert("There was an error loading your subscription data. Defaulting to Free plan.", "error");
    } finally {

    }
  };

  useEffect(() => {

    fetchCurrentPlan()
  }, [searchParams, discussionId])

  const handleRefreshPlan = async () => {
    setIsRefreshingPlan(true);
    await fetchCurrentPlan();
    // Show loading for at least 1 second
    setTimeout(() => {
      setIsRefreshingPlan(false);
    }, 1000);
  };

  const handleModelChange = (modelName) => {
    if (wsConnection?.readyState === WebSocket.OPEN) {
      wsConnection.send(JSON.stringify({
        type: "set_model",
        model_name: modelName,
        user_id: Cookies.get('userId')
      }));
    }

    setSelectedModel(modelName);
  }

  const handleInputChange = (event) => {
    setCurrentMessage(event.target.value);
  };

  const handleKeyPress = (event) => {
    if (loading) {
      return;
    }

    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleFormSubmit(event);
    }
  };

  useEffect(() => {
    if (wsConnection && wsStatus === "connected" && scheduleMessageOnWsConnect) {
      wsConnection.send(scheduleMessageOnWsConnect);
      setScheduleMessageOnWsConnect("");
    }
  }, [wsConnection, wsStatus, scheduleMessageOnWsConnect]);

  const handleFormSubmit = async (event) => {
    const message = textareaRef.current.value;
    setCurrentMessage(message);

    currentMessageRef.current = message

    textareaRef.current.value = '';
    try {
      if (isUploading || loading) {
        return;
      }

      event.preventDefault();

      if (processing) {
        stopStreaming();
        return;
      }

      if (message?.trim() !== "" || attachedFiles.length > 0) {
        setLoading(true);

        const messageTimestamp = Date.now();

        const newMessage = {
          id: messageTimestamp,
          text: message,
          sender: "User",
          file_attachments: attachedFiles
        };
        addMessage(newMessage);
        const checking_timestamp = messages[messages.length - 1];
        sessionStorage.setItem(`message_status_${searchParams.get('sessionId')}`, 'sent');

        if (searchParams.get('sessionId')) {
          if (wsConnection && wsStatus === "connected") {
            wsConnection.send(JSON.stringify({
              type: "user_input",
              task_id: searchParams.get('sessionId'),
              session_id: searchParams.get('sessionId'),
              project_id: params.projectId,
              user_id: Cookies.get('userId'),
              discussion_id: searchParams.get('query_discussion_id'),
              auth_token: Cookies.get('idToken'),
              tenant_id: Cookies.get('tenant_id'),
              input: message,
              file_attachments: attachedFiles
            }));
          } else {
            // Add error message to chat when WebSocket is not connected
            if (searchParams.get('sessionId')) {

              connectWebSocket(searchParams.get('sessionId'));
              setScheduleMessageOnWsConnect(JSON.stringify({
                type: "user_input",
                task_id: searchParams.get('sessionId'),
                session_id: searchParams.get('sessionId'),
                project_id: params.projectId,
                user_id: Cookies.get('userId'),
                discussion_id: searchParams.get('query_discussion_id'),
                auth_token: Cookies.get('idToken'),
                tenant_id: Cookies.get('tenant_id'),
                input: message,
                file_attachments: attachedFiles
              }));
            }
            setLoading(false);
            return;
          }
        } else {
          await handleStepExecution(steps[1], 1, message, "repeat", {}, null, attachedFiles);
        }

        if (textareaRef.current) {
          textareaRef.current.style.height = 'auto';
          textareaRef.current.style.overflowY = 'hidden';
        }
        setAttachedFiles([]);
        setFileStatuses({});
      }
    } catch (error) {

    }
  };

  const handleTextareaResize = (e) => {
    const textarea = e.target;

    // Reset the height
    textarea.style.height = 'auto';

    // Calculate the new height based on the content
    const newHeight = textarea.scrollHeight;

    // Set the textarea height based on the content
    textarea.style.height = `${newHeight}px`;

    // Define the maximum height before scrollbar should appear
    const maxHeight = 150; // Set this to whatever maximum height you want

    // If content height exceeds maxHeight, allow scrolling
    if (newHeight > maxHeight) {
      textarea.style.height = `${maxHeight}px`; // Restrict the height to maxHeight
      textarea.style.overflowY = 'scroll'; // Enable scrolling
    } else {
      textarea.style.overflowY = 'hidden'; // Disable scrolling if content fits
    }
  };
  const handleAttachmentClick = (e) => {
    e.stopPropagation();
    if (attachedFiles.length === 0) {
      // If no files are attached, open file selection window
      fileInputRef.current?.click();
    } else {
      // If files are attached, toggle the attachment window
      setShowAttachmentWindow(prev => !prev);
    }
  };

  const handleAddAttachment = (e) => {
    e.preventDefault(); // Prevent form submission
    fileInputRef.current?.click();
  };

  const handleFileUpload = useCallback(async (files) => {
    // Validate file types
    const invalidFiles = files.filter(file => {
      const extension = '.' + file.name.split('.').pop().toLowerCase();
      return !ACCEPTED_FILE_TYPES.includes(extension);
    });

    if (invalidFiles.length > 0) {
      const invalidFileNames = invalidFiles.map(f => f.name).join(', ');
      alert(`Unsupported file types: ${invalidFileNames}. Supported types: ${ACCEPTED_FILE_TYPES.join(', ')}`);
      return;
    }

    setAttachedFiles(prevAttachedFiles => {
      const updatedFiles = [...prevAttachedFiles];
      const newFileStatuses = { ...fileStatuses };

      if (updatedFiles.length + files.length <= maxFiles) {

        files.forEach(file => {
          const existingFileIndex = updatedFiles.findIndex(f => f.file_name === file.name);
          if (existingFileIndex !== -1) {

            return;
          }

          const newFile = {
            file_name: file.name,
            file_type: file.type,
            file_size: file.size,
            file_kind: file.type.startsWith('image/') ? 'image' : 'document',
            extracted_content: 'Loading...'
          };
          updatedFiles.push(newFile);
          newFileStatuses[file.name] = 'loading';
        });

        setFileStatuses(newFileStatuses);

        files.forEach(async (file) => {
          try {
            const formData = new FormData();
            formData.append('file', file);
            setIsUploading(true);
            let response;
            if (file.type.startsWith('image/')) {
              response = await uploadFile(file, discussionId);
            } else {
              response = await extractTextV1(formData);
            }

            setAttachedFiles(prevFiles => {
              const updatedFiles = [...prevFiles];
              const index = updatedFiles.findIndex(f => f.file_name === file.name);
              if (index !== -1) {
                if (file.type.startsWith('image/')) {
                  updatedFiles[index] = { ...updatedFiles[index], ...response };
                } else {
                  updatedFiles[index].extracted_content = response.text || response.extracted_content;
                }
              }
              return updatedFiles;
            });

            setFileStatuses(prevStatuses => ({
              ...prevStatuses,
              [file.name]: 'loaded'
            }));
          } catch (error) {


            let errorMessage = "An error occurred while processing the file.";
            if (error.message.includes("400: Unsupported file type")) {
              errorMessage = `Unsupported file type: ${file.name}`;
            } else if (error.response && error.response.data && error.response.data.detail) {
              errorMessage = error.response.data.detail;
            }
            alert(errorMessage);

            setAttachedFiles(prevFiles => prevFiles.filter(f => f.file_name !== file.name));
            setFileStatuses(prevStatuses => {
              const newStatuses = { ...prevStatuses };
              delete newStatuses[file.name];
              return newStatuses;
            });
          }
          finally {
            setIsUploading(false)
          }
        });

      } else {
        alert(`You can only attach up to ${maxFiles} files.`);
      }

      return updatedFiles;
    });
  }, [maxFiles, discussionId, fileStatuses]);

  const handleFileChange = async (e) => {
    const files = e.target.files;
    if (files) {
      await handleFileUpload(Array.from(files));
    }
  };
  const handleFileClick = (file) => {
    setSelectedFile(file);
  };

  const handleClearAttachments = () => {
    setAttachedFiles([]);
    setShowAttachmentWindow(false); // Close the attachment window after clearing
  };


 useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        attachmentButtonRef.current &&
        !attachmentButtonRef.current.contains(event.target ) &&
        !document
          .querySelector(".attachment-window")
          ?.contains(event.target)
      ) {
        setShowAttachmentWindow(false);
      }
    };

    // Only add the event listener when the attachment window is open
    if (showAttachmentWindow) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showAttachmentWindow]);

  useEffect(() => {
    const scrollTimeout = setTimeout(() => {
      endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
    return () => clearTimeout(scrollTimeout);
  }, [messages]);

  useEffect(() => {
    const handlePaste = async (e) => {
      const items = e.clipboardData.items;
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          e.preventDefault();
          const file = items[i].getAsFile();
          await handleFileUpload([file]);
        }
      }
    };

    const handleDragOver = (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropZoneRef.current.classList.add('bg-primary-100');
    };

    const handleDragLeave = (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropZoneRef.current.classList.remove('bg-primary-100');
    };

    const handleDrop = async (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropZoneRef.current.classList.remove('bg-primary-100');
      const files = Array.from(e.dataTransfer.files);
      await handleFileUpload(files);
    };

    document.addEventListener('paste', handlePaste);
    dropZoneRef.current.addEventListener('dragover', handleDragOver);
    dropZoneRef.current.addEventListener('dragleave', handleDragLeave);
    dropZoneRef.current.addEventListener('drop', handleDrop);

    return () => {
      document.removeEventListener('paste', handlePaste);
      dropZoneRef.current?.removeEventListener('dragover', handleDragOver);
      dropZoneRef.current?.removeEventListener('dragleave', handleDragLeave);

      dropZoneRef.current?.removeEventListener('drop', handleDrop);
    };
  }, [handleFileUpload]);

  const handleFileView = useCallback((file) => {
    setSelectedFile(file);
  }, []);

  return (
    <div className="chat-panel flex flex-col h-full overflow-hidden bg-white pt-2" ref={dropZoneRef}>


      {/* Add your CSSTransition and dropdown menu here if needed */}

      <div className="flex items-center justify-between px-4 py-1 border-b border-gray-200">
        <div className="flex items-center space-x-2 justify-between w-full">
          <div className="flex items-center space-x-2">
            <h2 className="project-panel-heading">Chat</h2>



            {(searchParams.get('sessionId')) && (
              <div className="flex items-center ml-2">
                <BootstrapTooltip
                  title={`WebSocket: ${wsStatus}`}
                  placement="bottom"
                >
                  <div
                    className={`h-2 w-2 mb-1.5 rounded-full ${wsStatus === "connected"
                      ? "bg-green-500"
                      : wsStatus === "connecting"
                        ? "bg-yellow-500"
                        : "bg-red-500"
                      }`}
                  />
                </BootstrapTooltip>
              </div>
            )}

            {(discussionId || searchParams.get('query_discussion_id')) && (
              <a
                href={`https://us5.datadoghq.com/logs?query=discussion_id%3A${discussionId || searchParams.get('query_discussion_id')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:text-primary-800 underline"
              >
                <p title='view logs'>{discussionId || searchParams.get('query_discussion_id')}</p>
              </a>
            )}

            {subscriptionData && tenantId === 'b2c' ? (
              <CreditBadge
                planCredits={subscriptionData.planCredits}
                organizationCost={subscriptionData.organizationCost}
                isRefreshing={isRefreshingPlan}
                onRefresh={handleRefreshPlan}
              />
            ) : null}

          {(discussionId || searchParams.get('query_discussion_id')) && (
            <DownloadLogsButton 
              discussionId={discussionId || searchParams.get('query_discussion_id')} 
            />
          )}

          </div>

          {/* {searchParams.get('sessionId') && */}
            <ModelSelector
              selectedModel={selectedModel}
              onSelectModel={handleModelChange}
              availableModels={availableModels}
            //   isDisabled={availableModels.length === 0}
              isLoading={isLoadingModels}
            />
          {/* } */}
        </div>
      </div>

      <MessagesSection
        messages={messages}
        endOfMessagesRef={endOfMessagesRef}
        name={name}
        onFileView={handleFileView}
      />

      <div className="border-t border-gray-200 p-2 bg-gray-100">
        <form onSubmit={handleFormSubmit} className="flex items-end">
          <textarea
            id="chat-input"
            ref={textareaRef}
            className={`flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none overflow-auto ${(loading || processing || isUploading || messages[messages.length - 1]?.text === "Thinking...") ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            placeholder={loading || processing || isUploading || messages[messages.length - 1]?.text === "Thinking..." ? 'Please wait...' : 'Enter your prompt'}
            onKeyDown={handleKeyPress}
            onInput={handleTextareaResize}
            disabled={loading || processing || isUploading || messages[messages.length - 1]?.text === "Thinking..."}
            rows={1}
          />
          {attachmentEnabled && (
            <div className="relative attachment-window">
              <AttachmentButton
                ref={attachmentButtonRef}
                onClick={handleAttachmentClick}
                fileCount={attachedFiles.length}
              // acceptedTypes={acceptedFileTypes.join(', ').replace(/\./g, '').toUpperCase()}
              />
              {showAttachmentWindow && attachedFiles.length > 0 && (
                <>
                  <AttachmentWindow
                    files={attachedFiles}
                    onAdd={handleAddAttachment}
                    onClear={handleClearAttachments}
                    onClose={() => setShowAttachmentWindow(false)}
                    maxFiles={maxFiles}
                    fileStatuses={fileStatuses}
                    onFileClick={handleFileClick}
                  />
                </>



              )}
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                multiple={maxFiles > 1}
                accept={ACCEPTED_FILE_TYPES.join(',')}
                className="hidden"
              />
            </div>
          )}
          <div></div>
          <button type="submit" disabled={isUploading || loading}
            className={`ml-2 mb-2 w-6 h-6 rounded-full flex items-center justify-center bg-primary text-white hover:bg-primary-600 ${(loading || isUploading) ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {
              processing ? (
                <FaStopCircle size={20} className="" />
              ) :
                loading || isUploading ? (
                  <Loader2 size={16} className="animate-spin" />
                ) : (
                  <ArrowUp size={16} />
                )
            }
          </button>

        </form>
      </div>
      {selectedFile && (
        <FileContentModal
          file={selectedFile}
          onClose={() => setSelectedFile(null)}
        />
      )}
    </div>
  );
}
