"use client";
// @ts-nocheck
// @ts-ignore
import React, { useEffect, useState } from "react";
import { Github, Gitlab, Search, Trash2 } from "lucide-react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import TableComponent from "@/components/SimpleTable/ScmTable";
import { useRouter } from "next/navigation";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { ScmRepoLoader } from "@/components/UIComponents/Loaders/LoaderGroup";
import DeleteConfirmationModal from "@/components/Modal/DeleteConfirmationModal";

interface SCMConfiguration {
  scm_type: string;
  credentials: {
    auth_type: string;
    organization: string;
    token_expires_at?: string;
  };
  encrypted_scm_id: string;
  webhook_url: string | null;
  webhook_secret: string | null;
  api_url: string | null;
}

interface TableConfiguration extends SCMConfiguration {
  id: string;
  organization: string;
  auth_type: string;
  token_expiry?: string;
  actions?: React.ReactNode;
}

interface SCMConfigurationPageProps {
  providerId: "github" | "gitlab" | "gerrit";
  getOAuthURL: (scmType: string) => Promise<{ data: { url: string } }>;
  getSCMConfigs: () => Promise<{
    data: { configurations: SCMConfiguration[] };
  }>;
  deleteSCMConfig: (id: string) => Promise<void>;
  saveGerritConfig?: (config: {
    username: string;
    password: string;
    hostname: string;
  }) => Promise<void>;
}

interface GerritConfiguration extends SCMConfiguration {
  credentials: {
    auth_type: string;
    organization: string;
    token_expires_at?: string;
    gerrit_host: string;
    gerrit_project: string;
    http_password?: string;
  };
}

export default function SCMConfigurationPage({
  providerId,
  getOAuthURL,
  getSCMConfigs,
  deleteSCMConfig,
  saveGerritConfig,
}: SCMConfigurationPageProps) {
  const [configurations, setConfigurations] = useState<TableConfiguration[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [showConnectModal, setShowConnectModal] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedScmId, setSelectedScmId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [gerritForm, setGerritForm] = useState({
    username: "",
    password: "",
    hostname: "",
  });
  
  // New state for Gerrit connection flow
  const [showInstructions, setShowInstructions] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState("");

  const router = useRouter();
  const rowsPerPage = 10;

  const Icon = providerId === "github" ? Github : providerId === "gitlab" ? Gitlab : Github;
  const providerName = providerId === "github" ? "GitHub" : providerId === "gitlab" ? "GitLab" : "Gerrit";
  const bgColor = providerId === "github" ? "bg-black" : providerId === "gitlab" ? "bg-primary-600" : "bg-green-600";

  // Check if hostname is valid HTTPS URL
  const isValidHttpsUrl = (hostname: string): boolean => {
    try {
      const url = hostname.startsWith('http') ? hostname : `https://${hostname}`;
      const urlObj = new URL(url);
      return urlObj.protocol === 'https:' && urlObj.hostname.length > 0;
    } catch {
      return false;
    }
  };

  const formatDate = (dateString: string | undefined): string => {
    if (!dateString) return "No expiry";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const transformConfigurations = (configs: SCMConfiguration[]): TableConfiguration[] => {
    return configs
      .filter((config) => config.scm_type === providerId)
      .map((config) => ({
        ...config,
        id: config.encrypted_scm_id,
        organization: config.credentials.organization,
        auth_type: config.credentials.auth_type,
        token_expiry: formatDate(config.credentials.token_expires_at),
      }));
  };

  const handleGerritConnect = async () => {
    if (!saveGerritConfig) return;
    
    setIsConnecting(true);
    setConnectionError("");
    
    try {
      await saveGerritConfig(gerritForm);
      setShowConnectModal(false);
      setGerritForm({ username: "", password: "", hostname: "" });
      setShowInstructions(false);
      await fetchConfigurations();
    } catch (error: any) {
      setConnectionError(error.message || "Failed to connect to Gerrit");
    } finally {
      setIsConnecting(false);
    }
  };

  const handleConnect = async () => {
    try {
      const { data } = await getOAuthURL(providerId);
      if (data?.url) {
        window.location.href = data.url;
      }
    } catch (error) {}
  };

  const fetchConfigurations = async () => {
    try {
      const response = await getSCMConfigs();
      const transformedConfigs = transformConfigurations(response.data.configurations);
      setConfigurations(transformedConfigs);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (scmId: string, e: React.MouseEvent) => {
    setSelectedScmId(scmId);
    setDeleteModalOpen(true);
    e.stopPropagation();
  };

  const handleConfirmDelete = async () => {
    if (!selectedScmId) return;
    setIsDeleting(true);
    try {
      await deleteSCMConfig(selectedScmId);
      await fetchConfigurations();
    } catch (error) {
    } finally {
      setIsDeleting(false);
      setDeleteModalOpen(false);
      setSelectedScmId(null);
    }
  };

  // Handle hostname change and show instructions
  const handleHostnameChange = (value: string) => {
    setGerritForm({ ...gerritForm, hostname: value });
    setConnectionError("");
    
    // Show instructions if valid HTTPS URL is entered
    if (isValidHttpsUrl(value)) {
      setShowInstructions(true);
    } else {
      setShowInstructions(false);
    }
  };

  useEffect(() => {
    fetchConfigurations();
  }, []);

  const filteredConfigurations = configurations.filter(
    (config) =>
      config.organization.toLowerCase().includes(searchQuery.toLowerCase()) ||
      config.scm_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      config.auth_type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const tableHeaders = [
    { key: "organization", label: "Organization" },
    { key: "scm_type", label: "SCM Type" },
    { key: "auth_type", label: "Authentication Type" },
    ...(providerId === "gitlab" ? [{ key: "token_expiry", label: "Token Expiry" }] : []),
    { key: "actions", label: "Actions" },
  ];

  const tableData = filteredConfigurations.map((config) => ({
    ...config,
    actions: (
      <button
        onClick={(e) => handleDelete(config.id, e)}
        className="p-2 text-gray-600 hover:text-red-600 transition-colors"
        aria-label="Delete configuration"
      >
        <Trash2 className="w-5 h-5" />
      </button>
    ),
  }));

  if (loading) return <ScmRepoLoader />;

  return (
    <div className="container mx-auto px-4">
      <div className="flex items-center typography-body-sm text-gray-600 space-x-2 mb-6">
        <span
          className="text-gray-800 hover:text-primary hover:underline cursor-pointer transition-colors duration-300"
          onClick={() => router.push("/dashboard/settings/scm")}
        >
          SCM Providers
        </span>
        <span className="text-gray-400">{">"}</span>
        <span className="text-gray-800 font-weight-medium">SCM Configuration</span>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="typography-body-lg font-weight-semibold">{`${providerName} SCM Configurations`}</h1>
        <DynamicButton
          variant="primary"
          icon={Icon}
          text={`Connect to ${providerName}`}
          onClick={() => setShowConnectModal(true)}
        />
      </div>

      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search configurations..."
            className="border border-gray-300 rounded-md p-2 pl-12 w-64"
          />
        </div>
      </div>

      {filteredConfigurations.length === 0 ? (
        <EmptyStateView
          type={searchQuery ? "noSearchResult" : `no${providerName}Config`}
          onClick={searchQuery ? () => setSearchQuery("") : () => {}}
        />
      ) : (
        <TableComponent
          headers={tableHeaders}
          data={tableData}
          onRowClick={(id) =>
            router.push(
              `/dashboard/settings/scm/connection?scmType=${providerId}&scmId=${encodeURIComponent(id)}`
            )
          }
          sortableColumns={{
            organization: true,
            scm_type: true,
            auth_type: true,
            token_expiry: true,
            actions: false,
          }}
          itemsPerPage={rowsPerPage}
          title="Configurations"
        />
      )}

      {showConnectModal && (
        <div className="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg border p-8 text-center max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className={`${bgColor} text-white p-4 rounded-full inline-flex mb-6`}>
              <Icon className="w-8 h-8" />
            </div>
            <h2 className="typography-heading-2 font-weight-semibold mb-2">
              Connect to your {providerName} account
            </h2>

            {providerId === "gerrit" ? (
              <>
                <p className="text-gray-500 mb-6">
                  Enter your Gerrit credentials to connect
                </p>
                
                <div className="space-y-4 mb-6">
                  <input
                    type="text"
                    placeholder="Hostname (e.g., https://gerrit.example.com)"
                    value={gerritForm.hostname}
                    onChange={(e) => handleHostnameChange(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    disabled={isConnecting}
                  />
                  
                  {showInstructions && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left text-sm">
                      <h4 className="font-semibold text-blue-900 mb-2">Get your HTTP credentials:</h4>
                      <ol className="list-decimal list-inside space-y-2 text-blue-800">
                        <li>
                          <a 
                            href={`${gerritForm.hostname}/settings/#HTTPCredentials`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 underline font-medium"
                          >
                            Open Gerrit HTTP Credentials Settings
                          </a>
                        </li>
                        <li>Click <strong>"Generate New Password"</strong> button</li>
                        <li>Copy the generated HTTP password</li>
                        <li>Paste it in the password field below</li>
                      </ol>
                      <p className="text-xs text-blue-700 mt-2 italic">
                        Note: Use the HTTP password, not your login password
                      </p>
                    </div>
                  )}
                  
                  <input
                    type="text"
                    placeholder="Username"
                    value={gerritForm.username}
                    onChange={(e) => setGerritForm({ ...gerritForm, username: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    disabled={isConnecting}
                  />
                  
                  <input
                    type="password"
                    placeholder="HTTP Password (not your login password)"
                    value={gerritForm.password}
                    onChange={(e) => setGerritForm({ ...gerritForm, password: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    disabled={isConnecting}
                  />
                </div>

                {connectionError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                    <p className="text-red-600 text-sm">{connectionError}</p>
                  </div>
                )}

                <div className="flex justify-center space-x-4">
                  <button
                    onClick={() => {
                      setShowConnectModal(false);
                      setShowInstructions(false);
                      setConnectionError("");
                      setGerritForm({ username: "", password: "", hostname: "" });
                    }}
                    className="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-weight-medium hover:bg-gray-300 transition-colors"
                    disabled={isConnecting}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleGerritConnect}
                    disabled={
                      !gerritForm.hostname ||
                      !gerritForm.username ||
                      !gerritForm.password ||
                      isConnecting
                    }
                    className="bg-primary-600 text-white px-6 py-3 rounded-lg font-weight-medium hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    {isConnecting && (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    )}
                    {isConnecting ? "Connecting..." : `Connect with ${providerName}`}
                  </button>
                </div>
              </>
            ) : (
              <>
                <p className="text-gray-500 mb-6">
                  Import repositories and start collaborating with your team
                </p>
                <div className="flex justify-center space-x-4">
                  <button
                    onClick={() => setShowConnectModal(false)}
                    className="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-weight-medium hover:bg-gray-300 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleConnect}
                    className="bg-primary-600 text-white px-6 py-3 rounded-lg font-weight-medium hover:bg-primary-700 transition-colors"
                  >
                    Connect with {providerName}
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete SCM Configuration"
        message={`Are you sure you want to delete this ${providerName} SCM configuration ? This action cannot be undone.`}
        isLoading={isDeleting}
      />
    </div>
  );
}