"use client";
import { usePathname, useSearchParams } from "next/navigation";
import { SidebarItem } from "./SidebarItem";
import { SidebarSection } from "./SidebarSection";
import {
  Building2,
  Layers,
  Users,
  Box,
  DoorOpen,
  ComputerIcon,
  Settings,
  CreditCard,
  UserCircle
} from "lucide-react";
import { useUser } from "@/components/Context/UserContext";
import LogoutProjectModal from "@/components/Modal/LogoutProjectModal";
import { useContext, useState, useEffect } from "react";
import { TopBarContext } from "@/components/Context/TopBarContext";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import LockedTabIndicator from "@/components/UIComponents/LockedTabIndicator";
import { useRouter } from "next/navigation";
import { getStripeBillingPortal, fetchOrganization } from "@/utils/api";


export const Sidebar = () => {
  const { is_super_admin, is_admin, tenant_id, fetchUserData, is_free_user, email }: {
    is_super_admin: boolean;
    is_admin: boolean;
    tenant_id: string | null;
    fetchUserData: () => Promise<void>;
    is_free_user: boolean;
    email: string;
  } = useUser();
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { clearTabsFromLocalStorage } = useContext(TopBarContext);
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const [enablePodManagement, setEnablePodManagement] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const router = useRouter()
  const [isB2C, setIsB2C] = useState(true);
  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData().then(() => setIsDataLoaded(true));
    } else {
      setIsDataLoaded(true);
    }
    if (tenant_id == process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID) {
      setIsB2C(true);
    }
    else {
      setIsB2C(false);
    }
  }, [is_admin, tenant_id]);

  useEffect(() => {
    const fetchData = async () => {
      if (tenant_id) {
        try {
          const orgData = await fetchOrganization(tenant_id);
          const enableSetting = orgData?.settings?.enable_log_download_pod_crud;
          setEnablePodManagement(!!enableSetting);
        } catch (err) {
          console.error("Failed to fetch organization settings", err);
        }
      }
    };

    fetchData();
  }, [is_admin, tenant_id]);


  const isGroupsActive = () => {
    return pathname.startsWith("/dashboard/groups");
  };

  const handleLogout = async () => {
    try {
      // Define and execute cookie deletion
      const deleteAllCookies = () => {
        document.cookie.split(";").forEach((cookie) => {
          const name = cookie.split("=")[0].trim();
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        });
      };
      deleteAllCookies();

      // Preserve tenant_id
      let _tenant_id = localStorage.getItem("tenant_id");

      // Clear tabs
      clearTabsFromLocalStorage();
      localStorage.clear();
      sessionStorage.clear();
      // Reset tenant_id
      localStorage.setItem("tenant_id", _tenant_id || "");

      showAlert("Successfully logged out!", "success");
      setIsLogoutModalOpen(false);

      // Redirect
      if (typeof window !== "undefined") {
        window.location.href = "/";
      }
    } catch (error) {

      showAlert("Logout failed. Please try again.", "danger");
      setIsLogoutModalOpen(false);
    }
  };

  const handleManageDescription = async () => {
  try {
    const response = await getStripeBillingPortal(email);
    if (response?.url) {
      window.open(response.url);
    } else {
      showAlert("Unable to retrieve billing portal link. Please try again later.","error");
    }
  } catch (error:any) {
    console.error("error in getting stripe bill", error);
    const message = error.message.includes("No customer found")
      ? "No Customer found for this email."
      : "Something went wrong while opening the billing portal.";
      
    showAlert(message, "error");
  }
};

  const renderMenuItems = () => {
    // Don't render menu items for super admin
    if (is_super_admin) return null;

    // Admin for non-B2C
    if (!isB2C && is_admin) {
      return (
        <>
          <SidebarItem
            icon={<UserCircle className="w-5 h-5" />}
            label="Profile"
            href="/dashboard/profile"
            active={pathname.startsWith("/dashboard/profile")}
          />
          <SidebarItem
            icon={<Users className="w-5 h-5" />}
            label="All Users"
            href="/dashboard/allusers"
            active={pathname === "/dashboard/allusers"}
          />
          <SidebarItem
            icon={<Layers className="w-5 h-5" />}
            label="Groups"
            href="/dashboard/groups"
            active={isGroupsActive()}
          />
          <SidebarItem
            icon={<Settings className="w-5 h-5" />}
            label="Settings"
            href="/dashboard/settings"
            active={pathname === "/dashboard/settings"}
          />
          {enablePodManagement && (
            <SidebarItem
              icon={<Box className="w-5 h-5" />}
              label="Pod Dashboard"
              href="/dashboard/organizations/podManagement"
              active={pathname === "/dashboard/organizations/podManagement"}
            />)}
          <SidebarItem
            icon={<ComputerIcon className="w-5 h-5" />}
            label="Application"
            href="/home"
          />
        </>
      );
    }

    // Member for non-B2C
    if (!isB2C && !is_admin) {
      return (
        <>
          <SidebarItem
            icon={<UserCircle className="w-5 h-5" />}
            label="Profile"
            href="/dashboard/profile"
            active={pathname.startsWith("/dashboard/profile")}
          />
          <SidebarItem
            icon={<ComputerIcon className="w-5 h-5" />}
            label="Application"
            href="/home"
          />
        </>
      );
    }

    // B2C users (both free and premium)
    return (
      <>
        <SidebarItem
          icon={<UserCircle className="w-5 h-5" />}
          label="Profile"
          href="/dashboard/profile"
          active={pathname.startsWith("/dashboard/profile")}
        />
        <SidebarItem
          icon={<ComputerIcon className="w-5 h-5" />}
          label="Application"
          href="/home"
        />
        {(is_free_user ) ? (
          <div className="flex items-center gap-3 px-4 py-2 typography-body-sm text-gray-400 opacity-70">
            <span className="w-5 h-5 flex items-center justify-center">
              <Settings className="w-5 h-5" />
            </span>
            <span className="flex-1 truncate">Settings</span>
            <div title="Upgrade to premium"><LockedTabIndicator /></div>
          </div>
        ) : (
          <>
            <SidebarItem
              icon={<Settings className="w-5 h-5" />}
              label="Settings"
              href="/dashboard/settings"
              active={pathname === "/dashboard/settings"}
            />
            <SidebarItem
              icon={<CreditCard className="w-5 h-5" />}
              label="Manage Subscriptions"
              href="#"
              title="Click to view the billing details"
              onClick={handleManageDescription}
            />
          </>

        )}
      </>
    );
  };

  useEffect(() => {
    if (isDataLoaded && pathname === '/dashboard') {
      router.push('/dashboard/profile');
    }
  }, [isDataLoaded, pathname]);

  return (
    <aside className={`relative transition-all duration-300 border-r bg-white h-full flex flex-col`}>
      {isDataLoaded && (
        <>
          {isLogoutModalOpen && (
            <LogoutProjectModal
              setIsLogoutModalOpen={setIsLogoutModalOpen}
              handleLogout={handleLogout}
            />
          )}
          <nav className="flex-1 flex flex-col">
            <div className="flex-1">
              {/* Super admin section remains unchanged */}
              <div className="mt-2">
                {is_super_admin && (
                  <SidebarSection
                    icon={<Building2 className="w-5 h-5" />}
                    title="Organizations"
                    items={[
                      {
                        label: "All Organizations",
                        href: "/dashboard/organizations",
                        active: pathname === "/dashboard/organizations",
                      },
                      {
                        label: "Add Organization",
                        href: "/dashboard/organizations/add",
                        active: pathname === "/dashboard/organizations/add",
                      },
                      {
                        label: "Announcements",
                        href: "/dashboard/organizations/announcements",
                        active: pathname === "/dashboard/organizations/announcements",
                      },
                      {
                        label: "API Dashboard",
                        href: "/dashboard/organizations/tracker",
                        active: pathname === "/dashboard/organizations/tracker",
                      },
                      {
                        label: "Session Dashboard",
                        href: "/dashboard/organizations/session_tracker",
                        active: pathname === "/dashboard/organizations/session_tracker",
                      },
                      {
                        label: "Deployment Dashboard",
                        href: "/dashboard/organizations/deployment_tracker",
                        active: pathname === "/dashboard/organizations/deployment_tracker",
                      },
                      {
                        label: "Pod Dashboard",
                        href: "/dashboard/organizations/podManagement",
                        active: pathname === "/dashboard/organizations/podManagement",
                      },
                        {
                        label: "Referral Activity",
                        href: "/dashboard/organizations/referredUsers",
                        active: pathname === "/dashboard/organizations/referredUsers",
                      }
                    ]}

                  />
                )}
              </div>
              <div className="-mt-1">
                {renderMenuItems()}
              </div>
            </div>
            {/* Logout button */}
            <div className="mb-4">
              <SidebarItem
                icon={<DoorOpen className="w-5 h-5" />}
                label="Logout"
                href="#"
                onClick={() => setIsLogoutModalOpen(true)}
              />
            </div>
          </nav>
        </>
      )}
    </aside>
  );
};