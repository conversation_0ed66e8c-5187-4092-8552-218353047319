"use client";

import React, { useCallback, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import LoginSignupContainer from "@/components/LoginSignupContainer";
import { validateReferralCode } from "@/utils/api";
import { CheckCircle, AlertCircle, Loader2, ChevronRight } from "lucide-react";
import { useSignupFlow } from "@/components/Context/SignupFlowContext";

const ReferralPage = () => {
  const router = useRouter();
  const params = useSearchParams();

  const { referralCode: ctxReferralCode, setReferralCode: setCtxReferralCode, setShouldAutoContinue, setHasAutoSubmitted } = useSignupFlow();
  const [referralCode, setReferralCode] = useState(ctxReferralCode || "");
  const [referralValidation, setReferralValidation] = useState({
    isValidating: false,
    isValid: false,
    referrerInfo: null,
    error: null,
  });

  useEffect(() => {
    // Prefill from query or previously saved value
    const initial = params.get("ref") || ctxReferralCode || "";
    if (initial) {
      setReferralCode(initial.toUpperCase());
    }
  }, [params, ctxReferralCode]);

  const validateReferral = useCallback(async (code) => {
    if (!code || code.length < 6) {
      setReferralValidation({
        isValidating: false,
        isValid: false,
        referrerInfo: null,
        error: null,
      });
      return;
    }

    setReferralValidation((prev) => ({ ...prev, isValidating: true, error: null }));
    try {
      const result = await validateReferralCode(code);
      if (result && result.valid) {
        setReferralValidation({
          isValidating: false,
          isValid: true,
          referrerInfo: {
            name: result.referrer_name,
            organization: result.referrer_organization,
          },
          error: null,
        });
      } else {
        setReferralValidation({
          isValidating: false,
          isValid: false,
          referrerInfo: null,
          error: result?.message || "Invalid referral code",
        });
      }
    } catch (error) {
      setReferralValidation({
        isValidating: false,
        isValid: false,
        referrerInfo: null,
        error: "Failed to validate referral code",
      });
    }
  }, []);

  useEffect(() => {
    const id = setTimeout(() => {
      if (referralCode) {
        validateReferral(referralCode.toUpperCase());
      } else {
        setReferralValidation({
          isValidating: false,
          isValid: false,
          referrerInfo: null,
          error: null,
        });
      }
    }, 350);
    return () => clearTimeout(id);
  }, [referralCode, validateReferral]);

  const handleChange = (e) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, "");
    setReferralCode(value);
  };

  const continueWithCode = (codeToUse) => {
    setCtxReferralCode((codeToUse || "").toUpperCase());
    setHasAutoSubmitted(false);
    setShouldAutoContinue(true);
    router.push("/users/sign_up");
  };

  return (
    <LoginSignupContainer>
      <div className="bg-custom-bg-primary rounded-lg shadow-xl p-6 max-w-sm w-full z-10 text-center sm:max-w-md md:max-w-lg lg:max-w-md">
        <h1 className="project-panel-heading mb-2">Do you have a referral code?</h1>
        <p className="text-font mb-4">Optional — you can skip by leaving it blank</p>

        <div className="relative">
          <input
            id="referralCode"
            type="text"
            value={referralCode}
            onChange={handleChange}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault();
                continueWithCode(referralCode);
              }
            }}
            autoFocus
            placeholder="Enter referral code..."
            maxLength={12}
            className={`h-12 w-full rounded-xl border bg-white pl-4 pr-12 text-gray-900 placeholder-gray-500 outline-none transition-colors focus:ring-2 focus:ring-black/10 ${
              referralValidation.isValidating
                ? "border-gray-300"
                : referralValidation.isValid
                ? "border-green-500/60"
                : referralValidation.error
                ? "border-red-500/60"
                : "border-gray-300"
            }`}
            aria-invalid={!!referralValidation.error}
            aria-describedby="referral-help"
          />
          <button
            onClick={() => continueWithCode(referralCode)}
            disabled={referralValidation.isValidating}
            className="absolute right-1.5 top-1.5 bottom-1.5 inline-flex w-9 items-center justify-center rounded-lg bg-primary text-primary-foreground transition-colors hover:bg-primary/90 disabled:opacity-50"
            aria-label="Submit referral code"
          >
            {referralValidation.isValidating ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </button>
        </div>

        {referralValidation.isValid && referralValidation.referrerInfo && (
          <div className="mt-2 flex items-center justify-center text-xs text-green-600">
            <CheckCircle className="mr-1 h-3 w-3" />
            Referred by <span className="ml-1 font-medium">{referralValidation.referrerInfo.name}</span>
          </div>
        )}
        {referralValidation.error && (
          <div className="mt-2 flex items-center justify-center text-xs text-red-600">
            <AlertCircle className="mr-1 h-3 w-3" />
            {referralValidation.error}
          </div>
        )}

        
      </div>
    </LoginSignupContainer>
  );
};

export default ReferralPage;


