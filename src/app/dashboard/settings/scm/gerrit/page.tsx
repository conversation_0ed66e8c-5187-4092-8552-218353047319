// @ts-nocheck
// @ts-ignore
"use client"
import React from 'react';
import { getOAuthLoginURL, getSCMConfiguration, deleteSCMConfiguration, saveGerritConfig } from '@/utils/scmAPI';
import SCMConfigurationPage from '@/components/SCM/SCMConfigurationPage';

const Page: React.FC = () => {
  return (
    <SCMConfigurationPage
      providerId="gerrit"
      getOAuthURL={getOAuthLoginURL}
      getSCMConfigs={getSCMConfiguration}
      deleteSCMConfig={deleteSCMConfiguration}
      saveGerritConfig={saveGerritConfig}
    />
  );
};

export default Page;