import React, { useState, useEffect, useContext, useRef } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { useCodeGeneration } from '@/components/Context/CodeGenerationContext';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { ConfirmationModal } from '@/components/CodeGenrationPanel/LoadingComponents';
import { controlTask } from '@/utils/batchAPI';
import TableComponent from "@/components/SimpleTable/ArchitectureTable";
import { formatDateTime } from "@/utils/datetime";
import { useWebSocket } from '@/components/Context/WebsocketContext';
import Cookies from "js-cookie";

const CodeMaintenanceHandler = ({ 
  projectId, 
  onComplete,
  selectedRepos
}) => {
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [activeSessions, setActiveSessions] = useState([]);
  const [showActiveSessionsModal, setShowActiveSessionsModal] = useState(false);
  const [isStoppingTasks, setIsStoppingTasks] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const router = useRouter();
  const pathname = usePathname();
  const { setIsVisible, setCurrentIframeUrl } = useCodeGeneration();
  const [logginfo, setLogInfo] = useState('');
  const [controller, setController] = useState(null);
  const hasGeneratedCode = useRef(false);
  const searchParams = useSearchParams()
  const [sessionsTableData, setSessionsTableData] = useState([]);
  const [plannedTaskId, setPlannedTaskId] = useState(null);
  const { connectToSession, connections, getConnection, disconnectFromSession } = useWebSocket();

  const handleNavigate = (iframe, taskId) => {
    setIsCompleted(true);
    setTimeout(() => {
      setCurrentIframeUrl(iframe);
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("task_id", taskId);
      router.push(`${pathname}?${newSearchParams.toString()}`);
      setIsVisible(true);
      onComplete();
    }, 3000);
  };

  useEffect(() => {
    if (plannedTaskId) {
      const connection = getConnection(plannedTaskId);
      if (connection && connection.readyState === WebSocket.OPEN) {

        connection.send(JSON.stringify({
          type: 'client',
          task_id: plannedTaskId
        }));

        connection.onmessage = (event) => {
          // if messaage set the planned task id to null and open the modal by setting the search params
          setPlannedTaskId(null);
          const newSearchParams = new URLSearchParams(searchParams);
          newSearchParams.set("task_id", plannedTaskId);
          router.push(`${pathname}?${newSearchParams.toString()}`);
          setIsVisible(true);
        };

      }
    }
    
  }, [plannedTaskId]);

  useEffect(() => {
    const startMaintenance = async () => {
    const abortController = new AbortController();
      setIsGeneratingCode(true);
      setIsCompleted(false);
      try {
        if (!hasGeneratedCode.current) {
          hasGeneratedCode.current = true;
          setController(abortController);
          let url = `${process.env.NEXT_PUBLIC_API_URL}/batch/start_code_maintenance/${projectId}/`;
          setIsGeneratingCode(true);
         const eventSource = fetchEventSource(
            url,
            {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${Cookies.get('idToken')}`,
                'Content-Type': 'application/json',
                'X-Tenant-Id': Cookies.get('tenant_id'),
              },
              body: JSON.stringify({
                request_timestamp: new Date().toISOString(),
                selectedrepos: selectedRepos.all_repositories 
                  ? { all_repositories: true }
                  : { all_repositories: false, repositories: selectedRepos.repositories },
              }),
              signal: abortController.signal,
              openWhenHidden: true,
              onopen: (response) => {
                // showAlert("Starting code maintenance... ", "info");
                
                if(response.status !== 200){
                  showAlert("Something went wrong!", "error");
                  abortController.abort();
                  confirmClose();
                }
                return Promise.resolve();
              },  
              onmessage: (event) => {
                try {
                  const data = JSON.parse(event.data);
                  if (data.task_id) {
                    const newSearchParams = new URLSearchParams(searchParams);
                    newSearchParams.set("task_id", data.task_id);
                    router.push(`${pathname}?${newSearchParams.toString()}`);
                  }
                  if (data.planned_job_id) {
                    setPlannedTaskId(data.planned_job_id);
                    connectToSession(data.planned_job_id);
                  }
                  if (data.message) {
                    setLogInfo(data.message);
                  }
                  if (data.end === true) {
                    if (data.task_id) {
                      handleNavigate(data.iframe, data.task_id);
                    } else if (data.error && data.error.includes("Maximum number of active code maintenance sessions reached")) {
                      try {
                        // Parse the active_sessions string into JSON
                        const sessions = JSON.parse(data.active_sessions);
                        setActiveSessions(sessions);
                        setShowActiveSessionsModal(true);
                        abortController.abort();
                        setIsGeneratingCode(false);
                      } catch (parseError) {
                        
                        // showAlert(data.error, "error");
                        onComplete();
                      }
                    } else {
                      // showAlert(data.message || data.error ||"Unable to start task", "error");
                      onComplete();
                    }
                    abortController.abort();
                  }
                } catch (error) {
                  abortController.abort();
                  showAlert("Error processing response", "error");
                }
              },
              onerror: (error) => {
                if (plannedTaskId) {
                  disconnectFromSession(plannedTaskId);
                }
                // showAlert("Error in code maintenance" + error, "error");
                abortController.abort();
                setIsGeneratingCode(false);
                setShowActiveSessionsModal(false);
                setIsCompleted(false);
                setShowConfirmModal(false);
                setPlannedTaskId(null);
                onComplete();
                return null;
              },
              onclose: () => {
                if (plannedTaskId) {
                  disconnectFromSession(plannedTaskId);
                }
                showAlert("Connection closed",isCompleted ? "success" : "error");
                setIsGeneratingCode(false);
                setController(null);
                setShowActiveSessionsModal(false);
                setShowConfirmModal(false);
                setPlannedTaskId(null);
                
                if (!isCompleted) {
                  setIsVisible(false);
                  setCurrentIframeUrl(null);
                }
              }
            }
          );
        }
      } catch (error) {
        if (plannedTaskId) {
          disconnectFromSession(plannedTaskId);
        }

        abortController.abort()
        showAlert("Failed to start code maintenance", "error");
        onComplete();
      }
      finally{

        setIsGeneratingCode(false);
        setController(null);
        setShowActiveSessionsModal(false);
        setShowConfirmModal(false);
        setPlannedTaskId(null);
      }
    };

    startMaintenance();

    return () => {
      if (controller) {
        controller.abort();
      }
    };
  }, [projectId, selectedRepos]); 

  useEffect(() => {
    if (activeSessions.length > 0) {
      // Transform session data for the table component
      const formattedSessions = activeSessions.map(session => ({
        _id: session._id || '',
        job_id: session.job_id || '',
        fullId: session.job_id || '',
        session_name: session.session_name || `Session ${session.job_id.substring(0, 8)}`,
        status: session.status || 'UNKNOWN',
        start_time: formatDateTime(session.start_time, true),
        ip: session.ip || ''
      }));
      setSessionsTableData(formattedSessions);
    }
  }, [activeSessions]);

  const handleClose = () => setShowConfirmModal(true);
  
  const confirmClose = () => {
    if (controller) controller.abort();
    setShowConfirmModal(false);
    setIsGeneratingCode(false);
    setIsVisible(false);
    setCurrentIframeUrl(null);
    setPlannedTaskId(null);
    onComplete();
  };

  const cancelClose = () => setShowConfirmModal(false);

  const handleStopTask = async (taskId) => {
    try {
      await controlTask(taskId, "stop", projectId);
      setActiveSessions(prevSessions => prevSessions.filter(session => session.job_id !== taskId));
      showAlert(`Successfully stopped task ${taskId}`, "success");
      setPlannedTaskId(null);
      if (activeSessions.length <= 1) {
        setShowActiveSessionsModal(false);
        onComplete();
      }
    } catch (error) {
      showAlert(`Failed to stop task: ${error.message}`, "error");
    }
  };

  const handleStopAllTasks = async () => {
    try {
      setIsStoppingTasks(true);
      await Promise.all(
        activeSessions.map(session => 
          controlTask(session.job_id, "stop", projectId)
            .catch(error => {})
        )
      );
      showAlert("Successfully stopped all tasks", "success");
      setShowActiveSessionsModal(false);
      onComplete();
    } catch (error) {
      // showAlert(`Failed to stop all tasks: ${error.message}`, "error");
    } finally {
      setIsStoppingTasks(false);
    }
  };

  // Define headers for the active sessions table
  const activeSessionsHeaders = [
    { 
      key: 'session_name', 
      label: 'Session Name',
      render: (value, row) => (
        <span className="font-weight-medium">{value}</span>
      )
    },
    { 
      key: 'job_id', 
      label: 'Job ID',
      render: (value) => (
        <span className="typography-body-sm text-gray-600">{value.substring(0, 8)}...</span>
      )
    },
    { 
      key: 'status', 
      label: 'Status',
      render: (value) => (
        <span className={`px-2 py-1 typography-caption rounded-full ${
          value === 'RUNNING' ? 'bg-green-100 text-green-800' : 
          value === 'SUBMITTED' ? 'bg-primary-100 text-primary-800' : 
          'bg-gray-100 text-gray-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'start_time',
      label: 'Start Time'
    },
    {
      key: 'action',
      label: 'Action',
      render: (_, row) => (
        <button 
          className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded typography-body-sm"
          onClick={(e) => {
            e.stopPropagation();
            handleStopTask(row.job_id);
          }}
        >
          Stop
        </button>
      )
    }
  ];

  // Handler for sorting
  const handleSort = (column, direction) => {
    const sortedData = [...sessionsTableData].sort((a, b) => {
      if (column === 'start_time') {
        const dateA = new Date(a.start_time);
        const dateB = new Date(b.start_time);
        return direction === 'asc' ? dateA - dateB : dateB - dateA;
      }
      return 0;
    });
    setSessionsTableData(sortedData);
  };

  return (
    <>
      {/* {isGeneratingCode && (
        <FullScreenLoader 
          logginfo={logginfo}
          onClose={handleClose}
          isCompleted={isCompleted}
        />
      )} */}
      {showConfirmModal && (
        <ConfirmationModal 
          onConfirm={confirmClose} 
          onCancel={cancelClose} 
        />
      )}
      {showActiveSessionsModal && (
        <div className="fixed inset-0 bg-semantic-gray-900/50 dark:bg-background/80 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-card rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <h2 className="typography-heading-4 text-black font-weight-semibold mb-4">Active Maintenance Sessions</h2>
              <p className="mb-4 text-red-500">
                Maximum number of active code maintenance sessions reached (limit: 3).
                Please stop some sessions and try again.
              </p>
              
              <div className="overflow-auto max-h-[50vh]">
                <TableComponent
                  data={sessionsTableData}
                  headers={activeSessionsHeaders}
                  sortableColumns={{ start_time: true, status: true }}
                  onSort={handleSort}
                  defaultSort={{ column: 'start_time', direction: 'desc' }}
                  emptyMessage="No active sessions found"
                />
              </div>
              
              <div className="mt-6 flex justify-end gap-4">
                <button 
                  className="bg-gray-300 text-black hover:bg-gray-400 dark:bg-gray-700 dark:hover:bg-gray-600 px-4 py-2 rounded"
                  onClick={() => {
                    setShowActiveSessionsModal(false);
                    onComplete();
                  }}
                >
                  Close
                </button>
                <button 
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded flex items-center"
                  onClick={handleStopAllTasks}
                  disabled={isStoppingTasks}
                >
                  {isStoppingTasks ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : "Stop All Sessions"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CodeMaintenanceHandler;