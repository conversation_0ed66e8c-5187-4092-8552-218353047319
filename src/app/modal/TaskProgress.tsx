import React, { useState, useEffect } from 'react';
import { fetchEventSource } from "@microsoft/fetch-event-source";
import Drawer from '@/components/Drawer';
import { Clock, Check, Loader2 } from 'lucide-react';
import { useSearchParams } from "next/navigation";
import { useCodeGeneration } from '@/components/Context/CodeGenerationContext';
import { getHeadersRaw } from "@/utils/api";
import Cookies from "js-cookie";
import { BootstrapTooltip } from '@/components/UIComponents/ToolTip/Tooltip-material-ui';

interface TaskProgressProps {
    isOpen: boolean;
    onClose: () => void;
}

interface Step {
    title: string;
    description: string;
    status: string;
}

// Empty state component for TaskProgress
const TaskProgressEmptyState = () => {
  return (
    <div className="flex flex-col items-center justify-center h-full py-12 px-6 text-center">
      <div className="bg-gray-50 rounded-full p-6 mb-4">
        <Clock className="w-12 h-12 text-gray-400" />
      </div>
      <h3 className="font-weight-semibold typography-body-lg text-gray-700 mb-2">
        No tasks in progress
      </h3>
      <p className="typography-body-sm text-gray-500 max-w-xs">
        Tasks will appear here when your code generation process begins. You'll be able to track each step in real-time.
      </p>
      <div className="mt-6 w-full max-w-xs">
        <div className="h-2 bg-gray-100 rounded-full w-full mb-3"></div>
        <div className="h-2 bg-gray-100 rounded-full w-3/4 mb-3"></div>
        <div className="h-2 bg-gray-100 rounded-full w-1/2 mb-3"></div>
      </div>
    </div>
  );
};

const TaskProgress: React.FC<TaskProgressProps> = ({
    isOpen,
    onClose,
}) => {
    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <Check className="w-[20px] h-[20px] text-green-500" />;
            case 'in-progress':
                return <Loader2 className="w-[20px] h-[20px] text-primary animate-spin" />;
            case 'pending':
            default:
                return <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.5 4.25a1 1 0 1 1 0-2 1 1 0 1 1 0 2ZM12.5 17.75a1 1 0 1 1 0-2 1 1 0 1 1 0 2ZM16 7.25a1 1 0 1 1 0-2 1 1 0 1 1 0 2ZM16 14.75a1 1 0 1 1 0-2 1 1 0 1 1 0 2ZM17 11a1 1 0 1 1 0-2 1 1 0 1 1 0 2ZM10 18c-1.1 0-2.14-.21-3.11-.63a8 8 0 0 1-2.54-1.72 8 8 0 0 1-1.72-2.54A7.93 7.93 0 0 1 2 9.99c0-1.1.21-2.14.63-3.11a8 8 0 0 1 1.72-2.54A8 8 0 0 1 6.89 2.63 7.93 7.93 0 0 1 10 2v1.5a6.5 6.5 0 0 0-4.6 1.9A6.5 6.5 0 0 0 3.5 10a6.5 6.5 0 0 0 1.9 4.6A6.5 6.5 0 0 0 10 16.5V18Zm0-6.5a1.5 1.5 0 0 1-1.06-.44A1.5 1.5 0 0 1 8.5 10v-.2L7 8.06 8.06 7l1.54 1.54A1.5 1.5 0 0 1 11.5 10c0 .42-.15.78-.44 1.06a1.5 1.5 0 0 1-1.06.44Z" fill="#374151"/>
                </svg>;
        }
    };

    const { steps, setSteps } = useCodeGeneration();
    const [selectedStep, setSelectedStep] = useState<number | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const searchParams = useSearchParams();
    const currentTaskDetailsId = searchParams.get("task_id");
    const idToken = Cookies.get("idToken");

    useEffect(() => {
        if (!currentTaskDetailsId) return;

        const abortController = new AbortController();
        setIsLoading(true);

        const eventSource = fetchEventSource(
            `${process.env.NEXT_PUBLIC_API_URL}/batch/callback/steps/${currentTaskDetailsId}`,
            {
                headers: getHeadersRaw(),
                openWhenHidden: true,
                signal: abortController.signal,
                onmessage: (event) => {
                    setIsLoading(false);
                    try {
                        const data = JSON.parse(event.data);
                        if (data.steps) {
                            setSteps(data.steps);
                            if (data.steps.length > 0) {
                                setSelectedStep(data.steps.length - 1);
                            }
                        }
                    } catch (error) {

                    }
                },
                onerror: (error) => {

                    setIsLoading(false);
                    abortController.abort();
                },
                onclose: () => {
                    setIsLoading(false);
                },
            }
        );

        return () => {
            abortController.abort();
        };
    }, [currentTaskDetailsId, idToken]);


    return (
        <Drawer
            isOpen={isOpen}
            onClose={onClose}
            placement="right"
            width={400}
            overlayClassName="z-[1001] left-0"
            title={
                <div className="flex items-center space-x-3 ml-2">
                    <svg width="24" height="24" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.5 4.25a1 1 0 1 1 0-2 1 1 0 1 1 0 2ZM12.5 17.75a1 1 0 1 1 0-2 1 1 0 1 1 0 2ZM16 7.25a1 1 0 1 1 0-2 1 1 0 1 1 0 2ZM16 14.75a1 1 0 1 1 0-2 1 1 0 1 1 0 2ZM17 11a1 1 0 1 1 0-2 1 1 0 1 1 0 2ZM10 18c-1.1 0-2.14-.21-3.11-.63a8 8 0 0 1-2.54-1.72 8 8 0 0 1-1.72-2.54A7.93 7.93 0 0 1 2 9.99c0-1.1.21-2.14.63-3.11a8 8 0 0 1 1.72-2.54A8 8 0 0 1 6.89 2.63 7.93 7.93 0 0 1 10 2v1.5a6.5 6.5 0 0 0-4.6 1.9A6.5 6.5 0 0 0 3.5 10a6.5 6.5 0 0 0 1.9 4.6A6.5 6.5 0 0 0 10 16.5V18Zm0-6.5a1.5 1.5 0 0 1-1.06-.44A1.5 1.5 0 0 1 8.5 10v-.2L7 8.06 8.06 7l1.54 1.54A1.5 1.5 0 0 1 11.5 10c0 .42-.15.78-.44 1.06a1.5 1.5 0 0 1-1.06.44Z" fill="#374151" />
                    </svg>
                    <span className="font-weight-semibold text-[20px] leading-[150%] tracking-normal align-middle">Task Progress</span>
                </div>
            }
        >
            <div className="flex flex-col h-full overflow-hidden">
                <div className="flex-1 overflow-y-auto py-[16px] px-[20px] pt-0 ml-2">
                    {isLoading && steps.length === 0 && (
                        <div className="flex flex-col items-center justify-center h-full py-12 px-6 text-center">
                            <div className="bg-gray-50 rounded-full p-6 mb-4">
                                <svg className="animate-spin h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                            <h3 className="font-weight-semibold typography-body-lg text-gray-700 mb-2">
                                Loading tasks...
                            </h3>
                            <p className="typography-body-sm text-gray-500 max-w-xs">
                                Please wait while we fetch your task progress information.
                            </p>
                        </div>
                    )}

                    {steps && steps.length > 0 && (
                        steps.map((task: Step, index: number) => (
                            <div
                                key={`task-${index}-${task.title}`}
                                className="py-2 border-b border-gray-200 flex items-start space-x-4"
                            >
                                <BootstrapTooltip title={task.status}>
                                <div className='min-w-[20px] mt-0.5'>
                                    {getStatusIcon(task.status)}
                                </div>
                                </BootstrapTooltip>
                                <div>
                                    <h3 className="font-weight-normal text-[13px] leading-[150%] tracking-[0%] text-gray-600">{task.title}</h3>
                                </div>
                            </div>
                        ))
                    )}

                    {steps.length === 0 && !isLoading && <TaskProgressEmptyState />}
                </div>
            </div>
        </Drawer>
    );
};

export default TaskProgress;