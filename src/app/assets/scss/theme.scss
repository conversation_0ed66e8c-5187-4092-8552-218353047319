/* Main Orange Theme - Matching website design */
.theme-custom-orange {
  --background: 0 0% 100%;
  --foreground: 0 0% 8%;

  --muted: 0 0% 97%;
  --muted-foreground: 0 0% 45%;

  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 8%;

  --card: 0 0% 100%;
  --card-foreground: 0 0% 8%;

  --border: 0 0% 92%;
  --input: 0 0% 96%;

  /* Orange theme - based on main brand color rgb(242 106 27) */
  --primary-50: 22 100% 97%;
  --primary-100: 22 95% 92%;
  --primary-200: 22 90% 85%;
  --primary-300: 22 85% 75%;
  --primary-400: 22 80% 65%;
  --primary-500: 22 89% 53%;
  --primary-600: 22 85% 48%;
  --primary-700: 22 80% 43%;
  --primary-800: 22 75% 38%;
  --primary-900: 22 70% 33%;
  --primary-950: 22 65% 28%;
  --primary: 22 89% 53%;
  --primary-foreground: 0 0% 100%;

  --secondary: 0 0% 96%;
  --secondary-foreground: 0 0% 15%;

  --accent: 0 0% 96%;
  --accent-foreground: 0 0% 15%;

  /* Status colors */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 85.7% 97.3;
  --success: 142.1 70.6% 45.3%;
  --success-foreground: 138.5 76.5% 96.7%;
  --warning: 24.6 95% 53.1%;
  --warning-foreground: 33.3 100% 96.5%;
  --info: 188.7 94.5% 42.7%;
  --info-foreground: 183.2 100% 96.3%;

  --ring: 16 75% 62%;
}

/* Dark Brown Theme with Orange Accents - Matching kavia.ai */
.dark .theme-custom-orange {
  --background: 25 15% 8%;
  --foreground: 30 8% 95%;

  --muted: 25 12% 12%;
  --muted-foreground: 30 6% 75%;

  --popover: 25 15% 10%;
  --popover-foreground: 30 8% 95%;

  --card: 25 12% 10%;
  --card-foreground: 30 8% 95%;

  --border: 25 20% 20%;
  --input: 25 12% 12%;

  /* Dark theme - based on main brand color rgb(242 106 27) with adjustments */
  --primary-50: 22 100% 97%;
  --primary-100: 22 95% 92%;
  --primary-200: 22 90% 85%;
  --primary-300: 22 85% 75%;
  --primary-400: 22 80% 65%;
  --primary-500: 22 89% 58%;
  --primary-600: 22 85% 53%;
  --primary-700: 22 80% 48%;
  --primary-800: 22 75% 43%;
  --primary-900: 22 70% 38%;
  --primary-950: 22 65% 33%;
  --primary: 22 89% 58%;
  --primary-foreground: 0 0% 100%;

  --secondary: 25 18% 18%;
  --secondary-foreground: 30 8% 90%;

  --accent: 25 12% 12%;
  --accent-foreground: 30 8% 90%;

  /* Status colors for dark theme */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 85.7% 97.3;
  --success: 142.1 70.6% 45.3%;
  --success-foreground: 138.5 76.5% 96.7%;
  --warning: 24.6 95% 53.1%;
  --warning-foreground: 33.3 100% 96.5%;
  --info: 188.7 94.5% 42.7%;
  --info-foreground: 183.2 100% 96.3%;

  --ring: 16 95% 68%;

  /* Ultra vibrant warm accent colors for dark theme */
  --warm-accent-1: 35 60% 20%;
  --warm-accent-2: 40 55% 26%;
  --warm-accent-3: 45 50% 32%;

  /* Ultra vibrant colors for UI elements */
  --vibrant-orange: 16 95% 68%;
  --vibrant-amber: 35 95% 65%;
  --vibrant-yellow: 45 95% 60%;
}
