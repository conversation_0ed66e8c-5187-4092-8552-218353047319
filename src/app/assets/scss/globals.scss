@tailwind base;
@tailwind components;
@tailwind utilities;
// root css
@layer base {
  :root {
    /* Light theme - matching website clean design */
    --background: 0 0% 100%;
    --foreground: 0 0% 8%;
    --muted: 0 0% 97%;
    --muted-foreground: 0 0% 45%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 8%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 8%;
    --border: 0 0% 92%;
    --input: 0 0% 96%;

    /* Orange theme - lighter, more subtle brand orange */
    --primary-50: 24 100% 98%;
    --primary-100: 24 95% 95%;
    --primary-200: 22 90% 90%;
    --primary-300: 20 85% 84%;
    --primary-400: 18 80% 76%;
    --primary-500: 16 75% 68%;
    --primary-600: 14 70% 60%;
    --primary-700: 12 65% 50%;
    --primary-800: 10 60% 40%;
    --primary-900: 8 55% 30%;
    --primary-950: 6 50% 22%;
    --primary: 16 75% 62%;
    --primary-foreground: 0 0% 100%;

    /* Secondary colors - clean and minimal */
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 15%;
    --accent: 25 12% 12%;
    --accent-foreground: 30 8% 90%;

    /* Status colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 85.7% 97.3;
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 138.5 76.5% 96.7%;
    --warning: 24.6 95% 53.1%;
    --warning-foreground: 33.3 100% 96.5%;
    --info: 188.7 94.5% 42.7%;
    --info-foreground: 183.2 100% 96.3%;
    --ring: 16 75% 62%;

    /* Essential semantic colors - lighter, website-aligned */
    --semantic-gray-50: 0 0% 98%;
    --semantic-gray-100: 0 0% 96%;
    --semantic-gray-200: 0 0% 92%;
    --semantic-gray-300: 0 0% 85%;
    --semantic-gray-400: 0 0% 65%;
    --semantic-gray-500: 0 0% 45%;
    --semantic-gray-600: 0 0% 30%;
    --semantic-gray-700: 0 0% 20%;
    --semantic-gray-800: 0 0% 15%;
    --semantic-gray-900: 0 0% 8%;

    /* Green semantic colors */
    --semantic-green-50: 142 76% 96%;
    --semantic-green-100: 142 72% 91%;
    --semantic-green-200: 142 69% 81%;
    --semantic-green-300: 142 69% 70%;
    --semantic-green-400: 142 69% 58%;
    --semantic-green-500: 142 71% 45%;
    --semantic-green-600: 142 72% 36%;
    --semantic-green-700: 142 72% 29%;
    --semantic-green-800: 142 69% 24%;
    --semantic-green-900: 142 61% 20%;

    /* Purple semantic colors */
    --semantic-purple-50: 271 91% 98%;
    --semantic-purple-100: 271 91% 95%;
    --semantic-purple-200: 271 86% 91%;
    --semantic-purple-300: 271 84% 84%;
    --semantic-purple-400: 271 83% 74%;
    --semantic-purple-500: 271 82% 65%;
    --semantic-purple-600: 271 81% 56%;
    --semantic-purple-700: 271 76% 48%;
    --semantic-purple-800: 271 69% 40%;
    --semantic-purple-900: 271 61% 33%;

    /* Yellow semantic colors */
    --semantic-yellow-50: 45 100% 96%;
    --semantic-yellow-100: 45 96% 89%;
    --semantic-yellow-200: 45 97% 77%;
    --semantic-yellow-300: 45 94% 65%;
    --semantic-yellow-400: 45 90% 54%;
    --semantic-yellow-500: 45 93% 47%;
    --semantic-yellow-600: 45 87% 40%;
    --semantic-yellow-700: 45 83% 34%;
    --semantic-yellow-800: 45 78% 29%;
    --semantic-yellow-900: 45 69% 24%;

    /* Red semantic colors */
    --semantic-red-50: 0 86% 97%;
    --semantic-red-100: 0 93% 94%;
    --semantic-red-200: 0 96% 89%;
    --semantic-red-300: 0 94% 82%;
    --semantic-red-400: 0 91% 71%;
    --semantic-red-500: 0 84% 60%;
    --semantic-red-600: 0 72% 51%;
    --semantic-red-700: 0 74% 42%;
    --semantic-red-800: 0 70% 35%;
    --semantic-red-900: 0 63% 31%;

    /* Terminal and code colors */
    --terminal-green: 142 71% 45%;--terminal-cyan: 188 95% 43%;
    --terminal-purple: 271 81% 56%;
    --terminal-yellow: 45 93% 47%;
    --terminal-red: 0 84% 60%;

    /* Graph and visualization colors */
    --graph-node-project: var(--primary-500);
    --graph-node-requirement: 43 74% 66%;
    --graph-node-architecture: 54 77% 75%;
    --graph-node-epic: var(--primary-300);
    --graph-node-task: 271 81% 56%;
    --graph-node-file: 54 77% 75%;
    --graph-node-class: 142 71% 45%;

    --radius: 0.5rem;
  }
  .dark {
    /* Dark Brown Theme with Orange Accents - Matching kavia.ai */
    --background: 25 15% 8%;
    --foreground: 30 8% 95%;
    --muted: 25 12% 12%;
    --muted-foreground: 30 6% 75%;
    --popover: 25 15% 10%;
    --popover-foreground: 30 8% 95%;
    --card: 25 12% 10%;
    --card-foreground: 30 8% 95%;
    --border: 25 20% 20%;
    --input: 25 12% 12%;
    /* Ultra vibrant orange colors for dark theme */
    --primary-50: 24 100% 98%;
    --primary-100: 24 100% 94%;
    --primary-200: 22 100% 89%;
    --primary-300: 20 95% 82%;
    --primary-400: 18 90% 75%;
    --primary-500: 16 95% 68%;
    --primary-600: 14 90% 60%;
    --primary-700: 12 85% 50%;
    --primary-800: 10 80% 40%;
    --primary-900: 8 75% 30%;
    --primary-950: 6 70% 22%;
    --primary: 16 95% 68%;
    --primary-foreground: 0 0% 100%;
    --secondary: 25 18% 18%;
    --secondary-foreground: 30 8% 90%;
    --accent: 20 6% 10%;
    --accent-foreground: 0 0% 96%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 85.7% 97.3;
    --ring: 16 95% 68%;
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 138.5 76.5% 96.7%;
    --info: 188.7 94.5% 42.7%;
    --info-foreground: 183.2 100% 96.3%;
    --warning: 24.6 95% 53.1%;
    --warning-foreground: 33.3 100% 96.5%;

    /* Subtle dark theme semantic colors - minimal orange tint */
    --semantic-gray-50: 20 6% 10%;
    --semantic-gray-100: 20 5% 14%;
    --semantic-gray-200: 20 4% 20%;
    --semantic-gray-300: 20 3% 28%;
    --semantic-gray-400: 0 0% 50%;
    --semantic-gray-500: 0 0% 70%;
    --semantic-gray-600: 0 0% 80%;
    --semantic-gray-700: 0 0% 88%;
    --semantic-gray-800: 0 0% 94%;
    --semantic-gray-900: 0 0% 98%;

    /* Green semantic colors - dark theme */
    --semantic-green-50: 142 61% 20%;
    --semantic-green-100: 142 69% 24%;
    --semantic-green-200: 142 72% 29%;
    --semantic-green-300: 142 72% 36%;
    --semantic-green-400: 142 71% 45%;
    --semantic-green-500: 142 69% 58%;
    --semantic-green-600: 142 69% 70%;
    --semantic-green-700: 142 69% 81%;
    --semantic-green-800: 142 72% 91%;
    --semantic-green-900: 142 76% 96%;

    /* Purple semantic colors - dark theme */
    --semantic-purple-50: 271 61% 33%;
    --semantic-purple-100: 271 69% 40%;
    --semantic-purple-200: 271 76% 48%;
    --semantic-purple-300: 271 81% 56%;
    --semantic-purple-400: 271 82% 65%;
    --semantic-purple-500: 271 83% 74%;
    --semantic-purple-600: 271 84% 84%;
    --semantic-purple-700: 271 86% 91%;
    --semantic-purple-800: 271 91% 95%;
    --semantic-purple-900: 271 91% 98%;

    /* Yellow semantic colors - dark theme */
    --semantic-yellow-50: 45 69% 24%;
    --semantic-yellow-100: 45 78% 29%;
    --semantic-yellow-200: 45 83% 34%;
    --semantic-yellow-300: 45 87% 40%;
    --semantic-yellow-400: 45 93% 47%;
    --semantic-yellow-500: 45 90% 54%;
    --semantic-yellow-600: 45 94% 65%;
    --semantic-yellow-700: 45 97% 77%;
    --semantic-yellow-800: 45 96% 89%;
    --semantic-yellow-900: 45 100% 96%;

    /* Red semantic colors - dark theme */
    --semantic-red-50: 0 63% 31%;
    --semantic-red-100: 0 70% 35%;
    --semantic-red-200: 0 74% 42%;
    --semantic-red-300: 0 72% 51%;
    --semantic-red-400: 0 84% 60%;
    --semantic-red-500: 0 91% 71%;
    --semantic-red-600: 0 94% 82%;
    --semantic-red-700: 0 96% 89%;
    --semantic-red-800: 0 93% 94%;
    --semantic-red-900: 0 86% 97%;

    /* Terminal and code colors for dark theme */
    --terminal-green: 142 71% 45%;--terminal-cyan: 188 95% 43%;
    --terminal-purple: 271 81% 56%;
    --terminal-yellow: 45 93% 47%;
    --terminal-red: 0 84% 60%;

    /* Graph and visualization colors for dark theme */
    --graph-node-project: var(--primary-500);
    --graph-node-requirement: 43 74% 66%;
    --graph-node-architecture: 54 77% 75%;
    --graph-node-epic: var(--primary-300);
    --graph-node-task: 271 81% 56%;
    --graph-node-file: 54 77% 75%;
    --graph-node-class: 142 71% 45%;

    /* Ultra vibrant warm accent colors for dark theme */
    --warm-accent-1: 35 60% 20%;
    --warm-accent-2: 40 55% 26%;
    --warm-accent-3: 45 50% 32%;

    /* Ultra vibrant colors for UI elements */
    --vibrant-orange: 16 95% 68%;
    --vibrant-amber: 35 95% 65%;
    --vibrant-yellow: 45 95% 60%;

  }
  * {
    @apply border-custom-border;
  }
  html {
    @apply scroll-smooth;
  }
  body {
    @apply bg-custom-bg-primary text-foreground text-sm;
  }
  /* Theme transition styles */
  .theme-transition {
    transition-property: background-color, color, border-color, fill, stroke;
    transition-duration: 0.5s;
    transition-timing-function: ease-in-out;
  }
  .theme-transition * {
    transition-property: background-color, color, border-color, fill, stroke, opacity, box-shadow;
    transition-duration: 0.5s;
    transition-timing-function: ease-in-out;
  }
  @keyframes slideDown {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }
  @keyframes slideUp {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes progress-bar-stripes {
    0% {
      background-position: 1rem 0;
    }
    to {
      background-position: 0 0;
    }
  }
  .CollapsibleContent[data-state="open"] {
    animation: slideDown 300ms ease-out;
  }
  .CollapsibleContent[data-state="closed"] {
    animation: slideUp 300ms ease-out;
  }
}