// hooks/useKaviaWebsiteData.js
import { useState, useEffect } from 'react';
import { getKaviaWebsiteData, clearKaviaWebsiteDataFromHash } from '../utils/kaviaWebsiteData';

export const useKaviaWebsiteData = () => {
  const [data, setData] = useState(null);
  const [hasData, setHasData] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadData = () => {
    setLoading(true);
    const result = getKaviaWebsiteData();
    
    if (result.success) {
      setData(result.data);
      setHasData(result.hasData);
      setError(null);
    } else {
      setError(result.error);
      setData(null);
      setHasData(false);
    }
    
    setLoading(false);
  };

  const clearData = () => {
    const result = clearKaviaWebsiteDataFromHash();
    if (result.success) {
      setData(null);
      setHasData(false);
      setError(null);
    }
    return result;
  };

  useEffect(() => {
    loadData();
  }, []);

  return {
    data,
    hasData,
    loading,
    error,
    reload: loadData,
    clear: clearData
  };
};
