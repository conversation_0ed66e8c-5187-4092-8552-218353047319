// lib/utils.js
export function formatCurrency(amount) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 4
  }).format(amount);
}

export function formatDate(dateString) {
  return new Date(dateString).toLocaleString();
}

export function formatRelativeTime(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now - date;
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInMinutes < 60) {
    return `${diffInMinutes} minutes ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hours ago`;
  } else {
    return `${diffInDays} days ago`;
  }
}

export function getStatusColor(status) {
  // Normalize status to lowercase for consistent matching
  const normalizedStatus = status?.toLowerCase() || '';
  
  // Map backend statuses to display colors
  if (normalizedStatus === 'success' || normalizedStatus === 'completed') {
    return 'bg-semantic-green-100 text-semantic-green-800';
  }
  
  if (normalizedStatus === 'processing' || normalizedStatus === 'in_progress' || normalizedStatus === 'deploying') {
    return 'bg-semantic-yellow-100 text-semantic-yellow-800';
  }
  
  // Handle various failure statuses
  if (normalizedStatus.includes('failed') || normalizedStatus.includes('error') || normalizedStatus === 'fail') {
    return 'bg-semantic-red-100 text-semantic-red-800';
  }
  
  // Default fallback
  return 'bg-semantic-gray-100 text-semantic-gray-800';
}

export function getDeploymentStatusColor(status) {
  // Normalize status to lowercase for consistent matching
  const normalizedStatus = status?.toLowerCase() || '';
  
  // Map backend statuses to display colors
  if (normalizedStatus === 'success' || normalizedStatus === 'completed') {
    return 'bg-semantic-green-100 text-semantic-green-800';
  }
  
  if (normalizedStatus === 'processing' || normalizedStatus === 'in_progress' || normalizedStatus === 'deploying') {
    return 'bg-semantic-yellow-100 text-semantic-yellow-800';
  }
  
  // Handle various failure statuses
  if (normalizedStatus.includes('failed') || normalizedStatus.includes('error') || normalizedStatus === 'fail') {
    return 'bg-semantic-red-100 text-semantic-red-800';
  }
  
  // Default fallback
  return 'bg-semantic-gray-100 text-semantic-gray-800';
}

export function getDeploymentStatusIcon(status) {
  // Normalize status to lowercase for consistent matching
  const normalizedStatus = status?.toLowerCase() || '';
  
  // Map backend statuses to display icons
  if (normalizedStatus === 'success' || normalizedStatus === 'completed') {
    return '✅';
  }
  
  if (normalizedStatus === 'processing' || normalizedStatus === 'in_progress' || normalizedStatus === 'deploying') {
    return '⏳';
  }
  
  // Handle various failure statuses
  if (normalizedStatus.includes('failed') || normalizedStatus.includes('error') || normalizedStatus === 'fail') {
    return '❌';
  }
  
  // Default fallback
  return '❓';
}

export function truncateId(id, length = 8) {
  return id.length > length ? `${id.substring(0, length)}...` : id;
}

export function cn(...classes) {
  return classes.filter(Boolean).join(' ');
}