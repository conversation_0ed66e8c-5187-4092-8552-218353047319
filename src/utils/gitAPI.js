// GRAPH.js

import { getHeaders } from '@/utils/api';
import { getCookie } from "@/utils/auth";
const backend_base_url = process.env.NEXT_PUBLIC_API_URL;

// src/utils/gitApi.js

// Add constant at the top of the file
const _SHOW_NAME = "oauth/github";

export async function gitHubConnect() {
  const url = `${backend_base_url}/${_SHOW_NAME}`;
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {

    throw error;
  }
}

export async function gitHubCallback(code,userId) {
  const url = `${backend_base_url}/${_SHOW_NAME}/callback?code=${encodeURIComponent(code)}&userId=${encodeURIComponent(userId)}&tenantId=${encodeURIComponent(await getCookie('tenant_id'))}`;

  try {
      // Send GET request with code as query parameter
      const response = await fetch(url, {
          method: 'GET',  
          headers :await getHeaders()
      });

      if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data; // Return the response data on success
  } catch (error) {
      
  }
}


export async function fetchRepository() {
  const url = `${backend_base_url}/${_SHOW_NAME}/repositories`;

  try {
    // Send the access token as a query parameter
    const response = await fetch(`${url}`, {
      method: "GET",  
      headers: await getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data; // Return the fetched repository data
  } catch (error) {
    
    throw error; // Propagate the error
  }
}


export async function fetchRepositoryDetails(scm_id) {
  const url = `${backend_base_url}/${_SHOW_NAME}/repositories`;

  try {
    // Send the access token as a query parameter
    const response = await fetch(`${url}?scm_id=${encodeURIComponent(scm_id)}`, {
      method: "GET",  
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data; // Return the fetched repository data
  } catch (error) {
    
    throw error; // Propagate the error
  }
}


export async function getRepository(userId) {
  const url = `${backend_base_url}/${_SHOW_NAME}/repositories`;

  try {
    // Construct the URL with the userId as a query parameter
    const requestUrl = `${url}?userId=${encodeURIComponent(userId)}`;

    // Send the request with the userId as a query parameter
    const response = await fetch(requestUrl, {
      method: "GET",
      headers: await getHeaders(), // Assuming this function returns the headers
    });

    if (!response.ok) {
      // If response is not ok (status code not in the 2xx range), handle the error
      const errorData = await response.json();  // Extract error details from the response body
      throw new Error(errorData.detail || `HTTP error! Status: ${response.status}`);  // If no detail field, fallback to status code
    }

    const data = await response.json();
      // Log the successful response (repositories data)
    
    return data;  // Return the fetched repository data
  } catch (error) {
    // Log and throw the error to be handled by the caller
    
    throw error;  // Re-throw the error so it can be handled by the calling code
  }
}

export async function createKnowledge(projectId, buildIds, upstream = false) {
  const url = `${backend_base_url}/v1/build-knowledge-graph?project_id=${projectId}&upstream=${upstream}`;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        build_ids: buildIds
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;

  } catch (error) {
    
    throw {
      success: false,
      message: error.message || 'Failed to build knowledge graph'
    };
  }
}



export async function gitHubDisconnect() {
    const url = `${backend_base_url}/${_SHOW_NAME}/logout`;
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: await getHeaders(),
      });
  
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
  
      return await response.json();
    } catch (error) {
      
      throw error;
    }
  }


  export async function getkginfo(projId, associate = false, getAll = true) {
    // Base URL with required projId
    let url = `${backend_base_url}/v1/kg-info/${projId}`;
    
    // Add query parameters if they are provided
    const params = new URLSearchParams();
    if (true) params.append('get_all', getAll);
    
    // Append parameters to URL if any exist
    const queryString = params.toString();
    if (queryString) {
        url += `?${queryString}`;
    }
    
    try {
        const response = await fetch(url, {
            method: "GET",
            headers: await getHeaders(), 
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        
        
        return data;
    } catch (error) {
        
        throw error;
    }
  }

  export async function updateSelectedBranch(project_id, build_id, branch_name) {
    let url = `${backend_base_url}/v1/set-selected-branch`

    try{
      const response = await fetch(url, {
        method: 'POST', 
        headers: await getHeaders(),
        body: JSON.stringify({
          project_id: project_id,
          build_id: build_id,
          branch_name: branch_name
        }) 
      });

      if(response.ok){
        return true;
      }
      else {
        return false;
      }
    }
    catch(error){
      
      return false;
    }
  }
  

  export async function import_codebase(payload) {
    try {
      
      const response = await fetch(`${backend_base_url}/v1/import-codebase` , {
        method: 'POST',
        headers: await getHeaders(),
        body: JSON.stringify(payload),  // Convert the payload to a JSON string
      });
            
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    
      const responseData = await response.json(); // Parse the JSON response
        // Log the response data
      return responseData;  // Return the response data for further use
    } catch (error) {
      
      throw error;
    }
  }


  export async function clone_and_build(payload) {
    try {
      
      const response = await fetch(`${backend_base_url}/v1/clone-nd-build` , {
        method: 'POST',
        headers: await getHeaders(),
        body: JSON.stringify(payload),  // Convert the payload to a JSON string
      });
            
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    
      const responseData = await response.json(); // Parse the JSON response
        // Log the response data
      return responseData;  // Return the response data for further use
    } catch (error) {
      
      throw error;
    }
  }

  export const buildKnowledgeGraph = async (projectId, build_ids) => {
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/build-knowledge-graph?project_id=${projectId}`, {
        method: 'POST',
        headers: await getHeaders(),
        body: JSON.stringify(build_ids)
      });
  
      if (!response.ok) {
        throw new Error('Failed to build knowledge graph');
      }
  
      return await response.json();
    } catch (error) {
      
      throw error;
    }
  };

  export async function checkGitConnectionStatus(userId) {
    let url = `${backend_base_url}/${_SHOW_NAME}/git_connected_status`;
    if (userId){
      url = `${url}?user_id=${encodeURIComponent(userId)}`
    }
    try {
      const response = await fetch(url, {
        headers: await getHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      
      throw error;
    }
  }

export async function repoSynchronization(project_id, build_id, current_repo_status=2) {
  let url = `${backend_base_url}/v1/do-sync-the-repo?project_id=${project_id}&build_id=${build_id}&current_repo_status=${current_repo_status}`
  try{
    const response = await fetch(url,{
      method:'POST',
      headers:await getHeaders()
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  }catch (error) {
    
    throw error;
  }
}

export async function getRepoDetails(owner, repoName){
  const url = `${backend_base_url}/oauth/github/repo-details?owner=${owner}&repo_name=${repoName}`

  try{
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    })

    if(!response.ok){
      throw new Error("Failed to get repository details")
    }

    return await response.json();
  }
  catch (error) {
    console.error(error);
    return {}
  }
}

/**
 * Fetches repository branches using the new API structure
 * @param {string} owner - Repository owner (required)
 * @param {string} repo_name - Repository name (required)
 * @param {string} user_id - User ID (required)
 * @param {string|null} org - Organization (optional)
 * @param {string|null} scm_id - SCM ID (optional)
 * @param {boolean} is_encrypted - Whether the repository is encrypted (default: true)
 * @param {boolean} limit - Limit flag (default: false)
 * @returns {Promise<Array>} Array of branch objects with name, protected, and commit info
 */
export async function getRepoBranches(owner, repo_name, user_id, org = null, scm_id = null, is_encrypted = true, limit = false){
  const url = `${backend_base_url}/api/oauth/github/repo-branches`;

  try{
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        repo_name: repo_name,
        scm_id: scm_id,
        is_encrypted: is_encrypted,
        user_id: user_id,
        owner: owner,
        org: org,
        limit: limit
      })
    })

    if (!response.ok){
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Failed to find branches: ${response.status}`);
    }

    const data = await response.json();

    // Handle the response format
    // Expected format: { branches: [{ name: string, protected: boolean, commit: { sha: string, url: string } }] }
    if (data && Array.isArray(data.branches)) {
      return data.branches;
    } else if (Array.isArray(data)) {
      // Fallback for direct array response
      return data;
    } else {
      console.warn('Unexpected response format:', data);
      return [];
    }
  }
  catch (error){
    console.error('Error fetching repository branches:', error);
    return [];
  }
}

export const getBranchDetails = async (branchName, owner, repo, user_id=null) => {
  const url = `${backend_base_url}/oauth/github/get-branch-details`;
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: await getHeaders(),
        body: JSON.stringify({
          owner: owner,
          repo_name: repo,
          branch_name: branchName,
          user_id: user_id
        })
      });
      
      if(!response.ok){
        throw new Error("Failed to fetch branch details")
      }

      return await response.json();
    }
    catch (error) {
      return {};
    }
  }

export async function discardAndExit(projectId) {
  const url = `${backend_base_url}/discard-and-exit/${projectId}`;
  
  try {
    fetch(url, {
      method: 'POST',
      headers: await getHeaders()
    });
  } catch (error) {
    // Silently ignore errors as requested
  }
}
