// utils/documentationAPI.js
"use client";

import { getHeaders, getHeadersRaw } from "./api";

const base_url = process.env.NEXT_PUBLIC_API_URL;
const SHOW_NAME = 'documentation';

const appendInterfaceId = (url, interfaceId) => {
  if (interfaceId) {
    return url.includes('?') 
      ? `${url}&interface_id=${interfaceId}`
      : `${url}?interface_id=${interfaceId}`;
  }
  return url;
};

export const createDocumentationRoot = async (projectId, documentType ,interfaceId = null) => {
  try {
    let url = `${base_url}/${SHOW_NAME}/create_documentation`;
    url = appendInterfaceId(url, interfaceId);
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        project_id: projectId,
        documentation_type: documentType,
        interface_id: interfaceId
      })
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create documentation root');
    }
    return await response.json();
  } catch (error) {
    
    throw error;
  }

}

export const deleteSection = async (projectId, sectionId, documentType, interfaceId = null) => {
  try {
    let url = `${base_url}/${SHOW_NAME}/sections/${projectId}/${sectionId}?documentation_type=${documentType}`;
    url = appendInterfaceId(url, interfaceId);

    const response = await fetch(url, {
      method: 'DELETE',
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete section');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const createSection = async (projectId, sectionName, documentationType = null, interfaceId = null) => {
  try {
    let api_url = `${base_url}/${SHOW_NAME}/create_section`;
    
    // Add documentation_type as a query parameter if provided
    if (documentationType) {
      api_url = `${api_url}?documentation_type=${documentationType}`;
    }
    
    api_url = appendInterfaceId(api_url, interfaceId);

    const response = await fetch(api_url, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({
        project_id: projectId,
        section_name: sectionName,
        interface_id: interfaceId
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || "Failed to create section");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const reorderSections = async (projectId, sectionOrders, documentationType = null, interfaceId = null) => {
  try {
    let api_url = `${base_url}/${SHOW_NAME}/reorder-sections`;
    
    // Add documentation_type as a query parameter if provided
    if (documentationType) {
      api_url = `${api_url}?documentation_type=${documentationType}`;
    }
    
    api_url = appendInterfaceId(api_url, interfaceId);

    const data = {
      "project_id": Number(projectId),
      "section_orders": sectionOrders,
      "interface_id": interfaceId
    };

    const response = await fetch(api_url, {
      method: "PUT",
      headers: await getHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || "Failed to reorder sections");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const downloadDocumentationPDF = async (projectId, documentationType = null, interfaceId = null) => {
  try {
    let api_url = `${base_url}/${SHOW_NAME}/download-pdf/${projectId}`;
    if (documentationType) {
      api_url = `${api_url}?documentation_type=${documentationType}`;
    }
    
    api_url = appendInterfaceId(api_url, interfaceId);

    const response = await fetch(api_url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to generate PDF");
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `documentation_${projectId}${interfaceId ? `_interface_${interfaceId}` : ''}.pdf`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    return true;
  } catch (error) {
    
    throw error;
  }
};

export const syncDocumentationToS3 = async (projectId, documentationType = null, interfaceId = null) => {
  try {
    let api_url = `${base_url}/${SHOW_NAME}/sync-to-s3/${projectId}`;
    
    if (documentationType) {
      api_url = `${api_url}?documentation_type=${documentationType}`;
    }
    
    api_url = appendInterfaceId(api_url, interfaceId);

    const response = await fetch(api_url, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to sync documentation to S3");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const getDocumentation = async (projectId, documentationType = null, interfaceId = null) => {
  try {
    let url = `${base_url}/${SHOW_NAME}/?project_id=${projectId}`;

    if (documentationType) {
      url = `${url}&documentation_type=${documentationType}`;
    }
    
    url = appendInterfaceId(url, interfaceId);

    const response = await fetch(url, {
      method: "GET", 
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch documentation");
    }

    return await response.json();
  } catch (error) {
    
    throw error; 
  }
};

// List documentation types
export const getDocumentationTypes = async () => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/types`, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch documentation types");
    }
    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// List versions for a specific doc type
export const getDocumentVersions = async (projectId, docType, interfaceId = null) => {
  try {
    let url = `${base_url}/${SHOW_NAME}/versions/${projectId}/${docType}`;
    url = appendInterfaceId(url, interfaceId);

    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch document versions");
    }
    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// Download a specific document
export const downloadDocument = async (projectId, docType, version, fileName, folder_path, interfaceId = null) => {
  try {
    let url = `${base_url}/${SHOW_NAME}/download/${projectId}/${docType}/${version}/${folder_path}/${fileName}`;
    url = appendInterfaceId(url, interfaceId);

    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to download document");
    }
    
    // Return the blob for frontend handling
    const blob = await response.blob();
    return blob;
  } catch (error) {
    
    throw error;
  }
};

export const fetchSavedDocuments = async (projectId, docType, version = "", folderId = "") => {
  try{
    const url = `${base_url}/${SHOW_NAME}/fetch-saved-docs`;
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeadersRaw(),
      body: JSON.stringify({
        project_id: projectId,
        doc_type: docType,
        version: version,
        folder_id: folderId
      })
    });

    if(response.ok){
      return await response.json();
    }
    else {
      return {"message": "Failed to fetch files"}
    }
  }
  catch (error) {
    
    throw error;
  }
}

// Upload a new document
export const uploadDocument = async (projectId, docType, version, file, description = null, folderId = null,interfaceId = null) => {

  try {
    const formData = new FormData();
    formData.append("project_id", String(projectId));
    formData.append("doc_type", docType);
    formData.append("version", version);
    formData.append("file", file);
    formData.append("folder_id", folderId);
    
    if (description) {
      formData.append("description", description);
    }

    if (interfaceId) {
      formData.append("interface_id", String(interfaceId));
    }
    const headers = getHeadersRaw();
    delete headers["Content-Type"];
    const response = await fetch(`${base_url}/${SHOW_NAME}/upload`, {
      method: "POST",
      headers: headers,
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to upload document");
    }
    return await response.json();
  } catch (error) {
    
    throw error;
  }
};