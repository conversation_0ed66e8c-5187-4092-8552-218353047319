//utils/batchAPI.js
"use client ";

import { getHeaders, getHeadersRaw } from "./api";

let base_url = process.env.NEXT_PUBLIC_API_URL;

const SHOW_NAME = 'batch'

export const submitCodeGenerationJob = async (projectId, architectureId) => {
    const response = await fetch(`${base_url}/${SHOW_NAME}/start_code_generation/${projectId}`, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify({ architecture_id: architectureId }),
    });
  
    if (!response.ok) {
      throw new Error("Failed to submit code generation job");
    }
  
    return await response.json();
  };

  export const submitCodeMaintenanceJob = async (projectId) => {
    const response = await fetch(`${base_url}/${SHOW_NAME}/start_code_maintenance/${projectId}/`, {
      method: "POST",
      headers: await getHeaders(),
    });
  
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || "Failed to submit code maintenance job");
    }
  
    return await response.json();
  };

  export const updateTask = async (taskId, updateData) => {
    try {
      const headers = getHeadersRaw();
      const response = await fetch(`${base_url}/${SHOW_NAME}/update_task/${taskId}`, {
        method: "PATCH",
        headers: new Headers({...getHeadersRaw(), "Bypass-Credit-Check": "true"}),
        body: JSON.stringify(updateData),
      });
  
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to update task");
      }
  
      return await response.json();
    } catch (error) {
      
      throw error;
    }
  };
  
  export const getPastMaintenanceTasks = async (projectId, limit = 10, skip = 0, agentName="CodeMaintenance") => {
    let url = `${base_url}/${SHOW_NAME}/past_maintenance_tasks/${projectId}?limit=${limit}&skip=${skip}&agent=${agentName}`;
  
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
  
    if (!response.ok) {
      throw new Error("Failed to fetch past maintenance tasks");
    }
  
    return await response.json();
  };
  
 

export const createPullRequest = async (taskId, destinationBranch, options = {}) => {
    try {
      const response = await fetch(`${base_url}/${SHOW_NAME}/merge/${taskId}`, {
        method: "POST",
        headers: await getHeaders(),
        body: JSON.stringify({
          destination_branch: destinationBranch,
          ...(options.title && { title: options.title }),
          ...(options.description && { description: options.description })
        }),
      });
  
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to create pull request");
      }
  
      return await response.json();
    } catch (error) {
      
      throw error;
    }
  };

export const retryCodeGeneration = async (taskId) => {
    const response = await fetch(`${base_url}/${SHOW_NAME}/retry_code_generation/${taskId}`, {
      method: "POST",
      headers: await getHeaders(),
    });
  
    if (!response.ok) {
      throw new Error("Failed to retry code generation");
    }
  
    return await response.json();
  };
  
  export const getJobStatus = async (jobId) => {
    const response = await fetch(`${base_url}/${SHOW_NAME}/job_status/${jobId}`, {
      method: "GET",
      headers: await getHeaders(),
    });
  
    if (!response.ok) {
      throw new Error("Failed to get job status");
    }
  
    return await response.json();
  };
  
  export const cancelCodeGenerationTask = async (taskId) => {
    const response = await fetch(`${base_url}/${SHOW_NAME}/cancel_code_generation_job/${taskId}`, {
      method: "POST",
      headers: await getHeaders(),
      
    });
  
    if (!response.ok) {
      throw new Error("Failed to terminate job");
    }
  
    return await response.json();
  };
  
  export const listJobs = async (status = null) => {
    let url = `${base_url}/${SHOW_NAME}/list_jobs`;
    if (status) {
      url += `?status=${status}`;
    }
  
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });
  
    if (!response.ok) {
      
    }
  
    return await response.json();
  };
  


export const controlTask = async (taskId, control, projectId) => {
    const response = await fetch(`${base_url}/${SHOW_NAME}/control/${taskId}?control=${control}&project_id=${projectId}`, {
      method: "PATCH",
      headers: await getHeaders(),
    });
  
    if (!response.ok) {
      throw new Error(`Failed to ${control} task`);
    }
  
    return await response.json();
  };


export const updateUserInput = async (taskId, userInput, fileAttachments=[]) => {
  const response = await fetch(`${base_url}/${SHOW_NAME}/user_input/${taskId}`, {
    method: "PATCH",
    headers: await getHeaders(),
    body: JSON.stringify({ user_input: userInput, file_attachments: fileAttachments }),
  });

  if (!response.ok) {
    throw new Error("Failed to update user input");
  }

  return await response.json();
};

export const getPastCodeGenerationTasks = async (projectId, architectureId = null, limit = 10, skip = 0) => {
  let url = `${base_url}/${SHOW_NAME}/past_code_generation_tasks/${projectId}?limit=${limit}&skip=${skip}`;
  
  if (architectureId !== null) {
    url += `&container_id=${architectureId}`;
  }

  const response = await fetch(url, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Failed to fetch past code generation tasks");
  }

  return await response.json();
};



export const getCallbackState = async (callbackType, taskId) => {
  const response = await fetch(`${base_url}/${SHOW_NAME}/callback_state/${callbackType}/${taskId}`, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Failed to get callback state");
  }

  return await response.json();
};

// Delete a specific maintenance task
export const deleteMaintenanceTask = async(taskId)=> {
  const url = `${base_url}/${SHOW_NAME}/past_maintenance_tasks/${taskId}`;
  
  try {
    const response = await fetch(url, {
      method: 'DELETE',
      headers: await getHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to delete maintenance task');
    }

    return await response.json();
  } catch (error) {
    
    return false;
  }
}

export const retryTask = async (taskId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/retry/${taskId}`, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to retry task");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const downloadRepository = async (jobId, projectId, repoName) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/download-repository/${jobId}?project_id=${projectId}&repo_name=${repoName}`, {
      method: "POST",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to download repository");
    }

    // Handle streaming response by creating a blob and downloading it
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${repoName}.zip`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    return true;
  } catch (error) {
    
    throw error;
  }
};


export const getPastCodeTasks = async (projectId, limit = 10, skip = 0, query = '') => {
  try {
  let url = `${base_url}/${SHOW_NAME}/past_code_tasks/${projectId}?limit=${limit}&skip=${skip}`;
  
    // Add query string if provided
    if (query) {
      url += `&${query}`;
  }

  const response = await fetch(url, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to fetch past code tasks");
  }

  return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const mergeToKaviaMain = async (taskId) => {
  try {
    const response = await fetch(`${base_url}/batch/merge_to_kavia_main/${taskId}`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({}) // Empty body as per the API specification
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Merge to kavia main error:", error);
    throw error;
  }
};

export const getSessionPodStatus = async (sessionId) => {
  try {

    const response = await fetch(`${base_url}/${SHOW_NAME}/session_pod_status/${sessionId}`, {
      method: 'GET',
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Failed to get session pod status. Status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Get session pod status error:", error);
    throw error;
  }
};