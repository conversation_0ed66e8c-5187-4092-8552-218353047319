import { backend_base_url, getHeaders } from "./api";

interface KaviaAIResponse {
  answer: string;
  sources: string[];
  status: string;
}

export interface ChatResponse {
  answer: string;
  sources: string[];
}

export async function askKaviaAI(message: string): Promise<ChatResponse> {
  const res = await fetch(`${backend_base_url}/help_chat/search`, {
    method: "POST",
    headers: await getHeaders(),
    body: JSON.stringify({ query: message }),
  });
  if (!res.ok) {
    throw new Error("Failed to get response from Kavia AI");
  }
  const data: KaviaAIResponse = await res.json();
  return {
    answer: data.answer,
    sources: data.sources,
  };
}
